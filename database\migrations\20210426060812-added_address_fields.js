"use strict";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn(
				"customers",
				"address",
				Sequelize.STRING
			);
			await queryInterface.addColumn("customers", "address1", Sequelize.STRING);
			await queryInterface.addColumn("customers", "address2", Sequelize.STRING);
			await queryInterface.addColumn("customers", "city", Sequelize.STRING);
			await queryInterface.addColumn("customers", "state", Sequelize.STRING);
			await queryInterface.addColumn("customers", "zipCode", Sequelize.STRING);
			await queryInterface.addColumn("customers", "country", Sequelize.STRING);
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},

	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn("customers", "address1");
			await queryInterface.removeColumn("customers", "address2");
			await queryInterface.removeColumn("customers", "city");
			await queryInterface.removeColumn("customers", "state");
			await queryInterface.removeColumn("customers", "zipCode");
			await queryInterface.removeColumn("customers", "country");
			await queryInterface.addColumn("customers", "address", Sequelize.STRING);
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},
};
