"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable("qr_codes", {
      qr_code_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      job_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "shipment_jobs",
          key: "shipment_job_id",
        },
      },
      random_number: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      qr_image: {
        type: Sequelize.TEXT,
        defaultValue: "",
      },
      status: {
        type: Sequelize.ENUM,
        allowNull: true,
        values: ["Active", "Inactive"],
        defaultValue: "Active",
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("qr_codes");
  },
};
