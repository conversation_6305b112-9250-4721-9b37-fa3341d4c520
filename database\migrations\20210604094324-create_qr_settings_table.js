"use strict";
module.exports = {
	up: (queryInterface, Sequelize) => {
		return queryInterface.createTable("qr_settings", {
			qr_setting_id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: Sequelize.INTEGER,
			},
			shipment_job_id: {
				type: Sequelize.INTEGER,
				allowNull: true,
				onDelete: "CASCADE",
				onUpdate: "CASCADE",
				references: {
					model: "shipment_jobs",
					key: "shipment_job_id",
				},
			},
			place: {
				type: Sequelize.TEXT,
			},
			title_flag: {
				type: Sequelize.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			job_number_flag: {
				type: Sequelize.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			company_name_flag: {
				type: Sequelize.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			company_contact_flag: {
				type: Sequelize.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			from_address_flag: {
				type: Sequelize.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			to_address_flag: {
				type: Sequelize.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			sequenced_label_flag: {
				type: Sequelize.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			qr_code_label_flag: {
				type: Sequelize.ENUM("yes", "no"),
				defaultValue: "yes",
			},
		});
	},
	down: (queryInterface, Sequelize) => {
		return queryInterface.dropTable("qr_settings");
	},
};
