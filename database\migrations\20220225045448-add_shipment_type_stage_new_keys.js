"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_type_stages",
        "why_supervisor_signature_require_note",
        {
          type: Sequelize.STRING,
        }
      );
      await queryInterface.addColumn(
        "shipment_type_stages",
        "why_customer_signature_require_note",
        {
          type: Sequelize.STRING,
        }
      );
      await queryInterface.addColumn(
        "shipment_type_stages",
        "supervisor_signature_require_note_by_user",
        {
          type: Sequelize.STRING,
        }
      );
      await queryInterface.addColumn(
        "shipment_type_stages",
        "customer_signature_require_note_by_user",
        {
          type: Sequelize.STRING,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_type_stages", "why_supervisor_signature_require_note");
      await queryInterface.removeColumn("shipment_type_stages", "why_customer_signature_require_note");
      await queryInterface.removeColumn("shipment_type_stages", "supervisor_signature_require_note_by_user");
      await queryInterface.removeColumn("shipment_type_stages", "customer_signature_require_note_by_user");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
