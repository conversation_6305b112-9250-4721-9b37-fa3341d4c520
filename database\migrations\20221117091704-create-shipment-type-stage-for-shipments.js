'use strict';
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('shipment_type_stage_for_shipments', {
      local_shipment_stage_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      local_shipment_type_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "shipment_type_for_shipments",
          key: "local_shipment_type_id",
        },
      },
      ref_shipment_stage_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      name: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      order_of_stages: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      status: {
        type: Sequelize.ENUM("inactive", "active"),
        defaultValue: "active",
      },
      scan_require: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      is_add_item: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      supervisor_signature_require: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      customer_signature_require: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      why_supervisor_signature_require_note: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      why_customer_signature_require_note: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      supervisor_signature_require_note_by_user: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      customer_signature_require_note_by_user: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      supervisor_signature_require_at_origin_to_all_pages: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      supervisor_signature_require_at_destination_to_all_pages: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      customer_signature_require_at_origin_to_all_pages: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      customer_signature_require_at_destination_to_all_pages: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('shipment_type_stage_for_shipments');
  }
};
