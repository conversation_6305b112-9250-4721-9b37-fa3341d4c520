'use strict';
module.exports = (sequelize, DataTypes) => {
  const shipment_inventory_comments = sequelize.define(
    'shipment_inventory_comments',

    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      shipment_inventory_id: DataTypes.INTEGER,
      comment: DataTypes.STRING,
      created_at: {
        type: DataTypes.DATE,
      },
      updated_at: {
        type: DataTypes.DATE,
      },

    },
    { createdAt: false, updatedAt: false }
  );
  shipment_inventory_comments.associate = function (models) {

    shipment_inventory_comments.belongsTo(models.shipment_inventory, {
      as:"shipment_inventory",
			foreignKey: "shipment_inventory_id",
		});
  };

  return shipment_inventory_comments;
};