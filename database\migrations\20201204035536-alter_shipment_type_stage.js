"use strict";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.addColumn("shipment_type_stages", "scan_require", {
				type: Sequelize.BOOLEAN,
			});
			await queryInterface.addColumn("shipment_type_stages", "supervisor_signature_require", {
				type: Sequelize.BOOLEAN,
			});
			await queryInterface.addColumn("shipment_type_stages", "customer_signature_require", {
				type: Sequelize.BOOLEAN,
			});
			// await queryInterface.removeColumn(
			//   "shipment_type_stages",
			//   "signature_require"
			// );
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},

	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn("shipment_type_stages", "scan_require");
			await queryInterface.removeColumn("shipment_type_stages", "supervisor_signature_require");
			await queryInterface.removeColumn("shipment_type_stages", "customer_signature_require");
			// await queryInterface.addColumn(
			//   "shipment_type_stages",
			//   "signature_require",
			//   {
			//     type: Sequelize.BOOLEAN,
			//   }
			// );
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},
};
