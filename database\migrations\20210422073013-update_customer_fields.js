"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn(
        "customers",
        "customer_name",
        Sequelize.STRING
      );
      await queryInterface.addColumn(
        "customers",
        "first_name",
        Sequelize.STRING
      );
      await queryInterface.addColumn(
        "customers",
        "last_name",
        Sequelize.STRING
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("customers", "first_name");
      await queryInterface.removeColumn("customers", "last_name");
      await queryInterface.addColumn(
        "customers",
        "customer_name",
        Sequelize.STRING(50)
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
