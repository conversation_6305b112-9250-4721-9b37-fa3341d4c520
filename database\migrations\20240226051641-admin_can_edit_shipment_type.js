'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'staffs',
        'is_admin_can_edit_shipment_type',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('staffs', 'is_admin_can_edit_shipment_type');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
