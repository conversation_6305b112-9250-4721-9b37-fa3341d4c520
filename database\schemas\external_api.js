"use strict";
let bcrypt = require("bcrypt");
module.exports = (sequelize, DataTypes) => {
	const external_api = sequelize.define(
		"external_api",
		{
			id: {
				autoIncrement: true,
				primaryKey: true,
				type: DataTypes.INTEGER,
			},
			email: {
				type: DataTypes.STRING,
				unique: true,
			},
			password: {
				type: DataTypes.STRING,
			},
			type: {
				type: DataTypes.STRING,
			},
			from: {
				type: DataTypes.STRING,
			},
			apiKey: {
				type: DataTypes.STRING,
			},
			status: {
				type: DataTypes.ENUM,
				values: ["Active", "Inactive"],
				defaultValue: "Active",
			},
			expire: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{
			createdAt: false,
			updatedAt: false,
			indexes: [
				{
					fields: ["email"],
					unique: true,
				},
			],
			hooks: {
				beforeCreate: (user) => {
					const salt = bcrypt.genSaltSync(10);
					user.password = bcrypt.hashSync(user.password, salt);
				},
			},
		}
	);

	external_api.associate = function (models) {
		// associations can be defined here
	};
	return external_api;
};
