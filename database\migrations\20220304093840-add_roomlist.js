"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_rooms",
        "admin_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );

      await queryInterface.addColumn(
        "shipment_rooms",
        "company_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );

      await queryInterface.addColumn(
        "shipment_rooms",
        "staff_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );
   
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_rooms", "company_id");
      await queryInterface.removeColumn("shipment_rooms", "admin_id");
      await queryInterface.removeColumn("shipment_rooms", "staff_id");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
