FROM node:14-alpine as build
WORKDIR /app
RUN apk add python3 build-base
COPY package.json .
RUN npm install

FROM node:14-alpine as final
ARG APP_NAME=app
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
ENV APP_NAME=${APP_NAME}
RUN npm install -g pm2
WORKDIR /app
COPY --from=build /app/node_modules /app/node_modules
COPY .env .env
COPY . .
RUN npx sequelize-cli db:migrate || exit 0
CMD pm2-runtime start server.js --name ${APP_NAME}
