const {
  shipment_job,
  shipment_inventory,
  shipment_room,
  qr_code,
  sequelize,
  staff,
  shipment_inventory_photo,
} = require("../../database/schemas");

exports.labelExitsCheckModel = async (jobId, isManualLabelNumber) => {
  let check = await shipment_inventory.findOne({
    where: {
      shipment_job_id: jobId,
      label_no: isManualLabelNumber
    }
  })
  return check;
}

exports.lotNumberExitsCheck = async (jobId, isManualLabelNumber, lot_number) => {
  let check = await shipment_inventory.findOne({
    where: {
      shipment_job_id: jobId,
      label_no: isManualLabelNumber,
      lot_no: lot_number
    }
  })
  return check;
}


exports.listInventory = async (req) => {
  let shipment_jobs = await shipment_inventory.findAll({
    where: { shipment_job_id: req.body.id },
    attributes: [...COMMON_INVENTORY_ATTRIBUTES],
    include: [
      {
        model: shipment_job,
        attributes: ["total_items"],
      },
      {
        model: shipment_room,
        as: "room",
        attributes: ["name"],
      },
      {
        model: qr_code,
        as: "item_qr",
        attributes: ["random_number"],
      },
      {
        model: staff,
        as: "prepared_staff",
        attributes: ["first_name", "last_name"],
      },
      {
        model: staff,
        as: "carrier_packed",
        attributes: ["first_name", "last_name"],
      },
      {
        model: staff,
        as: "disassembled_user",
        attributes: ["first_name", "last_name"],
      },
      {
        model: shipment_inventory_photo,
        attributes: [
          ["shipment_inventory_photo_id", "photo_id"],
          "media",
          [
            sequelize.literal(
              `(CASE WHEN media IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE_Job_Item}', shipment_inventory.shipment_job_id, '/original/', media) END)`
            ),
            "item_photo",
          ],
        ],
        required: false,
        as: "item_photos",
      },
    ],
  });
  return shipment_jobs;
};
