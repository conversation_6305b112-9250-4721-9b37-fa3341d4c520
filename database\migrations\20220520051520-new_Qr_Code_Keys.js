"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "qr_codes",
        "type",
        {
          type: Sequelize.ENUM,
          allowNull: true,
          values: ["Shipment", "Generic", "External"],
          defaultValue: "Shipment",
        }
      );
      await queryInterface.addColumn(
        "qr_codes",
        "company_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          onDelete: "CASCADE",
          onUpdate: "CASCADE",
          references: {
            model: "companies",
            key: "company_id",
          },
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("qr_codes", "type");
      await queryInterface.removeColumn("qr_codes", "company_id");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
