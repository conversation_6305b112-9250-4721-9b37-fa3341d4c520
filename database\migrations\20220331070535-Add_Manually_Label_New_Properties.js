"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_inventories",
        "isManualLabel",
        {
          type: Sequelize.TINYINT,
          defaultValue: 0,
        }
      );
      await queryInterface.addColumn(
        "shipment_inventories",
        "label_no",
        {
          type: Sequelize.STRING,
        }
      );
      await queryInterface.addColumn(
        "shipment_inventories",
        "lot_no",
        {
          type: Sequelize.STRING,
        }
      );
      await queryInterface.addColumn(
        "shipment_inventories",
        "color",
        {
          type: Sequelize.STRING,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_inventories", "isManualLabel");
      await queryInterface.removeColumn("shipment_inventories", "label_no");
      await queryInterface.removeColumn("shipment_inventories", "lot_no");
      await queryInterface.removeColumn("shipment_inventories", "color");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
