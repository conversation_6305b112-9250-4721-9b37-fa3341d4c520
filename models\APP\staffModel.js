const {
	staff,
	accesstoken,
	company,
	sequelize,
} = require("../../database/schemas");
const { Op } = require("sequelize");
const bcrypt = require("bcrypt");
const commonFunction = require("../../assets/common");

exports.userDetailByCompanyId = async (body) => {
	return await company.findOne({
		where: {
			company_identity: body.company_id
		}
	})
}

exports.getStaffDetailsForCompnay = async (staffId) => {
	return await staff.findOne({
		where: {
			staff_id: staffId,
		},
	});
}

exports.accesstokenInsert = async (staff_id, request) => {
	let newToken = await commonFunction.jwtTokenGenerate(staff_id);
	const data = await accesstoken.create(
		{
			staff_id: staff_id,
			access_token: newToken,
			device_token: request.device_token,
			device_type: request.device_type,
			version: request.version,
		}
	);
	return data;
};

exports.signIn = async (request) => {
	return await staff.findOne({
		where: {
			email: request.email,
			is_deleted: "0",
		},
		raw: true,
	});
};


exports.companyIdUser = async (request) => {
	return await staff.findOne({
		where: {
			email: request.email,
			is_deleted: "0",
			status: "active"
		},
		raw: true,
	});
};

exports.signInWithCompanyId = async (request, companyData) => {
	return await staff.findOne({
		where: {
			email: request.email,
			company_id: companyData.company_id,
			is_deleted: "0",
			status: "active"
		},
		raw: true,
	});
};

exports.getStaffCompanyId = async (staffId) => {
	return await staff.findOne({
		where: {
			staff_id: staffId
		}
	})
}

exports.getCompanyIdentity = async (company_id) => {
	return await company.findOne({
		attributes: ["company_identity", "status", "email", "password", "group_id"],
		where: {
			company_id: company_id,
			is_deleted: 0
		},
		raw: true,
	});
};

exports.getCompanyIdentitySuperAdmin = async (company_id) => {
	return await company.findOne({
		attributes: ["company_identity", "status", "email", "password", "group_id"],
		where: {
			company_identity: company_id,
			is_deleted: 0
		},
		raw: true,
	});
};


exports.getPassword = async (staff_id) => {
	return await staff.findOne({
		where: {
			staff_id: staff_id,
		},
		attributes: ["password", "email"],
		raw: true,
	});
};

exports.findCompanyByEmail = (email) =>
	company.findOne({
		where: {
			email: email,
			is_deleted: "0"
		},
	});

exports.updatePasswordCompany = async (request, email) => {
	const salt = bcrypt.genSaltSync(10);
	let obj = {
		password: bcrypt.hashSync(request.new_password, salt),
	};
	return await company.update(obj, {
		where: {
			email: email,
		},
	});
};


exports.staffEditPassword = async (request, staff_id) => {
	const salt = bcrypt.genSaltSync(10);
	let pwd = bcrypt.hashSync(request.body.new_password, salt);
	let updateData = {
		password: pwd,
	};
	const data = await staff.update(updateData, {
		where: { staff_id: staff_id },
	});
	return data;
};

exports.staffEditPasswordStorage = async (new_password, staff_id) => {
	const salt = bcrypt.genSaltSync(10);
	let pwd = bcrypt.hashSync(new_password, salt);
	let updateData = {
		password: pwd,
	};
	const data = await staff.update(updateData, {
		where: { storage_staff_id: staff_id },
	});
	return data;
};

exports.deleteAccessTokens = async (request, staff_id) => {
	const deleteData = await accesstoken.destroy({
		where: {
			staff_id: staff_id,
			access_token: { [Op.ne]: request.headers.access_token },
		},
	});
	return deleteData;
};

exports.deleteAccessToken = async (request) => {
	const deleteData = await accesstoken.destroy({
		where: { access_token: request.headers.access_token },
	});

	return deleteData;
};

exports.getStaffProfile = async (staff_id) => {
	return await staff.findOne({
		where: {
			staff_id: staff_id,
		},
		attributes: [
			...COMMON_STAFF_ATTRIBUTES,
			[
				sequelize.literal(
					`(CASE WHEN photo IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE_Staff_Profile}', 'original/', photo) END)`
				),
				"profile_pic",
			],
		],
		raw: true,
	});
};

exports.updateVerificationToken = async (email) => {
	let newVerificationToken = 100000 + Math.floor(Math.random() * 900000);
	let updateData = {
		verification_code: newVerificationToken,
	};
	const data = await staff.update(updateData, { where: { email: email } });
	return newVerificationToken;
};

exports.checkExistingEmail = async (email) => {
	return await staff.count({
		where: {
			email: email.toLowerCase(),
		},
	});
};
