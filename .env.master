APP_PORT = 8421
DB_PORT = 3306
DB_HOST = mover-inventory.cosfriv88fmg.us-east-1.rds.amazonaws.com
DB_USER = mover_inventory
DB_PASS = iCDvp287Hsm9rIwA2aAdcpda
MYSQL_DB = mover_inventory
AWS_ACCESS_KEY_ID = ********************
AWS_SECRET_ACCESS_KEY = TP5ji8IK0pSQnQBfkPWPdsDmmJN/XqiSlE8IE4ww
AWS_BUCKET = mover-inventory-prod-public
AWS_BASEURL = https://mover-inventory-prod-public.s3.amazonaws.com/
EMAIL_ID = <EMAIL>
EMAIL_PASSWORD = nhqpnhfcfryuaipv
NODE_ENV = production
CMS_URL = https://moverinventory-cms.movegistics.com/mover-inventory-cms/
SUPER_ADMIN_COMPANY_ID = -1
JSREPORT_URL =  http://***********:5488/
JSREPORT_ADMIN = admin
JSREPORT_PASSWORD = oFQKJ8s33Qo9
MOVER_STORAGE_API_URL= https://moverstorage.movegistics.com/api/
MOVEGISTICS_STORAGE_API_URL = https://96bzov3qgf.execute-api.ap-south-1.amazonaws.com/dev/movegistics/api/
GOOGLE_API_KEY = AIzaSyB-yGpC4YxIsgGFtmWp19CAJkza_XVFpN4

