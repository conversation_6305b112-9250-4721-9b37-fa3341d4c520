const express = require("express");
const router = express();
const generalController = require("../controllers/APP/generalController");
const authentication = require("../middlewares/authentication");
const validators = require("../assets/validators");
const homeController = require("../controllers/APP/homeController");
const multer = require("multer");
const { errorHandler } = require("../assets/common");
const customerController = require("../controllers/Admin/customerController");
const upload = multer({ dest: "temp/" });

/**
* @desc Api for fetch static page.
* @method POST
*/

router
	.route("/get-static-page")
	.post(
		upload.none(),
		validators.app.getStaticPage,
		errorHandler,
		generalController.getStaticPage
	);

/**
* @desc Api for caontact us.
* @method POST
*/

router
	.route("/contact-us")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.app.contactUs,
		errorHandler,
		generalController.contactUs
	);

/**
* @desc Api for generate feedback.
* @method POST
*/

router
	.route("/feedback")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.app.feedback,
		errorHandler,
		generalController.feedback
	);

/**
* @desc Api for customer signIn.
* @method POST
*/

router
	.route("/customer/sign_in")
	.post(
		upload.none(),
		validators.admin.adminSignIn,
		errorHandler,
		customerController.isValidCustomerController,
		customerController.signIn
	);

/**
* @desc Api for customer check pass validity.
* @method POST
*/

router
	.route("/customer/check_pass_validity")
	.post(
		upload.none(),
		validators.admin.viewCustomerEmail,
		errorHandler,
		customerController.isValidActiveCustomerController,
		customerController.isValidCustomerController,
		customerController.isSignUpController
	);

/**
* @desc Api for customer set password.
* @method POST
*/

router
	.route("/customer/set_password")
	.post(
		upload.none(),
		validators.admin.viewCustomerEmail,
		validators.admin.isPassword,
		errorHandler,
		customerController.isValidCustomerController,
		customerController.alterCustomerPasswordController
	);

/**
* @desc Api for customer send mail forgot password.
* @method POST
*/

router
	.route("/customer/send_mail_forgot_password")
	.post(
		upload.none(),
		validators.admin.viewCustomerEmail,
		errorHandler,
		customerController.isValidCustomerController,
		customerController.isforgotPasswordController
	);

/**
* @desc Api for customer forgot password.
* @method POST
*/

router
	.route("/customer/forgot_password")
	.post(
		upload.none(),
		validators.admin.viewCustomerEmail,
		validators.admin.isPassword,
		errorHandler,
		customerController.isValidCustomerController,
		customerController.changeCustomerPasswordController
	);

/**
* @desc Api for customer inventory using inventoryId.
* @method POST
*/

router
	.route("/customer/inventory/:inventoryId")
	.get(
		validators.admin.inventoryDetail,
		errorHandler,
		homeController.isValidInventoryController,
		homeController.getInventoryDetailController
	);

/**
* @desc Api for generate comments customer inventory.
* @method POST
*/

router
	.route("/customer/inventory/add-comment")
	.post(
		validators.admin.addCommentToInventory,
		errorHandler,
		homeController.isCheckInventoryController,
		homeController.addInventoryCommentController
	);

/**
* @desc Api for fetch comments using inventory.
* @method POST
*/

router
	.route("/customer/inventory/fetch-comments/:inventoryId")
	.get(
		validators.admin.inventoryDetail,
		errorHandler,
		homeController.isValidInventoryController,
		homeController.fetchInventoryCommentController
	);

/**
* @desc Api for fetch comments using inventory.
* @method POST
*/

router
	.route("/send/request")
	.post(upload.none(),
		validators.admin.sendRequest,
		errorHandler,
		generalController.sendRequestMail
	);

module.exports = router;
