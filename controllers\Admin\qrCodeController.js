const {
	generateResponse,
	randomString,
	deleteImageS3,
	UploadImageS3,
} = require("../../assets/common");
const axios = require("axios");
const AWS = require("aws-sdk");
const fs = require("fs");
const qrCodeModelInstance = require("../../models/Admin/qrCodeModel");
const inventoryModel = require("../../models/Admin/inventoryModel");
const QRCode = require("qrcode");
const { PDFDocument, StandardFonts } = require("pdf-lib");
const charsetOptions = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
const s3 = new AWS.S3();

exports.listQrCodeController = async (request, response) => {
	try {
		const { jobId } = request.params;
		const qrCodeList = await qrCodeModelInstance.listQrCodeModel(jobId, request.query);
		generateResponse(response, SUCCESS_CODE, 1, GENERIC_REQUEST_SUCCESS, qrCodeList);
	} catch (error) {
		console.log("exports.listQrCode -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};

exports.listDymoQrCodeController = async (request, response) => {
	try {
		const { jobId } = request.params;
		const qrCodeList = await qrCodeModelInstance.listDymoQrCodeModel(jobId, request.query, request.body);
		generateResponse(response, SUCCESS_CODE, 1, GENERIC_REQUEST_SUCCESS, qrCodeList);
	} catch (error) {
		console.log("exports.listQrCode -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};

exports.qrListForAppApi = async (request, response) => {
	try {
		const { jobId } = request.params;
		const qrCodeList = await qrCodeModelInstance.qrListForAppApi(jobId, request.query);
		generateResponse(response, SUCCESS_CODE, 1, GENERIC_REQUEST_SUCCESS, qrCodeList);
	} catch (error) {
		console.log("exports.listQrCode -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};

exports.checksingalQrCodeExistence = async (request, response, next) => {
	try {
		const { qrId } = request.params;
		const isValidQrCode = await qrCodeModelInstance.checksingalQrCodeExistence(qrId);
		if (isValidQrCode) {
			next();
		} else {
			generateResponse(response, NOT_FOUND_CODE, 0, "", {});
		}
	}
	catch (error) {
		console.log("exports.checksingalQrCodeExistence -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
}

exports.findQrSettingsController = async (request, response) => {
	try {
		const { jobId } = request.params;
		const found = await qrCodeModelInstance.findQrSetting(jobId);
		if (found !== null) {
			generateResponse(response, SUCCESS_CODE, 1, "Settings found", found);
		} else {
			generateResponse(response, NOT_FOUND_CODE, 0, "", {});
		}
	} catch (error) {
		console.log("exports.findQrSettingsController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};

exports.findQrFirstAndLastLabelController = async (request, response) => {
	try {
		const { jobId } = request.params;
		let labelData = {};
		const findFirstLabel = await qrCodeModelInstance.findJobFirstLabel(jobId)
		let findLastLabel = await qrCodeModelInstance.findJobLastLabel(jobId);
		labelData["findFirstLabel"] = findFirstLabel;
		labelData["findLastLabel"] = findLastLabel;
		generateResponse(response, SUCCESS_CODE, 1, "Qr first and last label", labelData);

	} catch (error) {
		console.log("exports.findQrFirstAndLastLabelController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
}


exports.saveQrSettings = async (request, response) => {
	try {
		const { jobId } = request.params;
		const found = await qrCodeModelInstance.findQrSetting(jobId);

		if (found === null) {
			const qrSetting = await qrCodeModelInstance.saveQrSettings(jobId, request.body);
			generateResponse(response, SUCCESS_CODE, 1, "Settings Saved", {});
		} else {
			const qrSetting = await qrCodeModelInstance.updateQrSettings(jobId, request.body);

			generateResponse(response, SUCCESS_CODE, 1, "Settings Updated", {});
		}
	} catch (error) {
		console.log("exports.saveQrSettings -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};


exports.generateQrController = async (request, response) => {
	try {
		request.setTimeout(0);
		const { quantity, jobId, type } = request.body;
		let qrCodeBulk = [];
		let { label_number } = await qrCodeModelInstance.findLastLabel(jobId);

		for (let qrCodeString = 0; qrCodeString < quantity; qrCodeString++) {
			let qrString = randomString(10, charsetOptions);
			let qr_number = label_number + qrCodeString + 1;
			const qrImageFileName = await generateAndUploadLabel(qrString, jobId);
			if (qrImageFileName) {
				qrCodeBulk.push({
					random_number: qrString,
					qr_image: qrImageFileName,
					job_id: jobId,
					label_number: qr_number,
				});
			}
		}
		const modelResponse = await qrCodeModelInstance
			.generateQrModel(qrCodeBulk);
		generateResponse(response, CREATED_CODE, 1, "QR/Label created!", {});

	} catch (error) {
		console.log("exports.generateQrController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};

exports.deleteQrController = async (request, response) => {
	try {
		const { qrCodeId } = request.params;
		//newChanges
		const modelResponse = await qrCodeModelInstance.getQrModel(qrCodeId);
		if (modelResponse) {
			try {
				const bucketResponse = await removeQrLabel(modelResponse);
				if (bucketResponse) {
					const removeModelResponse = await qrCodeModelInstance.removeQrModel(qrCodeId);
					if (removeModelResponse) {

						generateResponse(response, NO_CONTENT_CODE, 1, QR_DELETED, removeModelResponse)
					}
				}
				else generateResponse(response, GENERIC_REQUEST_FAILURE, 0, bucketResponse, {});
			}
			catch (err) {
				generateResponse(response, SERVER_ERROR_CODE, 0, err, {})
			}
		} else {
			generateResponse(response, NO_CONTENT_CODE, 0, QR_NOT_AVAILABLE, {});
		}
	}
	catch (error) {
		console.log("exports.deleteQrController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {})
	}
}

const removeQrLabel = async (modelResponse) => {
	if (modelResponse.qr_image) {
		let delMedia = {
			Bucket: Const_AWS_BUCKET,
			Key: modelResponse.qr_image,
		};
		return deleteImageS3(delMedia, s3);
	} else {
		return false;
	}
};

const generateAndUploadLabel = async (qrCodeString, jobId) => {
	let qrImageFileName = qrCodeString + `_` + jobId + `.png`;
	let qrFilePath = `/tmp/qrCodes/${jobId}/${qrImageFileName}`;
	let serverUploadPath = `${QR_UPLOAD_PATH}/${jobId}/original/${qrImageFileName}`;

	let baseFolderPath = `/tmp/qrCodes/${jobId}`;
	if (!fs.existsSync(baseFolderPath)) fs.mkdirSync(`/tmp/qrCodes/${jobId}`, { recursive: true });

	await QRCode.toFile(`${qrFilePath}`, qrCodeString).catch((err) => console.log(err));

	// Moving photos to s3 bucket
	let bucketObject = {
		ACL: "public-read",
		Bucket: Const_AWS_BUCKET,
		Body: fs.createReadStream(qrFilePath),
		Key: `${serverUploadPath}`,
	};

	// uploading and removing files from temp folder
	let result = await UploadImageS3(bucketObject, s3);
	if (result) {
		return serverUploadPath;
	}
};

exports.singalQrPrintLabelController = async (request, response) => {
	let { qrId } = request.params;
	let {
		title_flag,
		company_name_flag,
		company_contact_flag,
		from_address_flag,
		to_address_flag,
		qr_code_label_flag,
		sequenced_label_flag,
		job_number_flag,
		external_reference_flag,
		jobId
	} = request.body;

	let pdfDoc = await PDFDocument.create();
	let HelveticaBold = await pdfDoc.embedFont(StandardFonts.TimesRomanBold);
	let page = pdfDoc.addPage();
	let rowShiftBy = 10;
	let columnShiftBy = 0;
	let howBroad = 0;
	let qrPerRow = 2;
	let qrPerColumn = 6;
	let isHeightLimit = 0;

	const qrCodeStrings = await qrCodeModelInstance.getQrCodeDetailsLinksModel(jobId, qrId);
	let place = await qrCodeStrings[0].shipment_job.shipment_name;
	// const newArray = qrCodeStrings.flatMap(i => [i, i]);
	for (let [isWidthLimit, qrCodeContent] of qrCodeStrings.entries()) {
		let qrStatus = new Promise(async (resolve, reject) => {
			let qrImageFileName = qrCodeContent.random_number + `_` + jobId + `.png`;
			let qrFilePath = `/tmp/qrCodes/${jobId}/${qrImageFileName}`;
			if (fs.existsSync(qrFilePath)) {
				let responseQR = fs.readFileSync(qrFilePath);
				resolve(responseQR);
			} else {
				let image = await axios.default(Const_AWS_BASE + qrCodeContent.qr_image, {
					responseType: "arraybuffer",
				});
				resolve(image.data);
			}
		});

		await qrStatus
			.then(async (qrContent) => {
				const pngImage = await pdfDoc.embedPng(qrContent);
				const pngDims = pngImage.scale(0.50);
				const baseMargin = 10;

				if (isWidthLimit % qrPerRow === 0) {
					isHeightLimit += 1;
					rowShiftBy += 6.25 + page.getHeight() / qrPerColumn;
					columnShiftBy = 1 + page.getWidth() / qrPerRow + 0.15;
					howBroad = -0.03;
				} else {
					howBroad += 0.97;
				}
				if (isHeightLimit === qrPerColumn) {
					page = pdfDoc.addPage();
					rowShiftBy = page.getHeight() / qrPerColumn + 10;
					isHeightLimit = 1;
					howBroad = 0;
				}
				let xPosition = baseMargin + page.getWidth() / 20 + howBroad * columnShiftBy;
				let yPosition = page.getHeight() - rowShiftBy - 10;

				const jobDetails = qrCodeContent.shipment_job;
				const companyDetails = jobDetails.job_company ? jobDetails.job_company : false;
				const randomNumber = qrCodeContent.random_number;
				const labelNumber = qrCodeContent.label_number;
				const externalReference = `CID: ${qrCodeContent &&
					qrCodeContent.shipment_job &&
					qrCodeContent.shipment_job.external_reference !== "undefined" &&
					qrCodeContent.shipment_job.external_reference !== "null" &&
					qrCodeContent.shipment_job.external_reference
					}`;


				const jobInfo = `Job: ${jobDetails.job_number}`;
				place = `${place ? place : "Final Mile, Paso"}`;
				const companyName = `${companyDetails ? companyDetails.company_name : "Mover Inventory"}`;
				const contactNumber = `${companyDetails.phone ? companyDetails.phone : "1234567890"}`;

				const fromAddress = `From: ${jobDetails.pickup_address &&
					jobDetails.pickup_address !== "undefined" &&
					jobDetails.pickup_address !== "null"
					? jobDetails.pickup_address
					: ""
					} ${jobDetails.pickup_address2 &&
						jobDetails.pickup_address2 !== "undefined" &&
						jobDetails.pickup_address2 !== "null"
						? jobDetails.pickup_address2
						: ""
					}`;
				const fromAddress1 = `${jobDetails.pickup_city &&
					jobDetails.pickup_city !== "undefined" &&
					jobDetails.pickup_city !== "null"
					? jobDetails.pickup_city
					: ""
					} ${jobDetails.pickup_state &&
						jobDetails.pickup_state !== "undefined" &&
						jobDetails.pickup_state !== "null"
						? jobDetails.pickup_state
						: ""
					}${jobDetails.pickup_zipcode &&
						jobDetails.pickup_zipcode !== "undefined" &&
						jobDetails.pickup_zipcode !== "null"
						? ", " + jobDetails.pickup_zipcode
						: ""
					}`;
				const fromAddress2 = `${jobDetails.pickup_country &&
					jobDetails.pickup_country !== "undefined" &&
					jobDetails.pickup_country !== "null"
					? jobDetails.pickup_country
					: ""
					}`;

				const toAddress = `To: ${jobDetails.delivery_address &&
					jobDetails.delivery_address !== "undefined" &&
					jobDetails.delivery_address !== "null"
					? jobDetails.delivery_address
					: ""
					} ${jobDetails.delivery_address2 &&
						jobDetails.delivery_address2 !== "undefined" &&
						jobDetails.delivery_address2 !== "null"
						? jobDetails.delivery_address2
						: ""
					}`;
				const toAddress1 = `${jobDetails.delivery_city &&
					jobDetails.delivery_city !== "undefined" &&
					jobDetails.delivery_city !== "null"
					? jobDetails.delivery_city
					: ""
					} ${jobDetails.delivery_state &&
						jobDetails.delivery_state !== "undefined" &&
						jobDetails.delivery_state !== "null"
						? jobDetails.delivery_state
						: ""
					}${jobDetails.delivery_zipcode &&
						jobDetails.delivery_zipcode !== "undefined" &&
						jobDetails.delivery_zipcode !== "null"
						? ", " + jobDetails.delivery_zipcode
						: ""
					}`;
				const toAddress2 = `${jobDetails.delivery_country &&
					jobDetails.delivery_country !== "undefined" &&
					jobDetails.delivery_country !== "null"
					? jobDetails.delivery_country
					: ""
					}`;

				const smallTextSize = 8;
				const bigTextSize = 12;
				const sideMomentQR = 80;
				let labelPosition = 65;

				const labelMeta = () => {
					const _ = {
						x: xPosition + sideMomentQR,
						y: yPosition + labelPosition,
						size: smallTextSize,
					};
					labelPosition -= 10.6;
					return _;
				};

				if (place.length <= 40) {
					if (title_flag)
						page.drawText(place, {
							x: xPosition + 10,
							y: yPosition + 81,
							size: bigTextSize - (place.length < 32 ? 0 : bigTextSize / place.length),
							font: HelveticaBold,
						});
				} else {
					const newWord = chunkSubstr(place, 40);
					function chunkSubstr(str, size) {
						const numChunks = Math.ceil(str.length / size);
						const chunks = new Array(numChunks);

						for (let i = 0, o = 0; i < numChunks; ++i, o += size) {
							chunks[i] = str.substr(o, size);
						}

						return chunks;
					}
					if (title_flag)
						page.drawText(newWord[1], {
							x: xPosition + 10,
							y: yPosition + 81,
							size: bigTextSize - (newWord[1].length < 32 ? 0 : bigTextSize / newWord[1].length),
							font: HelveticaBold,
						});
					if (title_flag)
						page.drawText(newWord[0], {
							x: xPosition + 10,
							y: yPosition + 93,
							size: bigTextSize - (newWord[0].length < 32 ? 0 : bigTextSize / newWord[0].length),
							font: HelveticaBold,
						});
				}

				if (job_number_flag) page.drawText(jobInfo, labelMeta());

				if (company_name_flag) page.drawText(companyName, labelMeta());

				if (company_contact_flag) page.drawText(contactNumber, labelMeta());

				if (external_reference_flag) page.drawText(externalReference, labelMeta());


				if (from_address_flag) {
					page.drawText(fromAddress, {
						...labelMeta(),
						font: HelveticaBold,
					});
					jobDetails.pickup_city !== "" &&
						jobDetails.pickup_city !== "undefined" &&
						jobDetails.pickup_city !== "null" &&
						jobDetails.pickup_state !== "" &&
						jobDetails.pickup_state !== "undefined" &&
						jobDetails.pickup_state !== "null" &&
						jobDetails.pickup_zipcode !== "" &&
						jobDetails.pickup_zipcode !== "undefined" &&
						jobDetails.pickup_zipcode !== "null"
						? page.drawText(fromAddress1, {
							...labelMeta(),
							font: HelveticaBold,
						})
						: "";

					jobDetails.pickup_country !== "" &&
						jobDetails.pickup_country !== "undefined" &&
						jobDetails.pickup_country !== "null"
						? page.drawText(fromAddress2, {
							...labelMeta(),
							font: HelveticaBold,
						})
						: "";
				}

				if (to_address_flag) {
					page.drawText(toAddress, {
						...labelMeta(),
						font: HelveticaBold,
					});
					page.drawText(toAddress1, {
						...labelMeta(),
						font: HelveticaBold,
					});
					page.drawText(toAddress2, {
						...labelMeta(),
						font: HelveticaBold,
					});
				}


				page.drawImage(pngImage, {
					x: xPosition + 15,
					y: yPosition + 8,
					...pngDims,
				});

				if (qr_code_label_flag)
					page.drawText(randomNumber, {
						x: xPosition + 10,
						y: yPosition,
						size: randomNumber.length > 7 ? 10 : 13,
						font: HelveticaBold,
					});

				if (sequenced_label_flag)
					page.drawText(labelNumber, {
						x: xPosition + 20,
						y: yPosition + 64,
						size: 13,
						font: HelveticaBold,
					});

			})
			.catch((err) => console.log(err));
	}
	try {
		pdfDoc
			.save()
			.then((result) => {
				let baseFolderPath = `/tmp/qrPdf/${jobId}`;
				// let qrPdfPath = `${baseFolderPath}/${jobId}_${date_ob.toISOString()}.pdf`;
				let qrPdfPath = `${baseFolderPath}/tempFile.pdf`;
				if (!fs.existsSync(baseFolderPath)) fs.mkdirSync(`${baseFolderPath}`, { recursive: true });

				if (result) {
					fs.writeFileSync(qrPdfPath, result);

					fs.readFile(qrPdfPath, (err, fileData) => {
						if (!err) {
							response.status(CREATED_CODE);
							response.contentType("application/pdf");
							response.send(fileData);
							console.log("pdfSaved");
							return;
						} else {
							generateResponse(response, SERVER_ERROR_CODE, 0, GENERIC_REQUEST_FAILURE, {});
						}
					});
				} else {
					generateResponse(response, NO_CONTENT_CODE, 0, GENERIC_REQUEST_FAILURE, {});
				}
			})
			.catch((err) => generateResponse(response, SERVER_ERROR_CODE, 0, err, {}));
	} catch (error) {
		console.log("exports.generateAndUploadLabel -> error: ", error);

		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.printLabelController = async (request, response) => {
	request.connection.setTimeout(1000000000000000)
	let { jobId } = request.params;
	let {
		place,
		title_flag,
		company_name_flag,
		company_contact_flag,
		from_address_flag,
		to_address_flag,
		qr_code_label_flag,
		sequenced_label_flag,
		job_number_flag,
		external_reference_flag,
	} = request.body;

	let pdfDoc = await PDFDocument.create();
	let HelveticaBold = await pdfDoc.embedFont(StandardFonts.TimesRomanBold);
	let page = pdfDoc.addPage();
	let rowShiftBy = 10;
	let columnShiftBy = 0;
	let howBroad = 0;
	let qrPerRow = 2;
	let qrPerColumn = 6;
	let isHeightLimit = 0;

	const qrCodeStrings = await qrCodeModelInstance.listQrCodeLinksModel(jobId, request.body);
	// const newArray = qrCodeStrings.flatMap(i => [i, i]);
	for (let [isWidthLimit, qrCodeContent] of qrCodeStrings.entries()) {
		let qrStatus = new Promise(async (resolve, reject) => {
			let qrImageFileName = qrCodeContent.random_number + `_` + jobId + `.png`;
			let qrFilePath = `/tmp/qrCodes/${jobId}/${qrImageFileName}`;
			if (fs.existsSync(qrFilePath)) {
				let responseQR = fs.readFileSync(qrFilePath);
				resolve(responseQR);
			} else {
				let image = await axios.default(Const_AWS_BASE + qrCodeContent.qr_image, {
					responseType: "arraybuffer",
				});
				resolve(image.data);
			}
		});

		await qrStatus
			.then(async (qrContent) => {
				const pngImage = await pdfDoc.embedPng(qrContent);
				const pngDims = pngImage.scale(0.50);
				const baseMargin = 10;

				if (isWidthLimit % qrPerRow === 0) {
					isHeightLimit += 1;
					rowShiftBy += 6.25 + page.getHeight() / qrPerColumn;
					columnShiftBy = 1 + page.getWidth() / qrPerRow + 0.15;
					howBroad = -0.03;
				} else {
					howBroad += 0.97;
				}
				if (isHeightLimit === qrPerColumn) {
					page = pdfDoc.addPage();
					rowShiftBy = page.getHeight() / qrPerColumn + 10;
					isHeightLimit = 1;
					howBroad = 0;
				}
				let xPosition = baseMargin + page.getWidth() / 20 + howBroad * columnShiftBy;
				let yPosition = page.getHeight() - rowShiftBy - 10;


				const jobDetails = qrCodeContent.shipment_job;
				const companyDetails = jobDetails.job_company ? jobDetails.job_company : false;
				const randomNumber = qrCodeContent.random_number;
				const labelNumber = qrCodeContent.label_number;

				const externalReference = `CID: ${qrCodeContent &&
					qrCodeContent.shipment_job &&
					qrCodeContent.shipment_job.external_reference !== "undefined" &&
					qrCodeContent.shipment_job.external_reference !== "null" &&
					qrCodeContent.shipment_job.external_reference
					}`;

				const jobInfo = `Job: ${jobDetails.job_number}`;
				place = `${place ? place : "Final Mile, Paso"}`;
				const companyName = `${companyDetails ? companyDetails.company_name : "Mover Inventory"}`;
				const contactNumber = `${companyDetails.phone ? companyDetails.phone : "1234567890"}`;
				const fromAddress = `From: ${jobDetails.pickup_address &&
					jobDetails.pickup_address !== "undefined" &&
					jobDetails.pickup_address !== "null"
					? jobDetails.pickup_address
					: ""
					} ${jobDetails.pickup_address2 &&
						jobDetails.pickup_address2 !== "undefined" &&
						jobDetails.pickup_address2 !== "null"
						? jobDetails.pickup_address2
						: ""
					}`;
				const fromAddress1 = `${jobDetails.pickup_city &&
					jobDetails.pickup_city !== "undefined" &&
					jobDetails.pickup_city !== "null"
					? jobDetails.pickup_city
					: ""
					} ${jobDetails.pickup_state &&
						jobDetails.pickup_state !== "undefined" &&
						jobDetails.pickup_state !== "null"
						? jobDetails.pickup_state
						: ""
					}${jobDetails.pickup_zipcode &&
						jobDetails.pickup_zipcode !== "undefined" &&
						jobDetails.pickup_zipcode !== "null"
						? ", " + jobDetails.pickup_zipcode
						: ""
					}`;
				const fromAddress2 = `${jobDetails.pickup_country &&
					jobDetails.pickup_country !== "undefined" &&
					jobDetails.pickup_country !== "null"
					? jobDetails.pickup_country
					: ""
					}`;

				const toAddress = `To: ${jobDetails.delivery_address &&
					jobDetails.delivery_address !== "undefined" &&
					jobDetails.delivery_address !== "null"
					? jobDetails.delivery_address
					: ""
					} ${jobDetails.delivery_address2 &&
						jobDetails.delivery_address2 !== "undefined" &&
						jobDetails.delivery_address2 !== "null"
						? jobDetails.delivery_address2
						: ""
					}`;
				const toAddress1 = `${jobDetails.delivery_city &&
					jobDetails.delivery_city !== "undefined" &&
					jobDetails.delivery_city !== "null"
					? jobDetails.delivery_city
					: ""
					} ${jobDetails.delivery_state &&
						jobDetails.delivery_state !== "undefined" &&
						jobDetails.delivery_state !== "null"
						? jobDetails.delivery_state
						: ""
					}${jobDetails.delivery_zipcode &&
						jobDetails.delivery_zipcode !== "undefined" &&
						jobDetails.delivery_zipcode !== "null"
						? ", " + jobDetails.delivery_zipcode
						: ""
					}`;
				const toAddress2 = `${jobDetails.delivery_country &&
					jobDetails.delivery_country !== "undefined" &&
					jobDetails.delivery_country !== "null"
					? jobDetails.delivery_country
					: ""
					}`;

				const smallTextSize = 8;
				const bigTextSize = 12;
				const sideMomentQR = 80;
				let labelPosition = 65;

				const labelMeta = () => {
					const _ = {
						x: xPosition + sideMomentQR,
						y: yPosition + labelPosition,
						size: smallTextSize,
					};
					labelPosition -= 10.6;
					return _;
				};

				if (place.length <= 40) {
					if (title_flag)
						page.drawText(place, {
							x: xPosition + 10,
							y: yPosition + 81,
							size: bigTextSize - (place.length < 32 ? 0 : bigTextSize / place.length),
							font: HelveticaBold,
						});
				} else {
					const newWord = chunkSubstr(place, 40);
					function chunkSubstr(str, size) {
						const numChunks = Math.ceil(str.length / size);
						const chunks = new Array(numChunks);

						for (let i = 0, o = 0; i < numChunks; ++i, o += size) {
							chunks[i] = str.substr(o, size);
						}

						return chunks;
					}
					if (title_flag)
						page.drawText(newWord[1], {
							x: xPosition + 10,
							y: yPosition + 81,
							size: bigTextSize - (newWord[1].length < 32 ? 0 : bigTextSize / newWord[1].length),
							font: HelveticaBold,
						});
					if (title_flag)
						page.drawText(newWord[0], {
							x: xPosition + 10,
							y: yPosition + 93,
							size: bigTextSize - (newWord[0].length < 32 ? 0 : bigTextSize / newWord[0].length),
							font: HelveticaBold,
						});
				}

				if (job_number_flag) page.drawText(jobInfo, labelMeta());

				if (company_name_flag) page.drawText(companyName, labelMeta());

				if (company_contact_flag) page.drawText(contactNumber, labelMeta());

				if (external_reference_flag) page.drawText(externalReference, labelMeta());

				if (from_address_flag) {
					page.drawText(fromAddress, {
						...labelMeta(),
						font: HelveticaBold,
					});
					jobDetails.pickup_city !== "" &&
						jobDetails.pickup_city !== "undefined" &&
						jobDetails.pickup_city !== "null" &&
						jobDetails.pickup_state !== "" &&
						jobDetails.pickup_state !== "undefined" &&
						jobDetails.pickup_state !== "null" &&
						jobDetails.pickup_zipcode !== "" &&
						jobDetails.pickup_zipcode !== "undefined" &&
						jobDetails.pickup_zipcode !== "null"
						? page.drawText(fromAddress1, {
							...labelMeta(),
							font: HelveticaBold,
						})
						: "";

					jobDetails.pickup_country !== "" &&
						jobDetails.pickup_country !== "undefined" &&
						jobDetails.pickup_country !== "null"
						? page.drawText(fromAddress2, {
							...labelMeta(),
							font: HelveticaBold,
						})
						: "";
				}

				if (to_address_flag) {
					page.drawText(toAddress, {
						...labelMeta(),
						font: HelveticaBold,
					});
					page.drawText(toAddress1, {
						...labelMeta(),
						font: HelveticaBold,
					});
					page.drawText(toAddress2, {
						...labelMeta(),
						font: HelveticaBold,
					});
				}



				page.drawImage(pngImage, {
					x: xPosition + 15,
					y: yPosition + 8,
					...pngDims,
				});

				if (qr_code_label_flag)
					page.drawText(randomNumber, {
						x: xPosition + 10,
						y: yPosition,
						size: randomNumber.length > 7 ? 10 : 13,
						font: HelveticaBold,
					});

				if (sequenced_label_flag)
					page.drawText(labelNumber, {
						x: xPosition + 20,
						y: yPosition + 64,
						size: 13,
						font: HelveticaBold,
					});

			})
			.catch((err) => console.log(err));
	}
	try {
		pdfDoc
			.save()
			.then((result) => {
				let baseFolderPath = `/tmp/qrPdf/${jobId}`;
				// let qrPdfPath = `${baseFolderPath}/${jobId}_${date_ob.toISOString()}.pdf`;
				let qrPdfPath = `${baseFolderPath}/tempFile.pdf`;
				if (!fs.existsSync(baseFolderPath)) fs.mkdirSync(`${baseFolderPath}`, { recursive: true });

				if (result) {
					fs.writeFileSync(qrPdfPath, result);

					fs.readFile(qrPdfPath, (err, fileData) => {
						if (!err) {
							response.status(CREATED_CODE);
							response.contentType("application/pdf");
							response.send(fileData);
							console.log("pdfSaved");
							return;
						} else {
							generateResponse(response, SERVER_ERROR_CODE, 0, GENERIC_REQUEST_FAILURE, {});
						}
					});
				} else {
					generateResponse(response, NO_CONTENT_CODE, 0, GENERIC_REQUEST_FAILURE, {});
				}
			})
			.catch((err) => generateResponse(response, SERVER_ERROR_CODE, 0, err, {}));
	} catch (error) {
		console.log("exports.printLabelController -> error: ", error);

		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};


exports.checkJobExistence = async (request, response, next) => {
	try {
		const jobId = request.body.jobId ? request.body.jobId : request.params.jobId;
		if (await qrCodeModelInstance.checkJobExistenceModel(jobId)) {
			next();
		} else {
			generateResponse(response, NOT_FOUND_CODE, 0, JOB_NOT_FOUND, {});
		}
	} catch (error) {
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};

exports.isQrCodeUsedController = async (request, response, next) => {

	try {
		const { qrCodeId } = request.params;
		const { qr_id, isManualLabel, label_no, job_id, lot_no, type, qr_generate_code } = request.body;
		let qrCode = qr_id ? qr_id : qrCodeId;

		if (type === "External") {
			const qr_generate_code_check = qr_generate_code.replace(/\s/g, "")

			let checkExternalRandomNumber = await qrCodeModelInstance.CheckExternalRandomNumber(request, qr_generate_code_check)

			if (checkExternalRandomNumber) {
				response.status(CONFLICT_CODE).json({
					status: 1,
					message: "Qr code already use.",
					data: {},
				});
			}
			else if (request.body.type !== "External") {
				next();
			}
			else {
				let { label_number } = await qrCodeModelInstance.checkExternalLabelNumber()
				let generateQrForExternal = await qrCodeModelInstance.generateQrForExternal(job_id, qr_generate_code_check, label_number);
				request.body.qr_id = generateQrForExternal.qr_code_id
				next();
			}
		}
		else {
			if (isManualLabel == true || isManualLabel == "true") {
				if (lot_no && lot_no !== "" && lot_no !== null) {
					let lotNumberExitsCheck = await inventoryModel.lotNumberExitsCheck(job_id, label_no, lot_no);

					if (lotNumberExitsCheck) {
						generateResponse(response, CONFLICT_CODE, 0, SHIPMENT_INVENTORY_ALREADY_LABELNUMBER, {});
					}
					else {

						let qrCodeBulk = [];
						let { label_number } = await qrCodeModelInstance.findLastLabel(job_id);

						for (let qrCodeString = 0; qrCodeString < 1; qrCodeString++) {
							let qrString = randomString(7, charsetOptions);
							let qr_number = label_number + qrCodeString + 1;
							//newChange
							const qrImageFileName = await generateAndUploadLabel(qrString, job_id)
							if (qrImageFileName) {
								qrCodeBulk.push({
									random_number: qrString,
									qr_image: qrImageFileName,
									job_id: job_id,
									label_number: qr_number,
								});
							}
						}
						let generateQrModel = await qrCodeModelInstance.generateQrModelForManualLabel(qrCodeBulk)
						request.body.qr_id = generateQrModel.qr_code_id
						next();
					}
				}
				else {

					let labelExitsCheck = await inventoryModel.labelExitsCheckModel(job_id, label_no);

					if (labelExitsCheck) {
						generateResponse(response, CONFLICT_CODE, 0, SHIPMENT_INVENTORY_ALREADY_LABELNUMBER, {});
					}
					else {

						let qrCodeBulk = [];
						let { label_number } = await qrCodeModelInstance.findLastLabel(job_id);

						for (let qrCodeString = 0; qrCodeString < 1; qrCodeString++) {
							let qrString = randomString(7, charsetOptions);
							let qr_number = label_number + qrCodeString + 1;
							//newChanges
							const qrImageFileName = await generateAndUploadLabel(qrString, job_id)
							if (qrImageFileName) {
								qrCodeBulk.push({
									random_number: qrString,
									qr_image: qrImageFileName,
									job_id: job_id,
									label_number: qr_number,
								});
							}

						} let generateQrModel = await qrCodeModelInstance.generateQrModelForManualLabel(qrCodeBulk)
						request.body.qr_id = generateQrModel.qr_code_id
						next();
					}
				}
			}
			else if (await qrCodeModelInstance.isQrCodeUsedModel(qrCode)) {
				let qrTypeChecking = await qrCodeModelInstance.qrTypeChecking(qrCode);
				if (qrTypeChecking.type === "Generic") {
					let genericQrUpdate = await qrCodeModelInstance.genericQrUpdate(qrCode, job_id)
					next();
				}
				else {
					next();
				}
			}
			else {
				generateResponse(response, EXPECTATION_FAILED_CODE, 0, QR_USED, {});
			}
		}
	} catch (error) {
		console.log("exports.isQrCodeUsedController1 -> error: ", error);

		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};
