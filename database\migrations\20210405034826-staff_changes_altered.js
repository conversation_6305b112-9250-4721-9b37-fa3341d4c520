"use strict";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.addColumn("staffs", "roles", {
				type: Sequelize.ENUM("ADMIN", "WORKER"),
				defaultValue: "WORKER",
			})
			await queryInterface.addColumn("staffs", "notes", {
				type: Sequelize.TEXT
			})
			await queryInterface.removeColumn("staffs", "address")
			return Promise.resolve()

		} catch (e) {
			return Promise.reject(e);
		}
	},

	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn("staffs", "roles")
			await queryInterface.removeColumn("staffs", "notes")
			await queryInterface.addColumn("staffs", "address", {
				type: DataTypes.STRING
			})
			return Promise.resolve()

		} catch (e) {
			return Promise.reject(e);
		}
	},
};
