"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "admins",
        "is_verified",
        {
          type: Sequelize.TINYINT,
          defaultValue: 0,
        }
      );
      await queryInterface.addColumn(
        "admins",
        "verification_code",
        {
          type: Sequelize.STRING,
          defaultValue: ''
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("admins", "is_verified");
      await queryInterface.removeColumn("admins", "verification_code");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
