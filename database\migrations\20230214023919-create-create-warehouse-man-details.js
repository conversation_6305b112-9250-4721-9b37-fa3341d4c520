"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable("warehouse_mans", {
      warehouse_man_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      shipment_job_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "shipment_jobs",
          key: "shipment_job_id",
        },
      },
      staff_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "staffs",
          key: "staff_id",
        },
      },
      unit_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "unit_lists",
          key: "unit_id",
        },
      },
      storage_unit_id: {
        type: Sequelize.STRING,
        allowNull: false
      },
      shipment_stage_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      warehouse_man_notes: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      photo: {
        type: Sequelize.TEXT,
        defaultValue: ''
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      }
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("warehouse_mans");
  },
};
