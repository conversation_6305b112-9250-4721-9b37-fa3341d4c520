"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_inventories',
        'is_additional_scan',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
      );
      await queryInterface.addColumn(
        'shipment_inventories',
        'is_remove_scan',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_inventories", "is_additional_scan");
      await queryInterface.removeColumn("shipment_inventories", "is_remove_scan");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};