"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable("shipment_inventories", {
      shipment_inventory_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      shipment_job_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "shipment_jobs",
          key: "shipment_job_id",
        },
      },
      room_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "shipment_rooms",
          key: "shipment_room_id",
        },
      },
      qr_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "qr_codes",
          key: "qr_code_id",
        },
      },
      item_name: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      description: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      is_carton: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      packed_by_owner: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      carrier_packed_user_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "staffs",
          key: "staff_id",
        },
      },
      is_disassembled: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      disassembled_by_owner: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      disassembled_user_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "staffs",
          key: "staff_id",
        },
      },
      is_electronics: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      serial_number: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      is_high_value: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      declared_value: {
        type: Sequelize.DECIMAL(11, 2),
        defaultValue: 0,
      },
      is_pro_gear: {
        type: Sequelize.TINYINT,
        defaultValue: 0,
      },
      pro_gear_weight: {
        type: Sequelize.DECIMAL(11, 2),
        defaultValue: 0,
      },
      volume: {
        type: Sequelize.DECIMAL(11, 2),
        defaultValue: 0,
      },
      weight: {
        type: Sequelize.DECIMAL(11, 2),
        defaultValue: 0,
      },
      pads_used: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      prepared_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "staffs",
          key: "staff_id",
        },
      },
      notes: {
        type: Sequelize.TEXT,
        defaultValue: "",
      },
      signature: {
        type: Sequelize.TEXT,
        defaultValue: "",
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("shipment_inventories");
  },
};
