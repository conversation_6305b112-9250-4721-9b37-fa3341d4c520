const fs = require("fs");
const md5 = require('md5');
const nodemailer = require("nodemailer");
const jwt = require("jsonwebtoken");
const { validationResult } = require("express-validator");


/** 
 * generate random string function
*/

exports.randomString = (len, charSet) => {
	charSet = charSet || "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
	let randomString = "";
	for (let i = 0; i < len; i++) {
		let randomPoz = Math.floor(Math.random() * charSet.length);
		randomString += charSet.substring(randomPoz, randomPoz + 1);
	}
	return randomString;
};


/** 
 * display only first error function
*/

exports.displayOnlyFirstError = (data) => {
	return data.filter(
		(thing, index, self) => index === self.findIndex((t) => t.param === thing.param)
	);
};

/** 
 * upload image function
*/

module.exports.UploadImageS3 = (params, s3) =>
	new Promise((resolve, reject) => {
		s3.upload(params, function (err, data) {
			if (err) reject(err);
			// an error occurred
			else resolve(data); // successful response
		});
	});


/** 
* delete image function
*/

module.exports.deleteImageS3 = (params, s3) => {
	return new Promise((resolve, reject) => {
		s3.deleteObject(params, (err, data) => {
			if (err) reject(err);
			// an error occurred
			else resolve(data); // successful response
		});
	});
};


/** 
 * readFile function
*/

module.exports.readFile = (path, opts = "utf8") =>
	new Promise((resolve, reject) => {
		fs.readFile(__dirname + "/../assets/" + path, opts, (err, data) => {
			if (err) reject(err);
			else resolve(data);
		});
	});

/** 
* sendEmail function
*/


module.exports.sendEmail = (email, subject, body, CCEmailId, attachments) => {
	return new Promise((resolve, reject) => {
		let smtpTransport = require("nodemailer-smtp-transport");
		let transporter = nodemailer.createTransport(

			smtpTransport({
				host: Const_Email_Host,
				port: Const_Email_Port,
				secure: false, // true for 465, false for other ports
				service: "gmail",
				//service: 'ses-smtp-user.20190212-100617',
				auth: {
					user: Const_Email_id, // generated ethereal user
					pass: Const_Email_Password, // generated ethereal password
				},
			})
		);

		let mailOptions = {
			from: '"Mover Inventory"< ' + Const_Email + ">", // sender address
			to: email, // list of receivers
			subject: subject, // Subject line
			//text: 'Hello All? Senbd fron Node.js!', // plain text body
			html: body, // html body
		};

		if (typeof CCEmailId !== "undefined" && CCEmailId && CCEmailId.length > 0) {
			mailOptions.cc = CCEmailId;
		}

		if (attachments !== "undefined" && attachments !== null && attachments !== "") {
			mailOptions.attachments = attachments;
		}

		transporter.sendMail(mailOptions, (error, info) => {
			try {
				if (error) {
					throw error;
				}
				resolve(true);
			} catch (e) {
				reject(e);
			}
		});
	});
};


/** 
 * JWT token generate function
*/

module.exports.jwtTokenGenerate = (user_id) =>
	new Promise((resolve, reject) => {
		jwt.sign(
			{ user_id: user_id },
			JWTPRIVATEKEY,
			// { expiresIn: "1d" },
			function (err, token) {
				if (err) reject(err);
				else resolve(token);
			}
		);
	});

/** 
* JWT token verify function
*/

module.exports.jwtTokenVerify = (accessToken) =>
	new Promise((resolve, reject) => {
		jwt.verify(accessToken, JWTPRIVATEKEY, function (err, decoded) {
			if (err) resolve(0);
			else resolve(decoded);
		});
	});

/** 
* JWT token decode function
*/

module.exports.jwtTokenDecode = (accessToken) =>
	new Promise((resolve, reject) => {
		let decoded = jwt.decode(accessToken, { complete: true });
		resolve(decoded);
	});


/** 
* error handler function
*/

module.exports.errorHandler = (request, response, next) => {
	const errors = validationResult(request);
	if (!errors.isEmpty()) {
		let message = "";
		errors.array().map((el) => (message += el.msg + ""));
		return response.status(NOT_VALID_DATA_CODE).json({ status: 0, message: message, data: {} });
	} else {
		next();
	}
};


/** 
* generate response function
*/

module.exports.generateResponse = (response, statusCode, isSuccess, message, payload) => {
	console.log(message);
	response.status(statusCode).json({
		status: isSuccess,
		message: message.message ? message.message : message,
		data: payload ? payload : {},
	});
};

/** 
* random sequenced string function
*/

exports.randomSequencedString = (len, charSet) => {
	charSet = charSet || "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	let half = Math.ceil(len / 2);
	let randomString = "";
	for (let iterator = 0; iterator < len; iterator++) {
		if (iterator === half) charSet = "0123456789";
		let randomPoz = Math.floor(Math.random() * charSet.length);
		randomString += charSet.substring(randomPoz, randomPoz + 1);
	}
	return randomString;
};
exports.leftFillNum = (num, targetLength) => num.toString().padStart(targetLength, 0);
