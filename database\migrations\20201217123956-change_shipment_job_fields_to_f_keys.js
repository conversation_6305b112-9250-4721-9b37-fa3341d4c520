"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.changeColumn("shipment_jobs", "sales_represent", {
        type: Sequelize.INTEGER(11),
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "staffs",
          key: "staff_id",
        },
      })
      await queryInterface.changeColumn("shipment_jobs", "move_coordinator", {
        type: Sequelize.INTEGER(11),
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "staffs",
          key: "staff_id",
        },
      })
      return Promise.resolve()

    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.changeColumn("shipment_jobs", "sales_represent", {
        type: Sequelize.STRING,
      })
      await queryInterface.changeColumn("shipment_jobs", "move_coordinator", {
        type: Sequelize.STRING,
      })
      return Promise.resolve()

    } catch (e) {
      return Promise.reject(e);
    }
  },
};
