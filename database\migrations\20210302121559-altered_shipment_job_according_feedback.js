"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.renameColumn("shipment_jobs", 'estimated_units', "estimated_volume")
      await queryInterface.addColumn(
        'shipment_jobs',
        'external_reference_3',
        {
          type: Sequelize.STRING,
        }
      )
      await queryInterface.addColumn(
        'shipment_jobs',
        'shipment_name',
        {
          type: Sequelize.STRING(200),
        }
      )
      await queryInterface.addColumn(
        'shipment_jobs',
        'external_reference_2',
        {
          type: Sequelize.STRING,
        }
      )
      await queryInterface.removeColumn("shipment_jobs", "job_type")
      await queryInterface.removeColumn("shipment_jobs", "activity_type")
      await queryInterface.removeColumn("shipment_jobs", "contact_name")
      await queryInterface.removeColumn("shipment_jobs", "account_name")
      await queryInterface.removeColumn("shipment_jobs", "phone3")
      await queryInterface.removeColumn("shipment_jobs", "pickup_city")
      await queryInterface.removeColumn("shipment_jobs", "pickup_state")
      await queryInterface.removeColumn("shipment_jobs", "pickup_country")
      await queryInterface.removeColumn("shipment_jobs", "pickup_zip")
      await queryInterface.removeColumn("shipment_jobs", "delivery_city")
      await queryInterface.removeColumn("shipment_jobs", "delivery_state")
      await queryInterface.removeColumn("shipment_jobs", "delivery_country")
      await queryInterface.removeColumn("shipment_jobs", "delivery_zip")
      await queryInterface.removeColumn("shipment_jobs", "sales_represent")
      await queryInterface.removeColumn("shipment_jobs", "term")
      await queryInterface.removeColumn("shipment_jobs", "frequent_access")
      return Promise.resolve()

    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.changeColumn("shipment_jobs", "sales_represent", {
        type: Sequelize.STRING,
      })
      await queryInterface.changeColumn("shipment_jobs", "move_coordinator", {
        type: Sequelize.STRING,
      })
      await queryInterface.addColumn("shipment_jobs", "job_type", {
        type: Sequelize.STRING(150),
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "activity_type", {
        type: Sequelize.STRING(150),
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "contact_name", {
        type: Sequelize.STRING,
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "account_name", {
        type: Sequelize.STRING,
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "phone3", {
        type: Sequelize.STRING(15),
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "pickup_city", {
        type: Sequelize.STRING,
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "pickup_state", {
        type: Sequelize.STRING,
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "pickup_country", {
        type: Sequelize.STRING,
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "pickup_zip", {
        type: Sequelize.STRING,
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "delivery_city", {
        type: Sequelize.STRING,
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "delivery_state", {
        type: Sequelize.STRING,
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "delivery_country", {
        type: Sequelize.STRING,
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "delivery_zip", {
        type: Sequelize.STRING,
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "sales_represent", {
        type: Sequelize.STRING,
        defaultValue: "",
      })
      await queryInterface.addColumn("shipment_jobs", "term", {
        type: Sequelize.STRING,
        defaultValue: "",})
      await queryInterface.addColumn("shipment_jobs", "frequent_access", {
        type: Sequelize.TINYINT,
        defaultValue: 1,
      })
      return Promise.resolve()

    } catch (e) {
      return Promise.reject(e);
    }
  },
};
