'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_types',
        'is_make_user_mandatory',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 1,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_for_shipments',
        'is_make_user_mandatory',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 1,
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_types', 'is_make_user_mandatory');
      await queryInterface.removeColumn('shipment_type_for_shipments', 'is_make_user_mandatory');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
