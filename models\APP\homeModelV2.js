const {
	staff,
	company,
	shipment_job,
	customer,
	shipment_job_assign_worker,
	shipment_job_assign_worker_list,
	sequelize,
	tag_shipment,
	tag_customer,
	tag,
} = require("../../database/schemas");
const { Op } = require("sequelize");
const moment = require("moment");
const { assertIs } = require("pdf-lib");

exports.jobListAssignWorker = async (request, fieldsAndValues, staff_id) => {
	const { roles, company_id } = await staff.findOne({
		where: { staff_id: staff_id },
		raw: true,
	});

	let whereCondition = {
		is_job_complete_flag: 0
	};

	if (roles === "ADMIN") {
		whereCondition.company_id = company_id;
		whereCondition[Op.or] = [
			{ shipment_name: { [Op.like]: `%${fieldsAndValues.search}%` } },
			{ job_number: { [Op.like]: `%${fieldsAndValues.search}%` } },
			{ email: { [Op.like]: `%${fieldsAndValues.search}%` } },
		];
	} else {
		const jobIds = await shipment_job_assign_worker_list.findAll({
			attributes: ["shipment_job_id", "local_shipment_stage_id"],
			where: { staff_id: staff_id },
			raw: true,
		});

		const filterConditions = jobIds.map(filter => ({
			shipment_job_id: filter.shipment_job_id,
			local_job_status: filter.local_shipment_stage_id
		}));

		whereCondition[Op.or] = filterConditions;
	}

	const result = await shipment_job.findAll({
		where: whereCondition,
		attributes: ["shipment_job_id"],
		limit: request.page_size ? parseInt(request.page_size) : 25,
		offset: request.page_no > 1 ? (parseInt(request.page_no) - 1) * (request.page_size ? parseInt(request.page_size) : 25) : 0,
		include: [
			{
				model: shipment_job_assign_worker_list,
				required: false,
				attributes: [
					"staff_id",
					"role"
				],
				as: "assign_worker",
				include: [
					{
						model: staff,
						attributes: ["first_name", "last_name"],
						as: "assign_worker_detail"
					}
				]
			},
			{
				model: company,
				attributes: ["company_id", "company_name"],
				required: true,
				as: "job_company",
				include: [
					{
						model: staff,
						where: { roles: "WORKER", is_deleted: 0, status: "active" },
						attributes: [
							"staff_id",
							["roles", "role"],
							"first_name",
							"last_name",
						],
						required: true,
						as: "staffs",
					},
				],
			},
		],
		order: [["shipment_job_id", "DESC"]],
		raw: false,
	});

	const uniqueWorkers = result.map(job => {
		const uniqueAssignWorkers = Array.from(
			new Map(job.job_company.staffs.map(worker => [worker.staff_id, worker])).values()
		);

		return {
			shipment_job_id: job.shipment_job_id,
			assign_worker: uniqueAssignWorkers
		};
	});

	return uniqueWorkers;
};

exports.getUpdatedJobList = async (request, fieldsAndValues, staff_id) => {
	try {
		const staffDetails = await staff.findOne({
			where: { staff_id },
			attributes: ['roles', 'company_id'],
			raw: true,
		});

		const { roles, company_id } = staffDetails || {};
		let whereCondition = { is_job_complete_flag: 0 };

		if (roles === 'ADMIN') {
			whereCondition.company_id = company_id;
			whereCondition[Op.or] = [
				{ shipment_name: { [Op.like]: `%${fieldsAndValues.search}%` } },
				{ job_number: { [Op.like]: `%${fieldsAndValues.search}%` } },
				{ email: { [Op.like]: `%${fieldsAndValues.search}%` } }
			];
		} else {
			const jobIds = await shipment_job_assign_worker_list.findAll({
				attributes: ['shipment_job_id', 'local_shipment_stage_id'],
				where: { staff_id },
				raw: true,
			});

			const filterConditions = jobIds.map(({ shipment_job_id, local_shipment_stage_id }) => ({
				shipment_job_id,
				local_job_status: local_shipment_stage_id,
			}));

			whereCondition[Op.or] = filterConditions;
		}



		return await shipment_job.findAndCountAll({
			where: whereCondition,
			limit: request.page_size ? parseInt(request.page_size) : 25,
			offset:
				request.page_no > 1
					? (parseInt(request.page_no) - 1) * (request?.page_size ? parseInt(request?.page_size) : 25)
					: 0,
			include: [
				{
					model: company,
					attributes: ["company_id", "company_name"],
					required: true,
					as: "job_company",
					include: [
						{
							model: staff,
							where: { roles: "ADMIN", is_deleted: 0, status: "active" },
							attributes: ["staff_id", ["roles", "role"], "first_name", "last_name"],
							required: true,
							as: "staffs",
						},
					],
				},
				{
					model: tag_shipment,
					as: "shipment_tag",
					required: false,
					attributes: [
						COMMON_JOB_TAG_ATTRIBUTES[0],
						[sequelize.literal("`shipment_tag->m2m_tag`.tag_id"), "tag_id"],
						[sequelize.literal("`shipment_tag->m2m_tag`.name"), "name"],
						[sequelize.literal("`shipment_tag->m2m_tag`.color"), "color"],
						[sequelize.literal("`shipment_tag->m2m_tag`.company_id"), "company_id"],
					],
					include: {
						model: tag,
						as: "m2m_tag",
						attributes: [],
					},
				},
				{
					model: customer,
					required: true,
					as: "customer_job",
					attributes: [...COMMON_CUSTOMER_ATTRIBUTES],
					include: [
						{
							model: tag_customer,
							as: "customer_tag",
							required: false,
							attributes: [
								"tag_id",
								[sequelize.literal("`customer_job->customer_tag->m2m_customer_tag`.tag_id"), "tag_id"],
								[sequelize.literal("`customer_job->customer_tag->m2m_customer_tag`.name"), "name"],
								[sequelize.literal("`customer_job->customer_tag->m2m_customer_tag`.color"), "color"],
								[sequelize.literal("`customer_job->customer_tag->m2m_customer_tag`.company_id"), "company_id"],

							],
							include: {
								model: tag,
								as: "m2m_customer_tag",
								attributes: []
							},
						},
					],
				},
			],
			raw: false,
			order: [["shipment_job_id", "DESC"]],
			distinct: true
		});

	}
	catch (error) {
		console.log("error", error)
	}
};


exports.jobSummary = async (request, fieldsAndValues, staff_id) => {
	try {
		const staffDetails = await staff.findOne({
			where: { staff_id },
			attributes: ['roles', 'company_id'],
			raw: true,
		});

		const { roles, company_id } = staffDetails || {};
		let whereCondition = { is_job_complete_flag: 0 };

		if (roles === 'ADMIN') {
			whereCondition.company_id = company_id;
			whereCondition[Op.or] = [
				{ shipment_name: { [Op.like]: `%${fieldsAndValues.search}%` } },
				{ job_number: { [Op.like]: `%${fieldsAndValues.search}%` } },
				{ email: { [Op.like]: `%${fieldsAndValues.search}%` } }
			];
		} else {
			const jobIds = await shipment_job_assign_worker_list.findAll({
				attributes: ['shipment_job_id', 'local_shipment_stage_id'],
				where: { staff_id },
				raw: true,
			});

			const filterConditions = jobIds.map(({ shipment_job_id, local_shipment_stage_id }) => ({
				shipment_job_id,
				local_job_status: local_shipment_stage_id,
			}));

			whereCondition[Op.or] = filterConditions;
		}



		return await shipment_job.findAndCountAll({
			where: whereCondition,
			attributes: [
				...COMMON_JOB_ATTRIBUTES,
				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_items",
				],
				[
					sequelize.literal(
						'(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_volume",
				],
				[
					sequelize.literal(
						'(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_weight",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
					),
					"firearms_total_quantity",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
					),
					"total_cartons",
				],


				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
					),
					"total_cartons_cp",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
					),
					"total_cartons_pbo",
				],

				[
					sequelize.literal(
						'(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
					),
					"total_high_value",
				],

				[
					sequelize.literal(
						'(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)'
					),
					"total_pads_used",
				],

				[
					sequelize.literal(
						'(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
					),
					"total_pro_gear_weight",
				],

				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
					),
					"total_pro_gear_items",
				],
				[
					sequelize.literal(
						"(select CONCAT(first_name,' ',(IF(last_name IS NULL, '', last_name)))  FROM `customers` where customer_id = shipment_job.customer_id) "
					),
					"customer_name",
				],
				[
					sequelize.literal(
						"(select company_name FROM `companies` where company_id = shipment_job.company_id) "
					),
					"company_name",
				],
				[
					sequelize.literal(
						"(select account_id FROM `customers` where customer_id = shipment_job.customer_id) "
					),
					"account_id",
				],
				[
					sequelize.literal(
						"(select name  FROM `shipment_type_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id ) "
					),
					"shipment_type_name",
				],
				[
					sequelize.literal(
						"(select CASE WHEN is_make_user_mandatory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id) "
					),
					"is_make_user_mandatory",
				],
				[
					sequelize.literal(
						"(select (IF(phone IS NULL, '', phone))  FROM `customers` where customer_id = shipment_job.customer_id) "
					),
					"customer_phone",
				],
				[sequelize.literal("(IF(shipment_name IS NULL, '', shipment_name))"), "shipment_name"],
				[
					sequelize.literal(
						"(select name FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"current_job_status",
				],
				[
					sequelize.literal(
						"(select CASE WHEN is_add_item = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"is_add_item",
				],
				[
					sequelize.literal(
						"(select CASE WHEN is_add_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"is_add_exceptions",
				],
				[
					sequelize.literal(
						"(select CASE WHEN show_no_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"show_no_exceptions",
				],
				[
					sequelize.literal(
						"(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"current_job_stage",
				],
				[
					sequelize.literal(
						"(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
					),
					"total_stages",
				],
				[
					sequelize.literal(
						"(select CASE WHEN order_of_stages = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"is_first_stage",
				],
				[
					sequelize.literal(
						"(select CASE WHEN scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"is_scan_require",
				],
				[
					sequelize.literal(
						"(select CASE WHEN remove_scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"remove_scan_require",
				],
				[
					sequelize.literal(
						"(select CASE WHEN allow_default_manual_label = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"allow_default_manual_label",
				],
				[
					sequelize.literal(
						"(select CASE WHEN add_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"add_items_to_inventory",
				],
				[
					sequelize.literal(
						"(select CASE WHEN remove_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"remove_items_to_inventory",
				],
				[
					sequelize.literal(
						"(select CASE WHEN enable_partial_complete_stage = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"enable_partial_complete_stage",
				],
				[
					sequelize.literal(
						"(select CASE WHEN scan_into_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"scan_into_storage",
				],
				[
					sequelize.literal(
						"(select CASE WHEN assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"assign_storage_units_to_items",
				],
				[
					sequelize.literal(
						"(select CASE WHEN scan_out_of_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"scan_out_of_storage",
				],
				[
					sequelize.literal(
						"(select CASE WHEN unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"unassign_storage_units_from_items",
				],
				[
					sequelize.literal(
						"(select CASE WHEN supervisor_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"is_supervisor_signature_require",
				],

				[
					sequelize.literal(
						"(select why_supervisor_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"why_supervisor_signature_require_note",
				],

				[
					sequelize.literal(
						"(select CASE WHEN customer_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"is_customer_signature_require",
				],

				[
					sequelize.literal(
						"(select why_customer_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
					),
					"why_customer_signature_require_note",
				],
				[
					sequelize.literal(`(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`),
					"is_job_complete",
				],
				[
					sequelize.literal(
						`(if(
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL) = 
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "1")
						,"yes","no"))`
					),
					"isAllItemScanned"
				],
				[
					sequelize.literal(
						`(if(
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "0") = 
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NOT NULL AND isScannedFlag = "0")
						,"yes","no"))`
					),
					"isAllItemsAssignToUnits"
				],
				[
					sequelize.literal(
						`(if(
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "0") = 
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL AND isScannedFlag = "0")
						,"yes","no"))`
					),
					"isAllItemsUnassignToUnits"
				],
				[
					sequelize.literal(
						`(if(
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "0") = 
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND is_additional_scan = "1" AND isScannedFlag = "0")
						,"yes","no"))`
					),
					"isAllAdditionalScanStageItemsScanned"
				],
				[
					sequelize.literal(
						`(if(
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL) = 
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND is_remove_scan = "1")
						,"yes","no"))`
					),
					"isAllRemoveScanStageItemsScanned"
				],
			],
			limit: request.page_size ? parseInt(request.page_size) : 25,
			offset:
				request.page_no > 1
					? (parseInt(request.page_no) - 1) * (request?.page_size ? parseInt(request?.page_size) : 25)
					: 0,
			include: [],
			order: [["shipment_job_id", "DESC"]],
		});

	}
	catch (error) {
		console.log("error", error)
	}
};


