"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("shipment_inventory_forced_photo");
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.createTable("shipment_inventory_forced_photo", {
      shipment_inventory_forced_photo_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      shipment_inventory_forced_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "shipment_inventory_forced",
          key: "shipment_inventory_id",
        },
      },
      media: {
        type: Sequelize.TEXT,
        defaultValue: "",
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
    });
  }
};
