"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable("generic_labels", {
      qr_code_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      company_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "companies",
          key: "company_id",
        },
      },
      random_number: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      label_number: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      qr_image: {
        type: Sequelize.TEXT,
        defaultValue: "",
      },
      status: {
        type: Sequelize.ENUM,
        allowNull: true,
        values: ["Active", "Inactive"],
        defaultValue: "Active",
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("generic_labels");
  },
};
