'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_jobs',
        'default_manual_label',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_jobs', 'default_manual_label');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
