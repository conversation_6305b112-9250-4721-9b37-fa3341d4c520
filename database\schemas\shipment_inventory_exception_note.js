"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_inventory_exception_note = sequelize.define(
		"shipment_inventory_exception_note",
		{
			shipment_inventory_exception_note_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			shipment_inventory_id: DataTypes.INTEGER,
			notes: DataTypes.TEXT,
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	shipment_inventory_exception_note.associate = function (models) {
		// associations can be defined here
		shipment_inventory_exception_note.hasMany(
			models.shipment_inventory_exception,
			{
				as: "eid",
				foreignKey: "exception_note_id",
			}
		);

		shipment_inventory_exception_note.hasMany(
			models.shipment_inventory_location,
			{
				as: "lid",
				foreignKey: "exception_note_id",
			}
		);
	};
	return shipment_inventory_exception_note;
};
