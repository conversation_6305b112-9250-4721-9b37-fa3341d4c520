"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_inventory_location = sequelize.define(
		"shipment_inventory_location",
		{
			shipment_inventory_location_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			shipment_inventory_id: DataTypes.INTEGER,
			exception_note_id: DataTypes.INTEGER,
			shipment_location_id: DataTypes.INTEGER,
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	shipment_inventory_location.associate = function (models) {
		// associations can be defined here
		shipment_inventory_location.belongsTo(models.shipment_inventory, {
			foreignKey: "shipment_inventory_id",
		});

		shipment_inventory_location.belongsTo(
			models.shipment_inventory_exception_note,
			{ foreignKey: "exception_note_id" }
		);

		shipment_inventory_location.belongsTo(models.shipment_location, {
			as: "location_list",
			foreignKey: "shipment_location_id",
		});
	};
	return shipment_inventory_location;
};
