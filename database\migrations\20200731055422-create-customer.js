'use strict';
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('customers', {
      customer_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        autoIncrement: true,
        primaryKey: true
      },
      first_name: {
        type: Sequelize.STRING(50)
      },
      last_name: {
        type: Sequelize.STRING(50)
      },
      photo: {
        type: Sequelize.TEXT, 
        defaultValue: ''
      },
      address: {
        type: Sequelize.STRING,
        defaultValue: ''
      },
      phone: {
        type: Sequelize.STRING(15),
        defaultValue: ''
      },
      email: {
        type: Sequelize.STRING
      },
      password: {
        type: Sequelize.STRING,
        defaultValue: ''
      },
      total_shipment: {
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      push_notification: {
        type: Sequelize.TINYINT,
        defaultValue: 1
      },
      is_verified: {
        type: Sequelize.TINYINT,
        defaultValue: 0
      },
      status: {
        type: Sequelize.ENUM('inactive', 'active'), 
        defaultValue: 'active'
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date()
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date()
      }
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('customers');
  }
};