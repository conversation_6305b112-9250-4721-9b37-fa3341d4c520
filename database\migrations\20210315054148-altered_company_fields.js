'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn(
        'companies',
        'start_date'
      );
      await queryInterface.removeColumn(
        'companies',
        'end_date'
      );
      await queryInterface.addColumn(
        'companies',
        'notes',
        {
          type: Sequelize.TEXT
        });
      return Promise.resolve()

    } catch (e) {
      return Promise.reject(e);
    }
  },
  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'companies',
        'start_date',
        {
          type: Sequelize.DATEONLY
        }
      );
      await queryInterface.addColumn(
        'companies',
        'end_date',
        {
          type: Sequelize.DATEONLY
        }
      );
      await queryInterface.removeColumn(
        'companies',
        'notes'
      );

      return Promise.resolve()

    } catch (e) {
      return Promise.reject(e);
    }
  }
};
