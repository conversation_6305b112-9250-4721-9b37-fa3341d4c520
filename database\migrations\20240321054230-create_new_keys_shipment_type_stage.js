'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_type_stages',
        'allow_default_manual_label',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stages',
        'add_items_to_inventory',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stages',
        'assign_storage_units_to_items',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stages',
        'unassign_storage_units_from_items',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stages',
        'remove_items_to_inventory',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stages',
        'enable_partial_complete_stage',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'allow_default_manual_label',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'add_items_to_inventory',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'assign_storage_units_to_items',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'unassign_storage_units_from_items',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'remove_items_to_inventory',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'enable_partial_complete_stage',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_type_stages', 'allow_default_manual_label');
      await queryInterface.removeColumn('shipment_type_stages', 'add_items_to_inventory');
      await queryInterface.removeColumn('shipment_type_stages', 'assign_storage_units_to_items');
      await queryInterface.removeColumn('shipment_type_stages', 'unassign_storage_units_from_items');
      await queryInterface.removeColumn('shipment_type_stages', 'remove_items_to_inventory');
      await queryInterface.removeColumn('shipment_type_stages', 'enable_partial_complete_stage');


      await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'allow_default_manual_label');
      await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'add_items_to_inventory');
      await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'assign_storage_units_to_items');
      await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'unassign_storage_units_from_items');
      await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'remove_items_to_inventory');
      await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'enable_partial_complete_stage');

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
