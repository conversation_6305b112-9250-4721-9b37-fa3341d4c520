"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_types",
        "admin_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );

      await queryInterface.addColumn(
        "shipment_types",
        "company_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );

      await queryInterface.addColumn(
        "shipment_types",
        "staff_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );
   
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_types", "company_id");
      await queryInterface.removeColumn("shipment_types", "admin_id");
      await queryInterface.removeColumn("shipment_types", "staff_id");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
