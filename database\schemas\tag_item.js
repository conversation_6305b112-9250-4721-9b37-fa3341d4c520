"use strict";
module.exports = (sequelize, DataTypes) => {
  const tag_item = sequelize.define(
    "tag_item",
    {
      tag_inventory_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tag_id: DataTypes.INTEGER,
      shipment_inventory_id: DataTypes.INTEGER
    },
    { createdAt: false, updatedAt: false, freezeTableName: true }
  );
  tag_item.associate = function (models) {
    tag_item.belongsTo(models.tag,{foreignKey: 'tag_id', as: "m2m_item_tag"});
    tag_item.belongsTo(models.shipment_inventory,{foreignKey: 'shipment_inventory_id', as: "m2m_item"});
  };
  return tag_item;
};
