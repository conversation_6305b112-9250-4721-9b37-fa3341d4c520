const express = require("express");
const app = express();

// App Routes
app.use("/api/staff", require("./staffRoutes"));
app.use("/api/general", require("./generalRoutes"));
app.use("/api/home", require("./homeRoutes"));
app.use("/api/home/<USER>", require("./homeRoutesV2"));


//Open Api 
app.use("/api/open-api", require("./openApiRoutes"));

////Open Api 
app.use("/api/webhook", require("./webhookRoutes"));

// CMS Routes
app.use("/api/admin", require("./adminRoutes"));
app.use("/api/external_api", require("./externalApiRoutes"));

// Integration Routes
app.use("/api/integration", require("./integrationRoutes"));


module.exports = app;
