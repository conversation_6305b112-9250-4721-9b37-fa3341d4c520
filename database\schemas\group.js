"use strict";
module.exports = (sequelize, DataTypes) => {
	const group = sequelize.define(
		"group",
		{
			group_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			name: DataTypes.STRING,
			admin_id:
			{
				type: DataTypes.INTEGER,
			},
			company_id: {
				type: DataTypes.INTEGER,
			},
			staff_id: {
				type: DataTypes.INTEGER,
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			deletedAt: {
				type: DataTypes.DATE
			},
			make_account_id_mandatory: {
				type: DataTypes.TINYINT,
			},
			pdf_time_stamp_checked: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			}
		},
		{ createdAt: false, updatedAt: false, paranoid: true }
	);
	group.associate = function (models) {
	};
	return group;
};
