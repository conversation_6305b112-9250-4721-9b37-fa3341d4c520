"use strict";
module.exports = (sequelize, DataTypes) => {
	const accesstoken = sequelize.define(
		"accesstoken",
		{
			accesstoken_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			admin_id: DataTypes.INTEGER,
			company_id: DataTypes.INTEGER,
			staff_id: DataTypes.INTEGER,
			customer_id: DataTypes.INTEGER,
			access_token: DataTypes.STRING,
			device_token: DataTypes.STRING,
			device_type: DataTypes.ENUM("ios", "android", "web"),
			version: DataTypes.STRING(10),
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{
			createdAt: false,
			updatedAt: false,
		}
	);
	accesstoken.associate = function (models) {
		// associations can be defined here
		// accesstoken.belongsTo(models.authentication,{foreignKey: 'auth_id'});
		accesstoken.belongsTo(models.admin, { foreignKey: "admin_id" });

		accesstoken.belongsTo(models.company, { foreignKey: "company_id" });

		accesstoken.belongsTo(models.staff, { foreignKey: "staff_id" });

		accesstoken.belongsTo(models.customer, { foreignKey: "customer_id" });
	};
	return accesstoken;
};
