"use strict";
module.exports = (sequelize, DataTypes) => {
	const qr_setting = sequelize.define(
		"qr_setting",
		{
			qr_setting_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			shipment_job_id: DataTypes.INTEGER,
			place: {
				type: DataTypes.TEXT,
			},
			title_flag: {
				type: DataTypes.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			job_number_flag: {
				type: DataTypes.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			company_name_flag: {
				type: DataTypes.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			company_contact_flag: {
				type: DataTypes.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			from_address_flag: {
				type: DataTypes.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			to_address_flag: {
				type: DataTypes.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			sequenced_label_flag: {
				type: DataTypes.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			qr_code_label_flag: {
				type: DataTypes.ENUM("yes", "no"),
				defaultValue: "yes",
			},
			external_reference_flag: {
				type: DataTypes.ENUM("yes", "no"),
				defaultValue: "yes",
			},
		},
		{
			createdAt: false,
			updatedAt: false,
		}
	);
	qr_setting.associate = function (models) {
		// associations can be defined here

		qr_setting.belongsTo(models.shipment_job, {
			foreignKey: "shipment_job_id",
		});
	};
	return qr_setting;
};
