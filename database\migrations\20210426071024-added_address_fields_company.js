"use strict";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn(
				"companies",
				"address",
				Sequelize.STRING
			);
			await queryInterface.addColumn("companies", "address1", Sequelize.STRING);
			await queryInterface.addColumn("companies", "address2", Sequelize.STRING);
			await queryInterface.addColumn("companies", "city", Sequelize.STRING);
			await queryInterface.addColumn("companies", "state", Sequelize.STRING);
			await queryInterface.addColumn("companies", "zipCode", Sequelize.STRING);
			await queryInterface.addColumn("companies", "country", Sequelize.STRING);
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},

	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn("companies", "address1");
			await queryInterface.removeColumn("companies", "address2");
			await queryInterface.removeColumn("companies", "city");
			await queryInterface.removeColumn("companies", "state");
			await queryInterface.removeColumn("companies", "zipCode");
			await queryInterface.removeColumn("companies", "country");
			await queryInterface.addColumn("companies", "address", Sequelize.STRING);
			return Promise.resolve();
		} catch (e) {
			return Promise.reject(e);
		}
	},
};
