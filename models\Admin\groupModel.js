const { group, company, sequelize } = require("../../database/schemas");
const { Op, literal } = require("sequelize");
const StaffModel = require("../../models/Admin/staffModel");

exports.isCheckGroupNameExistsModel = async (body) => {
	return await
		group.findOne({
			where: {
				name: body.name
			}
		})

}

exports.createGroupModel = async (groupData, getUserDetails) => {
	if (getUserDetails.admin_id !== null) {
		const itemDetails = await group.create({
			name: groupData.name.replace(/\s/g, ""),
			make_account_id_mandatory: groupData.make_account_id_mandatory,
			pdf_time_stamp_checked: groupData.pdf_time_stamp_checked,
			admin_id: getUserDetails.admin_id,
			company_id: "-1",
			staff_id: getUserDetails.staff_id
		});
		return itemDetails;
	}
	else if (getUserDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);
		const itemDetails = await group.create({
			name: groupData.name.replace(/\s/g, ""),
			make_account_id_mandatory: groupData.make_account_id_mandatory,
			pdf_time_stamp_checked: groupData.pdf_time_stamp_checked,
			admin_id: getUserDetails.admin_id,
			company_id: getStaffDetails.company_id,
			staff_id: getUserDetails.staff_id
		});
		return itemDetails;
	}
	else {
		const itemDetails = await group.create({
			name: groupData.name.replace(/\s/g, ""),
			make_account_id_mandatory: groupData.make_account_id_mandatory,
			pdf_time_stamp_checked: groupData.pdf_time_stamp_checked,
			admin_id: getUserDetails.admin_id,
			company_id: getUserDetails.company_id,
			staff_id: getUserDetails.staff_id
		});
		return itemDetails;
	}
}

exports.getGroupListingModel = async (fieldsAndValues, userDetails) => {
	if (userDetails.admin_id !== null) {
		return await group.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				[Op.or]: [{ name: { [Op.like]: fieldsAndValues.search + "%" } }],
			},
			attributes: [
				"group_id",
				"name",
				[
					sequelize.literal(`(CASE WHEN make_account_id_mandatory = '1' THEN 'Yes' ELSE 'No' END)`),
					"make_account_id_mandatory",
				],
				[
					sequelize.literal(`(CASE WHEN pdf_time_stamp_checked = '1' THEN 'Yes' ELSE 'No' END)`),
					"pdf_time_stamp_checked",
				],
				[
					sequelize.literal(
						'(select count(group_id) FROM `companies` where group_id = group.group_id and is_deleted = "0" and roles = "COMPANYADMIN")'
					),
					"total_count",
				],
				[
					sequelize.literal(
						'(select count(group_id) FROM `companies` where group_id = group.group_id and is_deleted = "0" and roles = "COMPANYSUPERADMIN")'
					),
					"total_user",
				],

			],
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "created_at",
					fieldsAndValues.order_sequence
						? fieldsAndValues.order_sequence
						: "DESC",
				],
			],
		});
	}
	else if (userDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(userDetails.staff_id);
		return await group.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				name:
				{
					[Op.like]: fieldsAndValues.search + "%"
				}
				,
			},
			attributes: [
				"group_id",
				"name",
				[
					sequelize.literal(
						'(select count(group_id) FROM `companies` where group_id = group.group_id and is_deleted = "0" and roles = "COMPANYADMIN")'
					),
					"total_count",
				],
				[
					sequelize.literal(
						'(select count(group_id) FROM `companies` where group_id = group.group_id and is_deleted = "0" and roles = "COMPANYSUPERADMIN")'
					),
					"total_user",
				],
			],
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "created_at",
					fieldsAndValues.order_sequence
						? fieldsAndValues.order_sequence
						: "DESC",
				],
			],
		});
	}

	else {

		return await group.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				[Op.or]: [{ name: { [Op.like]: fieldsAndValues.search + "%" } }],
			},
			attributes: ["group_id", "name",],
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "created_at",
					fieldsAndValues.order_sequence
						? fieldsAndValues.order_sequence
						: "DESC",
				],
			],
		});
	}

}

exports.getGroupUserListingModel = async (groupId, fieldsAndValues, userDetails) => {
	if (userDetails.admin_id !== null) {
		return await company.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				group_id: groupId,
				[Op.or]: [{ company_name: { [Op.like]: fieldsAndValues.search + "%" } }],
				roles: "COMPANYSUPERADMIN"

			},
			attributes: [
				"company_name",
				"group_id",
				"email",
				"company_id"
			],
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "created_at",
					fieldsAndValues.order_sequence
						? fieldsAndValues.order_sequence
						: "DESC",
				],
			],
		});
	}
	else if (userDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(userDetails.staff_id);
		return await group.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				[Op.or]: [{ company_name: { [Op.like]: fieldsAndValues.search + "%" } }],
				roles: "COMPANYSUPERADMIN",
				group_id: groupId,


			},
			attributes: [
				"company_name",
				"group_id",
				"email",
				"company_id"
			],
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "created_at",
					fieldsAndValues.order_sequence
						? fieldsAndValues.order_sequence
						: "DESC",
				],
			],
		});
	}

	else {

		return await group.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				[Op.or]: [{ company_name: { [Op.like]: fieldsAndValues.search + "%" } }],
				roles: "COMPANYSUPERADMIN",
				group_id: groupId,


			},
			attributes: [
				"company_name",
				"group_id",
				"email",
				"company_id"
			],
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "created_at",
					fieldsAndValues.order_sequence
						? fieldsAndValues.order_sequence
						: "DESC",
				],
			],
		});
	}

}



exports.checkGroupExistenceModel = async (groupId) => {
	return await
		group.findOne({
			where: {
				group_id: groupId
			}
		})

}

exports.checkGroupUserExistenceModel = async (groupUserId) => {
	return await
		company.findOne({
			where: {
				company_id: groupUserId
			}
		})

}

exports.isGroupAssignModel = async (groupId) => {
	return await
		company.findAndCountAll({
			where: {
				group_id: groupId,
				roles: "COMPANYADMIN"
			}
		})
}

exports.removeGroupModel = async (groupId) =>
	await group.destroy({ where: { group_id: groupId } });

exports.removeGroupUserModel = async (groupId) =>
	await company.destroy({ where: { company_id: groupId } });

exports.getGroupModel = async (groupId) => await group.findByPk(groupId);


exports.editGroupModel = async (groupId, body) =>
	await group.update(
		{ name: body.name, make_account_id_mandatory: body.make_account_id_mandatory, pdf_time_stamp_checked: body.pdf_time_stamp_checked },
		{ where: { group_id: groupId } }
	);
