'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_type_stages',
        'old_scan_require',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stages',
        'remove_scan_require',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'old_scan_require',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_type_stage_for_shipments',
        'remove_scan_require',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_type_stages', 'old_scan_require');
      await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'remove_scan_require');
      await queryInterface.removeColumn('shipment_type_stages', 'old_scan_require');
      await queryInterface.removeColumn('shipment_type_stage_for_shipments', 'remove_scan_require');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
