
const { tag, company } = require("../../database/schemas");
const { Op, literal } = require("sequelize");


exports.createTagModel = async (request) => {
    return tag.create({
        name: request.name,
        tag_for: request.tag_for,
        color: request.color,
        company_id: request.company_id,
    });
}

exports.getTagListModel = async (fieldsAndValues, request) => {
    return await tag.findAndCountAll({
        limit: parseInt(fieldsAndValues.pageSize),
        offset:
            fieldsAndValues.page_no > 1
                ? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
                : 0,
        where: {
            company_id: request.company_id,
            [Op.or]: [
                { tag_for: { [Op.eq]: fieldsAndValues.search } },
                { tag_id: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
                { color: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
                { name: { [Op.like]: "%" + fieldsAndValues.search + "%" } }
            ],
        },
        attributes: COMMON_TAG_ATTRIBUTES,
        include: {
            model: company,
            as: "tag_company",
            attributes: ["company_name"]
        },
        order: [
            [
                fieldsAndValues.order_by_fields
                    ? fieldsAndValues.order_by_fields
                    : "created_at",
                fieldsAndValues.order_sequence
                    ? fieldsAndValues.order_sequence
                    : "ASC",
            ],
        ],
    })
}

exports.getTagListingForCustomerModel = async (request, body) => {

    const pageNo = request.page_no ? request.page_no : 1;
    const pageSize = request.page_size ? request.page_size : 10;
    const orderBy = request.order_by_fields ? request.order_by_fields : "created_at";
    const orderSequence = request.order_sequence ? request.order_sequence : "DESC";
    const search = request.search ? request.search : "";
    const status = request.filter ? request.filter : "active";
    pageNo > 1 ? (start = (pageNo - 1) * pageSize) : (start = 0);

    return await tag.findAll({
        where: {
            company_id: body.company_id,
            tag_for: "CUSTOMER",
            [Op.or]: [{ name: { [Op.like]: "%" + search + "%" } }],

        },
        limit: parseInt(pageSize),
        offset: parseInt(start),
        order: [[orderBy, orderSequence]]
    })
}

exports.getTagForShipmentListingModel = async (request, body) => {
    const pageNo = request.page_no ? request.page_no : 1;
    const pageSize = request.page_size ? request.page_size : 10;
    const orderBy = request.order_by_fields ? request.order_by_fields : "created_at";
    const orderSequence = request.order_sequence ? request.order_sequence : "DESC";
    const search = request.search ? request.search : "";
    const status = request.filter ? request.filter : "active";
    pageNo > 1 ? (start = (pageNo - 1) * pageSize) : (start = 0);

    return await tag.findAll({
        where: {
            company_id: body.company_id,
            tag_for: "SHIPMENT",
            [Op.or]: [{ name: { [Op.like]: "%" + search + "%" } }],
        },
        limit: parseInt(pageSize),
        offset: parseInt(start),
        order: [[orderBy, orderSequence]],
    })
}

