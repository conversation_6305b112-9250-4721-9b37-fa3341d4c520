APP_PORT = 8421
DB_PORT = 3306
DB_HOST = ************
DB_USER = mover_inventory
DB_PASS = SfqY3iLbm1opdAl
MYSQL_DB = mover_inventory
AWS_ACCESS_KEY_ID = ********************
AWS_SECRET_ACCESS_KEY = 4/qhcgRJK7B5h2gEC2ax9fpskmvFRt8wWwt2VNPt
AWS_BUCKET = openxcell-development-public
AWS_BASEURL = https://openxcell-development-public.s3.ap-south-1.amazonaws.com/
EMAIL_ID = <EMAIL>
EMAIL_PASSWORD = nhqpnhfcfryuaipv
NODE_ENV = development
CMS_URL = https://staging.moverinventory-cms.movegistics.com/mover-inventory-cms/
SUPER_ADMIN_COMPANY_ID = -1
JSREPORT_URL =  http://***********:5488/
JSREPORT_ADMIN = admin
JSREPORT_PASSWORD = oFQKJ8s33Qo9
MOVER_STORAGE_API_URL= https://storage-genie-api.apps.openxcell.dev/api/
GOOGLE_API_KEY = AIzaSyB-yGpC4YxIsgGFtmWp19CAJkza_XVFpN4
