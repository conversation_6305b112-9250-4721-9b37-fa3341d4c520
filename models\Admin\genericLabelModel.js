const {
	generic_label,
	batch_generic_label,
	shipment_job,
	shipment_inventory,
	company,
	sequelize,
	qr_code,
} = require("../../database/schemas");

const { Op } = require("sequelize");

exports.findCompanyLastGenericLabel = async (companyId) => {
	const data = await qr_code
		.findOne({
			where: {
				company_id: companyId,
				type: "Generic"
			},
			attributes: ["label_number"],
			order: [["label_number", "DESC"]],
			raw: true,
		});
	if (data !== null) {
		return data.label_number + 1
	}
	else {
		return 1
	}
}

exports.checkCompanyExistenceModel = async (companyId) => {
	const isCompany = await company
		.findOne({
			where: { company_id: companyId },
			raw: true,
		});
	if (isCompany !== null) {
		return true
	}
	else {
		return false
	}
}

checkValidBatchIdForLabel = async (batchIdArray) =>
	await batch_generic_label.findAll({
		where: {
			batch_id: {
				[Op.in]: batchIdArray,
			},
		}
	})


exports.checkLabelNumberAvailable = async (companyId, body) => {
	const startNumber = body.fromNumber
	const endNumber = body.fromNumber + body.quantity;
	const data = await qr_code
		.findAll({
			where: {
				company_id: companyId,
				type: "Generic",
				label_number: { [Op.between]: [startNumber, endNumber] }
			},
			attributes: [[sequelize.fn('DISTINCT', sequelize.col('batch_id')), 'batch_id']],
		});

	if (data.length > 0) {
		const batchIdArray = data.map((Batch) => Batch.batch_id);
		const checkData = await checkValidBatchIdForLabel(batchIdArray);
		if (checkData !== null && checkData !== undefined && checkData !== "") {
			return true
		}
		else {
			return false
		}
	}
	else {
		return false
	}
}


exports.findLastLabel = async (companyId) => {
	const data = await qr_code
		.findOne({
			where: {
				company_id: companyId,
				type: "Generic"
			},
			attributes: ["label_number"],
			order: [["label_number", "DESC"]],
			raw: true,
		});
	if (data !== null) {
		return data
	}
	else {
		return { label_number: 0 }
	}
}

exports.findLastBatch = async (companyId, formatDate) => {
	const data = await batch_generic_label
		.findOne({
			where: {
				company_id: companyId,
				date: formatDate
			},
			attributes: ["batch_number"],
			order: [["batch_number", "DESC"]],
			raw: true,
		});
	if (data !== null) {
		return data
	}
	else {
		return { batch_number: 0 }
	}
}


exports.createBatch = async (companyId, formatDate, batchNumber) => {
	return batch_generic_label
		.create({
			company_id: companyId,
			batch_number: batchNumber + 1,
			date: formatDate
		})
}

exports.checkAssignToItemsQR = async (companyId, batchId) =>
	await qr_code.findAll({
		where:
		{
			company_id: companyId,
			batch_id: batchId,
			type: "Generic",
		},
		attributes: [
			"job_id",
		],
	})



exports.listLabelModel = async (companyId, batchId, fieldsAndValues) =>
	await qr_code.findAndCountAll({
		limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
		offset:
			fieldsAndValues.page_no > 1
				? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
				: 0,
		where:
		{
			company_id: companyId,
			batch_id: batchId,
			type: "Generic",
			[Op.or]: [
				{ random_number: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
				{ label_number: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
			],
		}
		,
		attributes: [
			...COMMON_LABEL_ATTRIBUTES,
			[
				sequelize.literal(
					`(CASE WHEN qr_image IS NULL THEN '' ELSE CONCAT('${Const_AWS_BASE}', qr_image) END)`
				),
				"qr_image",
			],
			[
				sequelize.literal(
					`LPAD(label_number, 8, 0)`
				),
				"label_number",
			],
			[
				sequelize.literal(

					`(SELECT shipment_inventory_id FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
				),
				"item_id",
			],
			[
				sequelize.literal(
					`(CASE WHEN (SELECT shipment_inventory_id FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1) IS NULL THEN false ELSE true END)`
				),
				"isQrScanned",
			],
			[
				sequelize.literal(
					`(SELECT item_name FROM shipment_inventories si where qr_code.qr_code_id = si.qr_id limit 1)`
				),
				"item_name",
			],
		],
		order: [["label_number", "ASC"]],
	});

exports.deleteAllQrOfBatchModel = async (batchId) =>
	await qr_code.destroy({ where: { batch_id: batchId } });

exports.batchDeleteModel = async (batchId) =>
	await batch_generic_label.destroy({ where: { batch_id: batchId } });


exports.listBatchModel = async (companyId, fieldsAndValues) => {


	const checkLimit = fieldsAndValues.pageSize ? parseInt(fieldsAndValues.pageSize) : 10
	const checkOffset = fieldsAndValues.pageNo > 1
		? (parseInt(fieldsAndValues.pageNo) - 1) * fieldsAndValues.pageSize
		: 0
	return batch_generic_label.findAndCountAll({
		distinct: true,
		limit: checkLimit,
		offset: checkOffset,
		where:
		{
			company_id: companyId,
		},
		attributes: [
			"batch_id",
			"company_id",
			"batch_number",
			"created_at",
			"date",
			[
				sequelize.literal(
					"(select count(qr_code_id) FROM `qr_codes` where batch_id = batch_generic_label.batch_id and deletedAt IS NULL)"
				),
				"count",
			],
			[
				sequelize.literal(
					"(select count(qr_id) FROM `shipment_inventories` where qr_id in(select qr_code_id FROM `qr_codes` where batch_id = batch_generic_label.batch_id))"
				),
				"usedQrCount",
			],
		],
		include: [
			{
				model: qr_code,
				as: "batches",
				required: false,
				attributes: [
					"qr_code_id",
				],

			},
		],
		order: [["created_at", "DESC"]],
	});
}

exports.generateQrModel = async (qrArray) => await qr_code.bulkCreate(qrArray);



exports.getQrLabelModel = async (qrId) =>
	await qr_code.findOne({
		where: { qr_code_id: qrId, status: true },
		attributes: ["qr_image"],
	});


exports.getLabelDetailsDymo = async (qrId) =>
	await qr_code.findOne({
		where: { qr_code_id: qrId, status: true },
		attributes: ["qr_image", "random_number", "type"],
	});


exports.removeQrLabelModel = async (qrId) =>
	await qr_code.destroy({ where: { qr_code_id: qrId, status: true } });




exports.listGenericQrCodeLinksModel = async (companyId, batchId) => {
	const value = await qr_code
		.findAll({
			where: {
				company_id: companyId,
				status: true,
				type: 'Generic',
				batch_id: batchId,
			},
			attributes: [
				"qr_image",
				"random_number",
				[
					sequelize.literal(
						`LPAD(label_number, 8, 0)`
					),
					"label_number",
				],
			],
			include: [
				{
					model: shipment_job,
					as: "shipment_job",
					include: [
						{
							model: company,
							as: "job_company",
						},
					],
				},
			],
			order: [["label_number", "ASC"]],
		})

	if (value) {
		return JSON.parse(JSON.stringify(value, (k, v) => (v === null ? "" : v)))
	}
}

exports.listSingalGenericQrCodeLinksModel = async (companyId, qrId) => {
	const value = await qr_code
		.findAll({
			where: {
				company_id: companyId,
				status: true,
				type: 'Generic',
				qr_code_id: qrId,
			},
			attributes: [
				"qr_image",
				"random_number",
				[
					sequelize.literal(
						`LPAD(label_number, 8, 0)`
					),
					"label_number",
				],
			],
			include: [
				{
					model: shipment_job,
					as: "shipment_job",
					include: [
						{
							model: company,
							as: "job_company",
						},
					],
				},
			],
			order: [["label_number", "ASC"]],
		})

	if (value) {
		return JSON.parse(JSON.stringify(value, (k, v) => (v === null ? "" : v)))
	}
}