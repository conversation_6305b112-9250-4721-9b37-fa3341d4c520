"use strict";

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn("shipment_jobs", "source")
			await queryInterface.removeColumn("shipment_jobs", "opportunity_name")
			await queryInterface.removeColumn("shipment_jobs", "phone")
			await queryInterface.removeColumn("shipment_jobs", "phone2")
			await queryInterface.removeColumn("shipment_jobs", "opportunity_reference")
			await queryInterface.removeColumn("shipment_jobs", "wo_reference")
			await queryInterface.removeColumn("shipment_jobs", "account_reference")
			await queryInterface.removeColumn("shipment_jobs", "contact_reference")
			await queryInterface.removeColumn("shipment_jobs", "move_coordinator")
			return Promise.resolve()

		} catch (e) {
			return Promise.reject(e);
		}
	},

	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.addColumn("shipment_jobs", "source", {
				type: Sequelize.STRING(150),
				defaultValue: "",
			})
			await queryInterface.addColumn("shipment_jobs", "opportunity_name", {
				type: Sequelize.STRING,
				defaultValue: "",
			})
			await queryInterface.addColumn("shipment_jobs", "phone", {
				type: Sequelize.STRING,
				defaultValue: "",
			})
			await queryInterface.addColumn("shipment_jobs", "phone2", {
				type: Sequelize.STRING(15),
				defaultValue: "",
			})
			await queryInterface.addColumn("shipment_jobs", "opportunity_reference", {
				type: Sequelize.STRING,
				defaultValue: "",
			})
			await queryInterface.addColumn("shipment_jobs", "wo_reference", {
				type: Sequelize.STRING,
				defaultValue: "",
			})
			await queryInterface.addColumn("shipment_jobs", "account_reference", {
				type: Sequelize.STRING,
				defaultValue: "",
			})
			await queryInterface.addColumn("shipment_jobs", "contact_reference", {
				type: Sequelize.STRING,
				defaultValue: "",
			})
			await queryInterface.addColumn("shipment_jobs", "move_coordinator", {
				type: Sequelize.INTEGER(11),
				allowNull: true,
				onDelete: "CASCADE",
				onUpdate: "CASCADE",
				references: {
					model: "staffs",
					key: "staff_id",
				}
			})
			return Promise.resolve()

		} catch (e) {
			return Promise.reject(e);
		}
	},
};
