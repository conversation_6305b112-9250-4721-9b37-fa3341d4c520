'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_jobs',
        'created_by_type',
        {
          type: Sequelize.ENUM('company', 'staff'),
          defaultValue: 'company'
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_jobs',
        'created_by_id',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
        {
          logging: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('shipment_jobs', 'created_by_type');
      await queryInterface.removeColumn('shipment_jobs', 'created_by_id');
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
