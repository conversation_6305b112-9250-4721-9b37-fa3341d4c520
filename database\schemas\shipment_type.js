"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_type = sequelize.define(
		"shipment_type",
		{
			shipment_type_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			name: DataTypes.STRING,
			createdByUserRole: DataTypes.STRING,

			admin_id:
			{
				type: DataTypes.INTEGER,
			},
			company_id: {
				type: DataTypes.INTEGER,
			},
			staff_id: {
				type: DataTypes.INTEGER,
			},

			number_of_stages: DataTypes.INTEGER,
			signature_require: DataTypes.TINYINT,
			status: {
				type: DataTypes.ENUM("inactive", "active"),
				defaultValue: "active",
			},
			is_pickup_date_mandatory: DataTypes.TINYINT,
			is_make_user_mandatory: DataTypes.TINYINT,
			is_deleted: {
				type: DataTypes.TINYINT,
				defaultValue: 0,
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	shipment_type.associate = function (models) {
		// associations can be defined here

		shipment_type.hasMany(models.shipment_type_stage, {
			as: "shipment_stage",
			foreignKey: "shipment_type_id",
		});

		shipment_type.hasMany(models.shipment_job, {
			foreignKey: "shipment_type_id",
		});
	};
	return shipment_type;
};
