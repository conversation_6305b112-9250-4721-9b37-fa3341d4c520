const express = require("express");
const router = express();
const homeController = require("../controllers/APP/homeController");
const authentication = require("../middlewares/authentication");
const validators = require("../assets/validators");
const { errorHandler } = require("../assets/common");
const shipmentTypeController = require("../controllers/Admin/shipmentTypeController");
const shipmentController = require("../controllers/Admin/shipmentController");
const qrController = require("../controllers/Admin/qrCodeController");
const roomController = require("../controllers/Admin/roomController");
const tagController = require("../controllers/Admin/tagController");
const itemSuggestionController = require("../controllers/Admin/itemSuggestionController");

const multer = require("multer");
const {
  createRoomOnTheFlyController,
} = require("../controllers/Admin/roomController");
const upload = multer({ dest: "temp/" });

/**
 * @desc Api for fetch all shipments of staffs.
 * @method POST
 */

router
  .route("/storage/login")
  .post(
    upload.none(),
    validators.app.storageLogin,
    errorHandler,
    homeController.storageLogin
  );

router
  .route("/job-list")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.dateRequire,
    errorHandler,
    homeController.getJobList
  );

router
  .route("/unit-list")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.getUnitByJobId,
    errorHandler,
    homeController.getUnitList
  );

/**
 * @desc Api for fetch all qr of shipments for staffs.
 * @method POST
 */

router
  .route("/qr-job-list")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.dateRequire,
    errorHandler,
    homeController.getQrListForShipment
  );

router
  .route("/job-item-room-list-customer")
  .post(
    upload.none(),
    validators.admin.shipmentCheck,
    errorHandler,
    homeController.jobItemRoomListController
  );

/**
 * @desc Api for fetch all tag list of shipment items.
 * @method POST
 */

router
  .route("/job-item-tag-list-customer")
  .post(
    upload.none(),
    validators.admin.shipmentCheck,
    errorHandler,
    homeController.jobItemTagListController
  );

/**
 * @desc Api for fetch all qr of shipments for staffs.
 * @method POST
 */

router
  .route("/job-exception-list")
  .post(
    authentication.validateToken,
    upload.none(),
    errorHandler,
    homeController.exceptionListController
  );

/**
 * @desc Api for fetch all room list data edit item.
 * @method POST
 */

router
  .route("/room-list-edit-item")
  .get(authentication.validateToken, homeController.roomListEditItemController);

/**
 * @desc Api for fetch all room list of shipment items.
 * @method POST
 */

router
  .route("/job-item-room-list")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.admin.shipmentCheck,
    errorHandler,
    homeController.jobItemRoomListController
  );

/**
 * @desc Api for fetch all tag list of shipment items.
 * @method POST
 */

router
  .route("/job-item-tag-list")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.admin.shipmentCheck,
    errorHandler,
    homeController.jobItemTagListController
  );

/**
 * @desc Api for fetch all user list data edit item.
 * @method POST
 */

router
  .route("/user-list-edit-item")
  .get(authentication.validateToken, homeController.userListEditItemController);

/**
 * @desc Api for fetch all room list data edit item.
 * @method POST
 */

router
  .route("/unit-list-edit-item/:jobId")
  .get(authentication.validateToken, homeController.unitListEditItemController);

/**
 * @desc Api for fetch all shipments of staffs by name.
 * @method POST
 */

router
  .route("/job-list/byName")
  .post(
    authentication.validateToken,
    upload.none(),
    homeController.getJobListByName
  );

/**
 * @desc Api for generate inventory(item).
 * @method POST
 */

router
  .route("/add-item")
  .post(
    authentication.validateToken,
    upload.fields([
      { name: "photo" },
      { name: "photo1" },
      { name: "photo2" },
      { name: "photo3" },
      { name: "photo4" },
      { name: "photo5" },
      { name: "photo6" },
      { name: "photo7" },
      { name: "photo8" },
      { name: "photo9" },
    ]),
    validators.app.addItem,
    errorHandler,
    tagController.isValidTagController,
    homeController.checkAssignUnitValidationStorageAdd,
    qrController.isQrCodeUsedController,
    createRoomOnTheFlyController,
    itemSuggestionController.createItemSuggestionOnTheFlyController,
    homeController.addItem
  );

/**
 * @desc Api for edit inventory(item).
 * @method POST
 */

router
  .route("/edit-item")
  .post(
    authentication.validateToken,
    upload.fields([
      { name: "photo" },
      { name: "photo1" },
      { name: "photo2" },
      { name: "photo3" },
      { name: "photo4" },
      { name: "photo5" },
      { name: "photo6" },
      { name: "photo7" },
      { name: "photo8" },
      { name: "photo9" },
    ]),
    validators.app.editItem,
    errorHandler,
    tagController.isValidTagController,
    homeController.checkAssignUnitValidationStorageEdit,
    createRoomOnTheFlyController,
    itemSuggestionController.createItemSuggestionOnTheFlyController,
    homeController.editItem
  );

/**
 * @desc Api for edit inventory unit.
 * @method POST
 */

router
  .route("/edit-item-unit")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.editItemUnit,
    errorHandler,
    homeController.checkEditItemUnitValidation,
    homeController.editItemUnit
  );

/**
 * @desc Api for assign inventory item unit.
 * @method POST
 */

router
  .route("/assign-item-unit")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.assignitemUnit,
    errorHandler,
    homeController.isValidRandomNumberForAssignUnitController,
    homeController.checkEditItemUnitValidation,
    homeController.assignItemUnit
  );

/**
 * @desc Api for assign inventory item unit.
 * @method POST
 */

router
  .route("/bulk-assign/item-unit")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.bulkAssignItemUnit,
    errorHandler,
    homeController.isValidItemsForAssignUnitController,
    homeController.checkBulkAssignItemUnitValidation,
    homeController.assignBulkItemUnit
  );

router
  .route("/bulk-unassign/item-unit")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.bulkUnassignItemUnit,
    errorHandler,
    shipmentController.isValidShipmentController,
    shipmentController.isValidShipmentStageIdController,
    homeController.unassignBulkItemUnit
  );

/**
 * @desc Api for check item validation using random number
 * @method POST
 */

router
  .route("/check-items-validation-randomNumber/:shipmentId")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.checkItemsValidationUsingRandomNumber,
    errorHandler,
    shipmentController.isValidShipmentController,
    homeController.checkItemsValidationUsingRandomNumber
  );

/**
 * @desc Api for fetch jobsummary(shipment details).
 * @method POST
 */

router
  .route("/job-summary")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.getJobSummary,
    errorHandler,
    homeController.getJobSummary
  );

/**
 * @desc Api for check unit validation.
 * @method POST
 */

router
  .route("/check-unit-validation")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.checkUnitValidationInventory,
    errorHandler,
    homeController.checkUnitValidationInventory
  );

/**
 * @desc Api for generate shipment signature.
 * @method POST
 */

router.route("/add-signature").post(
  authentication.validateToken,
  upload.fields([{ name: "cust_signature" }, { name: "signature" }]),
  validators.app.addSignature,
  errorHandler,
  // homeController.addSignatureStorage,
  homeController.addSignature,
  shipmentController.changeShipmentStageController,
  homeController.getJobListController
);

/**
 * @desc Api for delete inventory(item) using inventory_id.
 * @method DELETE
 */

router
  .route("/remove-item/:inventory_id")
  .delete(
    authentication.validateToken,
    upload.none(),
    validators.app.removeItem,
    errorHandler,
    homeController.removeItem
  );

/**
 * @desc Api for scan inventory(item) for shipment.
 * @method PUT
 */

router
  .route("/job/:shipmentId/scan/:random_number/")
  .put(
    authentication.validateToken,
    upload.none(),
    validators.app.scanItemRandomNumber,
    errorHandler,
    shipmentController.isValidShipmentController,
    homeController.isValidInventoryForRandomNumberController,
    homeController.isInventoryOnJobForRandomNumberController,
    homeController.isPreOverriddenInventoryForRandomNumberController,
    homeController.isPreScannedInventoryForRandomNumberController,
    shipmentTypeController.isValidShipmentStageController,
    homeController.scanItemForRandomNumberController,
    homeController.isAllScannedForRandomNumberController
  );

/**
 * @desc Api for scan bulk inventory(item) for shipment.
 * @method PUT
 */

router
  .route("/job/bulk-scan-items")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.bulkscanitemsValidation,
    errorHandler,
    shipmentController.isValidShipmentController,
    shipmentTypeController.isValidShipmentStageCheckController,
    homeController.BulkScanItemsController,
    homeController.isAllBulkItemsScannedController
  );

/**
 * @desc Api for scan bulk inventory(item) for shipment.
 * @method PUT
 */

router
  .route("/bulk-Additional-items-scan")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.bulkAdditionalItemsScanValidation,
    errorHandler,
    shipmentController.isValidShipmentController,
    homeController.BulkAdditionalItemsScanController,
    homeController.isAllBulkAdditionalItemsScanController
  );

/**
 * @desc Api for fetch all inventory(item) scan details for shipment.
 * @method GET
 */

router
  .route("/job/:shipmentId/stage_item/:stageId")
  .get(
    authentication.validateToken,
    upload.none(),
    validators.app.scanState,
    errorHandler,
    shipmentController.isValidShipmentController,
    shipmentTypeController.isValidShipmentStageController,
    homeController.isAllScannedControllerCheck
  );

/**
 * @desc Api for override inventory(item) for shipment.
 * @method PUT
 */

router
  .route("/job/:shipmentId/override_scan/:inventoryId")
  .put(
    authentication.validateToken,
    upload.none(),
    validators.app.overrideScanState,
    errorHandler,
    shipmentController.isValidShipmentController,
    homeController.isValidInventoryController,
    homeController.isInventoryOnJobController,
    homeController.isPreScannedInventoryController,
    homeController.isPreOverriddenInventoryController,
    shipmentTypeController.isValidShipmentStageController,
    homeController.overrideItemController,
    homeController.isAllScannedController
  );

/**
 * @desc Api for generate inventory(item).
 * @method POST
 */

router
  .route("/shipment-inventory/add")
  .post(
    authentication.validateToken,
    upload.fields([
      { name: "photo" },
      { name: "photo1" },
      { name: "photo2" },
      { name: "photo3" },
      { name: "photo4" },
      { name: "photo5" },
      { name: "photo6" },
      { name: "photo7" },
      { name: "photo8" },
      { name: "photo9" },
    ]),
    validators.app.addShipmentInventoryMedia,
    errorHandler,
    homeController.isValidInventoryController,
    shipmentTypeController.isValidShipmentStageController,
    homeController.addShipmentInventoryMedia
  );

/**
 * @desc Api for fetch qr code using random_number.
 * @method GET
 */

router
  .route("/global-qr-code-search/:random_number")
  .get(
    authentication.validateToken,
    validators.app.globalQrCodeValidation,
    errorHandler,
    homeController.isValidQrCodeController,
    homeController.getQrCodeDetailsController
  );

/**
 * @desc Api for fetch qr code details using random_number for mover storage.
 * @method GET
 */

router
  .route("/global-qr-code-search-storage/:random_number")
  .get(
    authentication.validateToken,
    validators.app.globalQrCodeValidation,
    errorHandler,
    homeController.isValidQrCodeStorageController,
    homeController.getQrCodeDetailsForStorageController
  );

/**
 * @desc Api for qr code validation.
 * @method GET
 */

router
  .route("/checkQrCodeValidation/:random_number/job/:shipmentId")
  .get(
    authentication.validateToken,
    validators.app.checkQrCodeValidation,
    errorHandler,
    shipmentController.isCheckValidShipmentController,
    homeController.checkQrCodeValidationController,
    homeController.isQrCodeBelongToItemController
  );

/**
 * @desc Api for fetch all itemList using shipmentId.
 * @method GET
 */

router.route("/allItemsList/:shipmentId").get(
  // authentication.validateToken,
  validators.app.allItemsListValidation,
  errorHandler,
  shipmentController.isCheckValidShipmentController,
  homeController.allItemsListController
);

/**
 * @desc Api for fetch all itemList using shipmentId.
 * @method GET
 */

router
  .route("/allItemsList/cms")
  .post(
    upload.none(),
    validators.app.allItemsListValidationForCms,
    errorHandler,
    shipmentController.isCheckValidShipmentForCmsController,
    homeController.allItemsListForCmsController
  );

/**
 * @desc Api for fetch all itemList using shipmentId.
 * @method GET
 */

router
  .route("/allItemsList/cms-web")
  .post(
    upload.none(),
    validators.app.allItemsListValidationForCmsWeb,
    errorHandler,
    shipmentController.isCheckValidShipmentForCmsController,
    homeController.allItemsListForCmsWebController
  );

/**
 * @desc Api for fetch all itemList using byUnitId.
 * @method GET
 */

router
  .route("/itemsList/byUnitId")
  .post(
    validators.app.fetchAllUnitListByArray,
    errorHandler,
    authentication.validateToken,
    homeController.allItemsListByUnitIdController
  );

/**
 * @desc Api for fetch all itemList using Warehouse.
 * @method GET
 */

router
  .route("/itemsList/Warehouse")
  .post(
    validators.app.fetchAllItemListByWarehouse,
    errorHandler,
    authentication.validateToken,
    homeController.fetchAllItemListByWarehouse
  );

/**
 * @desc Api for fetch all itemHistory
 * @method post
 */

router
  .route("/itemsHistory")
  .post(
    authentication.validateToken,
    validators.app.itemsHistoryByArray,
    errorHandler,
    homeController.itemsHistoryByArrayController
  );

/**
 * @desc Api for fetch all itemHistory count using units
 * @method post
 */

router
  .route("/itemsHistoryCountByUnits")
  .post(
    authentication.validateToken,
    validators.app.itemsHistoryCountByUnits,
    errorHandler,
    homeController.itemsHistoryCountByUnitsController
  );

/**
 * @desc Api for fetch all itemHistory count using shipment
 * @method post
 */

router
  .route("/itemsHistoryCountByShipments")
  .post(
    authentication.validateToken,
    validators.app.itemsHistoryCountByShipments,
    errorHandler,
    homeController.itemsHistoryCountByShipmentsController
  );

/**
 * @desc Api for fetch all item count using unitId
 * @method post
 */

router
  .route("/itemsCount/byUnitId")
  .post(
    validators.app.fetchAllUnitListByArray,
    errorHandler,
    authentication.validateToken,
    homeController.allItemsCountByUnitIdController
  );

/**
 * @desc Api for fetch all item count validation using unitId and shipmentId
 * @method post
 */

router
  .route("/itemsCountValidation/byUnitId-ShipmentId")
  .post(
    upload.none(),
    validators.app.fetchAllUnitCountValidation,
    errorHandler,
    authentication.validateToken,
    homeController.fetchAllUnitCountValidation
  );

/**
 * @desc Api for fetch shipment data for mover storage
 * @method post
 */

router
  .route("/shipment/forStorgae/details")
  .post(
    validators.app.fetchShipmentArray,
    errorHandler,
    authentication.validateToken,
    homeController.shipmentListForStorageController
  );

/**
 * @desc Api for fetch all unit lists
 * @method post
 */

router
  .route("/fetchAllUnitList")
  .post(
    upload.none(),
    validators.app.fetchAllUnitListArray,
    errorHandler,
    homeController.createBulkUnitListController
  );

/**
 * @desc Api for item shuffle and reshuffle
 * @method post
 */

router
  .route("/shuffleAndReshuffle")
  .post(
    upload.none(),
    validators.app.shuffleAndReshuffleItem,
    errorHandler,
    homeController.shuffleAndReshuffleItemController
  );

/**
 * @desc Api for deliver shipment
 * @method post
 */

router
  .route("/deliver-shipment")
  .post(
    upload.none(),
    validators.app.deliverShipment,
    errorHandler,
    homeController.deliverShipmentController
  );

/**
 * @desc Api for add warehouse man info
 * @method post
 */

router.route("/add-warehouse-man").post(
  authentication.validateToken,
  upload.single("photo"),
  validators.app.addWarehouseMan,
  errorHandler,
  // homeController.checkValidationWarehouseMan,
  homeController.addWarehouseMan
);

router
  .route("/update_item_thumbnail")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.updateItemThumbnail,
    errorHandler,
    homeController.updateItemThumbnail
  );

router
  .route("/group-company-list")
  .post(
    authentication.validateToken,
    upload.none(),
    validators.app.companyListByGroup,
    errorHandler,
    homeController.companyListByGroup
  );

module.exports = router;
