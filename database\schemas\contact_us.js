"use strict";
module.exports = (sequelize, DataTypes) => {
	const contact_us = sequelize.define(
		"contact_us",
		{
			contact_us_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			name: DataTypes.STRING,
			email: DataTypes.STRING,
			message: DataTypes.TEXT,
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	contact_us.associate = function (models) {
		// associations can be defined here
	};
	return contact_us;
};
