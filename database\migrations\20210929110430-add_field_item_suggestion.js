"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn("item_suggestion", "volume", {
        type: Sequelize.INTEGER,
        allowNull: false,
      });
      await queryInterface.addColumn("item_suggestion", "weight", {
        type: Sequelize.INTEGER,
        allowNull: false,
      });
      await queryInterface.addColumn("item_suggestion", "status", {
        type: Sequelize.ENUM("inactive", "active"),
        defaultValue: "active",
      });
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("item_suggestion", "volume");
      await queryInterface.removeColumn("item_suggestion", "weight");
      await queryInterface.removeColumn("item_suggestion", "status");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};