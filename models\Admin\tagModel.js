const { tag, company, tag_customer, tag_shipment, tag_item } = require("../../database/schemas");
const { Op } = require("sequelize");
const StaffModel = require("../../models/Admin/staffModel");

exports.fetchTagListForCompnay = async (request) => {
	return await tag.findAll({
		where: {
			company_id: "-1",
		},
	});
}

exports.createTagListForCompnay = async (fetchTagListForCompnay, companyDetails) => {

	let newArrayData = [];
	for (let i = 0; i < fetchTagListForCompnay.length; i++) {
		let container = {};
		container.name = fetchTagListForCompnay[i].name;
		container.color = fetchTagListForCompnay[i].color;
		container.tag_for = fetchTagListForCompnay[i].tag_for;
		container.admin_id = null;
		container.company_id = companyDetails.company_id;
		container.staff_id = null;
		newArrayData.push(container)
	}

	return await tag.bulkCreate(newArrayData)
}

exports.getTagListingForCustomerModel = async (userDetails) => {
	if (userDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(userDetails.staff_id);
		return await tag.findAll({
			where: {
				company_id: getStaffDetails.company_id,
				tag_for: "CUSTOMER"
			}
		})
	}
	else {
		return await tag.findAll({
			where: {
				company_id: userDetails.company_id,
				tag_for: "CUSTOMER"
			}
		})
	}
}

exports.getTagListingForShipmentModel = async (userDetails) => {
	if (userDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(userDetails.staff_id);
		return await tag.findAll({
			where: {
				company_id: getStaffDetails.company_id,
				tag_for: "SHIPMENT"
			}
		})
	}
	else {
		return await tag.findAll({
			where: {
				company_id: userDetails.company_id,
				tag_for: "SHIPMENT"
			}
		})
	}
}

exports.getTagListingModel = async (fieldsAndValues, userDetails) => {
	if (userDetails.admin_id !== null) {

		return await tag.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				admin_id: {
					[Op.not]: null,
				},
				[Op.or]: [
					{ tag_for: { [Op.eq]: fieldsAndValues.search } },
					{ tag_id: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
					{ color: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
					{ name: { [Op.like]: "%" + fieldsAndValues.search + "%" } }
				],
			},
			attributes: COMMON_TAG_ATTRIBUTES,
			include: {
				model: company,
				as: "tag_company",
				attributes: ["company_name"]
			},
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "created_at",
					fieldsAndValues.order_sequence
						? fieldsAndValues.order_sequence
						: "ASC",
				],
			],
		})
	}


	else if (userDetails.staff_id !== null) {

		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(userDetails.staff_id);



		return await tag.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				[Op.or]: [
					{ staff_id: userDetails.staff_id },
					{ company_id: getStaffDetails.company_id },
				],
			},
			attributes: COMMON_TAG_ATTRIBUTES,
			include: {
				model: company,
				as: "tag_company",
				attributes: ["company_name"]
			},
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "created_at",
					fieldsAndValues.order_sequence
						? fieldsAndValues.order_sequence
						: "ASC",
				],
			],
		})
	}

	else {


		return await tag.findAndCountAll({
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				company_id: userDetails.company_id,
				[Op.or]: [
					{ tag_for: { [Op.eq]: fieldsAndValues.search } },
					{ tag_id: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
					{ color: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
					{ name: { [Op.like]: "%" + fieldsAndValues.search + "%" } }
				],
			},
			attributes: COMMON_TAG_ATTRIBUTES,
			include: {
				model: company,
				as: "tag_company",
				attributes: ["company_name"]
			},
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "created_at",
					fieldsAndValues.order_sequence
						? fieldsAndValues.order_sequence
						: "ASC",
				],
			],
		})
	}

}

exports.createTagModel = async (addingTags, getUserDetails) => {
	if (getUserDetails.admin_id !== null) {
		let createTag = await tag.create({
			name: addingTags.name,
			tag_for: addingTags.tag_for,
			color: addingTags.color,
			admin_id: getUserDetails.admin_id,
			company_id: "-1",
			staff_id: getUserDetails.staff_id,
		});
		return createTag;
	}

	else if (getUserDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);

		let createTag = await tag.create({
			name: addingTags.name,
			tag_for: addingTags.tag_for,
			color: addingTags.color,
			admin_id: getUserDetails.admin_id,
			company_id: getStaffDetails.company_id,
			staff_id: getUserDetails.staff_id,
		});
		return createTag;
	}

	else {
		let createTag = await tag.create({
			name: addingTags.name,
			tag_for: addingTags.tag_for,
			color: addingTags.color,
			admin_id: getUserDetails.admin_id,
			company_id: getUserDetails.company_id,
			staff_id: getUserDetails.staff_id,
		});
		return createTag;
	}



}

exports.editTagModel = async (tagId, updatingTags, getUserDetails) => {


	if (getUserDetails.admin_id !== null) {

		let updateTag = ({
			name: updatingTags.name,
			tag_for: updatingTags.tag_for,
			color: updatingTags.color,
			admin_id: getUserDetails.admin_id,
			company_id: "-1",
			staff_id: getUserDetails.staff_id,
		});
		let data = await tag.update(updateTag, { where: { tag_id: tagId } });

		return data;
	}

	else if (getUserDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);

		let updateTag = ({
			name: updatingTags.name,
			tag_for: updatingTags.tag_for,
			color: updatingTags.color,
			admin_id: getUserDetails.admin_id,
			company_id: getStaffDetails.company_id,
			staff_id: getUserDetails.staff_id,
		});
		let data = await tag.update(updateTag, { where: { tag_id: tagId } });

		return data;
	}

	else {
		let updateTag = ({
			name: updatingTags.name,
			tag_for: updatingTags.tag_for,
			color: updatingTags.color,
			admin_id: getUserDetails.admin_id,
			company_id: getUserDetails.company_id,
			staff_id: getUserDetails.staff_id,
		});
		let data = await tag.update(updateTag, { where: { tag_id: tagId } });

		return data;
	}

}

exports.getTagModel = async (tagId) => await tag.findByPk(tagId,
	{
		attributes: COMMON_TAG_ATTRIBUTES,
		include: {
			model: company,
			as: "tag_company",
			attributes: ["company_name"]
		},
	}
);

exports.removeTagModel = async (tagId) => await tag.destroy({ where: { tag_id: tagId } });

exports.checkTagExistenceModel = async (tagId) => {
	const isTag = await tag.findOne({
		where: { tag_id: tagId },
		attributes: ["tag_id"],
		raw: true
	})
	if (isTag !== null) {
		return true
	}
	else {
		return false
	}

}

exports.checkTagAssignModel = async (tagId, type) => {
	if (type == "CUSTOMER") {
		return await tag_customer.findAndCountAll({
			where: {
				tag_id: tagId,
			},
		});
	}
	else if (type == "SHIPMENT") {
		return await tag_shipment.findAndCountAll({
			where: {
				tag_id: tagId,
			},
		});
	}
	else {
		return await tag_item.findAndCountAll({
			where: {
				tag_id: tagId,
			},
		});
	}
};
