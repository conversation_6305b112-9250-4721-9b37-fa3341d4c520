'use strict';

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.renameColumn(
				'customers',
				'first_name',
				'customer_name',
				Sequelize.STRING
			);
			await queryInterface.removeColumn(
				'customers',
				'last_name'
			);
			await queryInterface.addColumn(
				"customers",
				'phone2',
				{
					type: Sequelize.STRING(15),
					allowNull: true
				});
			await queryInterface.addColumn(
				"customers",
				'phone3',
				{
					type: Sequelize.STRING(15),
					allowNull: true,
					defaultValue: ''
				});
			await queryInterface.addColumn(
				"customers",
				'email2',
				{
					type: Sequelize.STRING(55),
					allowNull: true,
					defaultValue: ''
				});
			await queryInterface.addColumn(
				'customers',
				'account_id',
				{
					type: Sequelize.STRING(15),
					allowNull: true,
					defaultValue: ''
				});
			await queryInterface.addColumn(
				'customers',
				'account_name',
				{
					type: Sequelize.STRING(15),
					allowNull: true,
					defaultValue: ''
				});
			await queryInterface.addColumn(
				'customers',
				'sales_rep',
				{
					type: Sequelize.STRING(120),
					allowNull: true,
				});
			return Promise.resolve()

		} catch (e) {
			return Promise.reject(e);
		}
	},
	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.renameColumn(
				'customers',
				'customer_name',
				'first_name'
			);
			await queryInterface.addColumn(
				'customers',
				'last_name',
				Sequelize.STRING(50)
			);
			await queryInterface.removeColumn(
				'customers',
				'phone2'
			);
			await queryInterface.removeColumn(
				'customers',
				'phone3'
			);
			await queryInterface.removeColumn(
				'customers',
				'email2'
			);
			await queryInterface.removeColumn(
				'customers',
				'account_id',
			);
			await queryInterface.removeColumn(
				'customers',
				'account_name');
			await queryInterface.removeColumn(
				'customers',
				'sales_rep',
			);
			return Promise.resolve()

		} catch (e) {
			return Promise.reject(e);
		}
	}
};
