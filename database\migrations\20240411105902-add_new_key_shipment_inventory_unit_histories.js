"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_inventory_unit_histories',
        'item_weight',
        {
          type: Sequelize.DECIMAL(11, 2),
          defaultValue: 0,
        },
      );
      await queryInterface.addColumn(
        'shipment_inventory_unit_histories',
        'add_stage_id',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );
      await queryInterface.addColumn(
        'shipment_inventory_unit_histories',
        'remove_stage_id',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_inventory_unit_histories", "item_weight");
      await queryInterface.removeColumn("shipment_inventory_unit_histories", "add_stage_id");
      await queryInterface.removeColumn("shipment_inventory_unit_histories", "remove_stage_id");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};