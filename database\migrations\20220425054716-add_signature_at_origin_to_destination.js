"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_type_stages",
        "supervisor_signature_require_at_origin_to_all_pages",
        {
          type: Sequelize.TINYINT,
          defaultValue: 0,
        }
      );
      await queryInterface.addColumn(
        "shipment_type_stages",
        "supervisor_signature_require_at_destination_to_all_pages",
        {
          type: Sequelize.TINYINT,
          defaultValue: 0,
        }
      );
      await queryInterface.addColumn(
        "shipment_type_stages",
        "customer_signature_require_at_origin_to_all_pages",
        {
          type: Sequelize.TINYINT,
          defaultValue: 0,
        }
      );
      await queryInterface.addColumn(
        "shipment_type_stages",
        "customer_signature_require_at_destination_to_all_pages",
        {
          type: Sequelize.TINYINT,
          defaultValue: 0,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_type_stages", "supervisor_signature_require_at_origin_to_all_pages");
      await queryInterface.removeColumn("shipment_type_stages", "supervisor_signature_require_at_destination_to_all_pages");
      await queryInterface.removeColumn("shipment_type_stages", "customer_signature_require_at_origin_to_all_pages");
      await queryInterface.removeColumn("shipment_type_stages", "customer_signature_require_at_destination_to_all_pages");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
