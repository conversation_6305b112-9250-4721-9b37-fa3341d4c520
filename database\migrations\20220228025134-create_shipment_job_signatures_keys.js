"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_job_signatures",
        "supervisor_signature_require_note_by_user",
        {
          type: Sequelize.STRING,
        }
      );
      await queryInterface.addColumn(
        "shipment_job_signatures",
        "customer_signature_require_note_by_user",
        {
          type: Sequelize.STRING,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_job_signatures", "supervisor_signature_require_note_by_user");
      await queryInterface.removeColumn("shipment_job_signatures", "customer_signature_require_note_by_user");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
