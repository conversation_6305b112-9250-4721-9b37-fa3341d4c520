"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_types",
        "createdByUserRole",
        {
          type: Sequelize.STRING,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_types", "createdByUserRole");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
