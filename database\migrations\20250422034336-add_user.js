"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_inventories',
        'make_disassembled_user_mandatory',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
      );
      await queryInterface.addColumn(
        'shipment_inventories',
        'make_carton_user_mandatory',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_inventories", "make_disassembled_user_mandatory");
      await queryInterface.removeColumn("shipment_inventories", "make_carton_user_mandatory");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};