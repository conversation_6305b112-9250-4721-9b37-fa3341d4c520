const {
	accesstoken,
	admin,
	authentication,
	staff,
	external_api,
	company,
	company_token
} = require("../database/schemas");
const constant = require("../assets/constant");
const commonFunction = require("../assets/common");
const jwt = require("jsonwebtoken");


/** 
 * validate compant api-key function
*/

exports.validateCompanyKey = async (req, res, next) => {
	try {
		const body = JSON.parse(JSON.stringify(req.body))
		const getCompanyKey = await company_token.findOne({
			where: {
				company_key: body.company_key
			}
		});
		if (getCompanyKey) {
			req.body.company_id = getCompanyKey.company_id
			next();
		}
		else {
			return res.status(NOT_FOUND_CODE).json({
				status: 0,
				message: VALID_COMPANY_KEY,
				data: {},
			});
		}
	}
	catch (error) {
		console.log("exports.validateCompanyKey -> error: ", error);
		return res.status(SERVER_ERROR_CODE).send({
			status: 0,
			message: SERVER_ERROR,
			data: {},
		});
	}
}

/** 
 * validate access token function
*/

exports.validateAccessToken = async (req, res, next) => {
	try {

		let access_token = req.headers.access_token || "";
		if (access_token) {
			if (/bearer/g.test(access_token)) {
				access_token = access_token.slice(7);
			}
			let decoded = await jwt.verify(access_token, JWTPRIVATEKEY);
			if (decoded && decoded.hasOwnProperty("user_id")) {

				let data = await accesstoken.findOne({
					where: {
						access_token: access_token,
						admin_id: decoded.user_id,
					},
					include: [
						{
							model: admin,
							required: true,
						},
					],
				});
				if (data != null) {

					req.user = { admin_id: decoded.user_id };
					return next();
				}
			}
			return res.status(UNAUTHORIZED_CODE).json({
				status: 0,
				message: UNAUTHORIZED_ACCESS,
				data: {},
			});
		} else {
			return res.status(NOT_VALID_DATA_CODE).send({
				status: 0,
				message: TOKEN_MISSING_ERROR,
				data: {},
			});
		}
	} catch (error) {

		if (error.name === "TokenExpiredError") {
			return res
				.status(UNAUTHORIZED_CODE)
				.send({ status: 0, message: UNAUTHORIZED_ACCESS, data: {} });
		} else {
			return res.status(SERVER_ERROR_CODE).send({
				status: 0,
				message: SERVER_ERROR,
				data: {},
			});
		}
	}
};


/** 
 * validate token function
*/

exports.validateToken = async (req, res, next) => {
	try {
		let access_token = req.headers["access_token"] || "access_token";

		let data = await commonFunction.jwtTokenVerify(access_token);

		if (data != 0) {
			let user = await accesstoken.findOne({
				where: {
					access_token: access_token,
				},
			});

			if (user !== null) {

				if (user.staff_id !== null) {
					let staff_roles = await staff.findOne({
						where: {
							roles: "ADMIN",
							staff_id: user.staff_id,
						},
					});
					if (staff_roles !== null) {
						req["company_id"] = staff_roles.company_id;
					}
				} else if (user.company_id !== null) {
					req["company_id"] = user.company_id;
				}
			} else {

				return res.status(UNAUTHORIZED_CODE).json({
					success: "0",
					message: UNAUTHORIZED_ACCESS,
					data: "Please provide valid token!",
				});
			}
			next();
		} else {
			return res.status(UNAUTHORIZED_CODE).json({
				success: "0",
				message: UNAUTHORIZED_ACCESS,
				data: "You dont have access!",
			});
		}
	} catch (err) {
		res
			.status(SERVER_ERROR_CODE)
			.json({ success: 0, data: {}, message: err.message });
	}
};


/** 
 * validate external token function
*/

exports.validateExternalToken = async (req, res, next) => {
	try {
		const access_token = req.headers["access_token"] || "access_token";

		let data = await commonFunction.jwtTokenVerify(access_token);

		if (data != 0) {
			let user = await external_api.findOne({
				where: {
					apiKey: access_token,
					status: "Active",
				},
			});

			if (user !== null) {

				next();
			} else {

				res.status(UNAUTHORIZED_CODE).json({
					success: "0",
					message: UNAUTHORIZED_ACCESS,
					data: "Please provide valid token!",
				});
			}

		} else {
			res.status(UNAUTHORIZED_CODE).json({
				success: "0",
				message: UNAUTHORIZED_ACCESS,
				data: "You dont have access!",
			});
		}
	} catch (err) {
		res
			.status(SERVER_ERROR_CODE)
			.json({ success: 0, message: err.message, data: {} });
	}
};

/** 
 * validate x-api-key function
*/

exports.validateAPIKey = async (req, res, next) => {
	try {
		let x_api_key = req.headers["x_api_key"];
		let data = await authentication.findOne({
			where: {
				auth_key: x_api_key,
			},
		});

		if (data != null) {
			next();
		} else {
			res.status(UNAUTHORIZED_CODE).json({
				message: UNAUTHORIZED_ACCESS,
				data: {},
			});
		}
	} catch (err) {
		res
			.status(SERVER_ERROR_CODE)
			.json({ success: 0, data: {}, message: err.message });
	}
};


/** 
 * validate validate API Key And Token function
*/

exports.validateAPIKeyAndToken = async (req, res, next) => {
	try {
		let x_api_key = req.headers["x_api_key"];
		let access_token = req.headers["access_token"];
		let device_token = req.headers["device_token"];
		let user_id = req.headers["user_id"];
		let version = req.headers["version"];
		let objAccesstokenModel = await accesstoken.findOne({
			where: {
				access_token: access_token,
				user_id: user_id,
			},
			include: [{ model: authentication, where: { auth_key: x_api_key } }],
		});

		if (objAccesstokenModel != null) {

			await objAccesstokenModel.update({
				device_token: device_token,
				version: version,
			});
			next();
		} else {
			res.status(UNAUTHORIZED_CODE).json({
				success: "0",
				message: UNAUTHORIZED_ACCESS,
				data: {},
			});
		}
	} catch (err) {
		res
			.status(SERVER_ERROR_CODE)
			.json({ success: 0, data: {}, message: err.message });
	}
};
