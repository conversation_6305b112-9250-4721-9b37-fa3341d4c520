const {
	shipment_type,
	shipment_type_stage,
	shipment_job,
	shipment_type_for_shipment,
	shipment_type_stage_for_shipment,
	sequelize
} = require("../../database/schemas");
const { Op, literal } = require("sequelize");
const StaffModel = require("../../models/Admin/staffModel");

exports.updateShipmentTypeStageOrderNumber = async (request) => {
	const data = await shipment_type_stage_for_shipment.update(
		{
			order_of_stages: request.order_of_stages + 1
		},
		{
			where: { local_shipment_stage_id: request.local_shipment_stage_id },
		}
	);

	return data
}

exports.jobPendingFlagUpdate = async (newShipmentCreatedStage, shipmentId) => {
	const updateData = await shipment_job.update(
		{
			is_job_complete_flag: 0,
			local_job_status: newShipmentCreatedStage.local_shipment_stage_id
		},
		{
			where: { shipment_job_id: shipmentId },
		});
	return updateData;
}

exports.findShipmentOrderStageDetails = async (request) => {
	return shipment_type_stage_for_shipment.findOne({
		where: {
			shipment_job_id: request.shipment_job_id,
			order_of_stages: request.order_of_stages
		}
	})
}

exports.updateNewStageAsScanOutOfStorage = async (stageId) => {
	const data = await shipment_type_stage_for_shipment.update(
		{
			scan_out_of_storage: 1,
			scan_require: 1,
		},
		{
			where: { local_shipment_stage_id: stageId },
		}
	);

	return data
}

exports.removeOldStageAsScanOutOfStorage = async (stageId) => {
	const data = await shipment_type_stage_for_shipment.update(
		{
			scan_out_of_storage: 0,
			scan_require: 0
		},
		{
			where: { local_shipment_stage_id: stageId },
		}
	);

	return data
}


exports.findAllStagesOfShipment = async (request) => {
	return shipment_job.findByPk(request.shipment_job_id, {
		attributes: [],
		include: [
			{
				model: shipment_type_for_shipment,
				as: "shipment_type_for_shipment",
				attributes: [
					"local_shipment_type_id",
					[
						sequelize.literal(
							"(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
						),
						"current_job_stage",
					],
					[
						sequelize.literal(
							"(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
						),
						"total_stages",
					],
				],
				include: [
					{
						model: shipment_type_stage_for_shipment,
						as: "local_shipment_stage",
						attributes: [
							"local_shipment_stage_id",
							"name",
							"order_of_stages",
							"why_supervisor_signature_require_note",
							"why_customer_signature_require_note",
							"PDF_time_require",
						],
						where: { status: "active" },
					},
				],
			},
		],
		order: [
			["shipment_type_for_shipment", "local_shipment_stage", "order_of_stages", "ASC"],
		]
	});
};



exports.fetchShipmentTypeForCompnay = async (request) => {
	return await shipment_type.findAll({
		where: {
			company_id: "-1",
			status: "active",
			is_deleted: "0",
		},
	});
}

exports.fetchShipmentTypeStagesForShipment = async (body) => {

	return await shipment_type_stage.findAll({

		where: {

			shipment_type_id: body.shipment_type_id,

		},

	});

}


exports.createShipmentTypeStagesForShipment = async (fetchShipmentTypeStagesForCompnay, createShipmentTypeForShipment) => {
	let newArrayData = [];
	for (let i = 0; i < fetchShipmentTypeStagesForCompnay.length; i++) {
		let container = {};

		container.name = fetchShipmentTypeStagesForCompnay[i].name;

		container.local_shipment_type_id = createShipmentTypeForShipment.local_shipment_type_id;

		container.shipment_job_id = createShipmentTypeForShipment.shipment_job_id;

		container.order_of_stages = fetchShipmentTypeStagesForCompnay[i].order_of_stages;

		container.status = fetchShipmentTypeStagesForCompnay[i].status;

		container.scan_require = fetchShipmentTypeStagesForCompnay[i].scan_require ? 1 : 0;

		container.remove_scan_require = fetchShipmentTypeStagesForCompnay[i].remove_scan_require ? 1 : 0;

		container.scan_into_storage = fetchShipmentTypeStagesForCompnay[i].scan_into_storage ? 1 : 0;

		container.scan_out_of_storage = fetchShipmentTypeStagesForCompnay[i].scan_out_of_storage ? 1 : 0;

		container.is_add_item = fetchShipmentTypeStagesForCompnay[i].is_add_item ? 1 : 0;

		container.is_add_exceptions = fetchShipmentTypeStagesForCompnay[i].is_add_exceptions ? 1 : 0;

		container.show_no_exceptions = fetchShipmentTypeStagesForCompnay[i].show_no_exceptions ? 1 : 0;

		container.PDF_time_require = fetchShipmentTypeStagesForCompnay[i].PDF_time_require ? 1 : 0;

		container.supervisor_signature_require = fetchShipmentTypeStagesForCompnay[i].supervisor_signature_require ? 1 : 0;;

		container.customer_signature_require = fetchShipmentTypeStagesForCompnay[i].customer_signature_require ? 1 : 0;;

		container.why_supervisor_signature_require_note = fetchShipmentTypeStagesForCompnay[i].why_supervisor_signature_require_note;

		container.why_customer_signature_require_note = fetchShipmentTypeStagesForCompnay[i].why_customer_signature_require_note;

		container.supervisor_signature_require_note_by_user = fetchShipmentTypeStagesForCompnay[i].supervisor_signature_require_note_by_user;

		container.customer_signature_require_note_by_user = fetchShipmentTypeStagesForCompnay[i].customer_signature_require_note_by_user;

		container.supervisor_signature_require_at_origin_to_all_pages = fetchShipmentTypeStagesForCompnay[i].supervisor_signature_require_at_origin_to_all_pages;

		container.supervisor_signature_require_at_destination_to_all_pages = fetchShipmentTypeStagesForCompnay[i].supervisor_signature_require_at_destination_to_all_pages;

		container.customer_signature_require_at_origin_to_all_pages = fetchShipmentTypeStagesForCompnay[i].customer_signature_require_at_origin_to_all_pages;

		container.customer_signature_require_at_destination_to_all_pages = fetchShipmentTypeStagesForCompnay[i].customer_signature_require_at_destination_to_all_pages;

		container.ref_shipment_stage_id = fetchShipmentTypeStagesForCompnay[i].shipment_stage_id;

		container.allow_default_manual_label = fetchShipmentTypeStagesForCompnay[i].allow_default_manual_label ? 1 : 0;

		container.add_items_to_inventory = fetchShipmentTypeStagesForCompnay[i].add_items_to_inventory ? 1 : 0;

		container.assign_storage_units_to_items = fetchShipmentTypeStagesForCompnay[i].assign_storage_units_to_items ? 1 : 0;

		container.unassign_storage_units_from_items = fetchShipmentTypeStagesForCompnay[i].unassign_storage_units_from_items ? 1 : 0;

		container.remove_items_to_inventory = fetchShipmentTypeStagesForCompnay[i].remove_items_to_inventory ? 1 : 0;

		container.enable_partial_complete_stage = fetchShipmentTypeStagesForCompnay[i].enable_partial_complete_stage ? 1 : 0;

		newArrayData.push(container)

	}
	return await shipment_type_stage_for_shipment.bulkCreate(newArrayData);

}





exports.createShipmentTypeForCompnay = async (fetchShipmentTypeForCompnay, companyDetails) => {

	let newArrayData = [];
	for (let i = 0; i < fetchShipmentTypeForCompnay.length; i++) {
		let container = {};
		container.name = fetchShipmentTypeForCompnay[i].name;
		container.admin_id = null;
		container.company_id = companyDetails.company_id;
		container.staff_id = null;
		container.number_of_stages = fetchShipmentTypeForCompnay[i].number_of_stages;
		container.is_pickup_date_mandatory = fetchShipmentTypeForCompnay[i].is_pickup_date_mandatory;
		container.is_make_user_mandatory = fetchShipmentTypeForCompnay[i].is_make_user_mandatory;
		container.signature_require = fetchShipmentTypeForCompnay[i].signature_require;
		container.status = fetchShipmentTypeForCompnay[i].status;
		container.is_deleted = fetchShipmentTypeForCompnay[i].is_deleted;
		newArrayData.push(container)
	}
	return await shipment_type.bulkCreate(newArrayData);
}


exports.fetchShipmentTypeStagesForCompnay = async (shipment_typeId) => {
	return await shipment_type_stage.findAll({
		where: {
			shipment_type_id: shipment_typeId,
			status: "active",
		},
	});
}

exports.shipmentTypeDetailLengthUpdate = async (shipmentTypeId, number) => {
	const data = await shipment_type.update(
		{
			number_of_stages: number
		},
		{
			where: { shipment_type_id: shipmentTypeId },
		}
	);

	return data
}

exports.updateStageOrderNumber = async (shipmentStageId, number) => {
	const data = await shipment_type_stage.update(
		{
			order_of_stages: number
		},
		{
			where: { shipment_stage_id: shipmentStageId },
		}
	);
	return data
}


exports.shipmentTypestagesupdate = async (request) => {
	const data = await shipment_type_for_shipment.update(
		{
			number_of_stages: request.order_of_stages
		},
		{
			where: { local_shipment_type_id: request.local_shipment_type_id },
		}
	);

	return data
}

exports.shipmentTypeStageCountUpdate = async (request, totalStages) => {
	const data = await shipment_type_for_shipment.update(
		{
			number_of_stages: totalStages + 1
		},
		{
			where: { local_shipment_type_id: request.local_shipment_type_id },
		}
	);

	return data
}

exports.shipmentJobCompleteFlagChange = async (request) => {
	const data = await shipment_job.update(
		{
			is_job_complete_flag: 0
		},
		{
			where: { shipment_job_id: request.shipment_job_id },
		}
	);

	return data
}

exports.addShipmentTypeStage = async (request) => {
	let shipmentTypeObj = {
		name: request.name,
		order_of_stages: request.order_of_stages,
		scan_require: request.scan_require,
		remove_scan_require: request.remove_scan_require,
		scan_into_storage: request.scan_into_storage,
		scan_out_of_storage: request.scan_out_of_storage,
		is_add_item: request.is_add_item,
		is_add_exceptions: request.is_add_exceptions,
		show_no_exceptions: request.show_no_exceptions,
		allow_default_manual_label: request.allow_default_manual_label,
		add_items_to_inventory: request.add_items_to_inventory,
		assign_storage_units_to_items: request.assign_storage_units_to_items,
		unassign_storage_units_from_items: request.unassign_storage_units_from_items,
		remove_items_to_inventory: request.remove_items_to_inventory,
		enable_partial_complete_stage: request.enable_partial_complete_stage,
		PDF_time_require: request.PDF_time_require,
		supervisor_signature_require: request.supervisor_signature_require,
		customer_signature_require: request.customer_signature_require,
		why_customer_signature_require_note: request.why_customer_signature_require_note,
		why_supervisor_signature_require_note: request.why_supervisor_signature_require_note,
		local_shipment_type_id: request.local_shipment_type_id,
		shipment_job_id: request.shipment_job_id,
		supervisor_signature_require_at_origin_to_all_pages: request.supervisor_signature_require_at_origin_to_all_pages && request.supervisor_signature_require_at_origin_to_all_pages,
		supervisor_signature_require_at_destination_to_all_pages: request.supervisor_signature_require_at_destination_to_all_pages && request.supervisor_signature_require_at_destination_to_all_pages,
		customer_signature_require_at_origin_to_all_pages: request.customer_signature_require_at_origin_to_all_pages && request.customer_signature_require_at_origin_to_all_pages,
		customer_signature_require_at_destination_to_all_pages: request.customer_signature_require_at_destination_to_all_pages && request.customer_signature_require_at_destination_to_all_pages,
	};
	return shipment_type_stage_for_shipment.create(shipmentTypeObj);
}


exports.createShipmentTypeStagesForCompnay = async (fetchShipmentTypeStagesForCompnay, shipmentId) => {
	let newArrayData = [];
	for (let i = 0; i < fetchShipmentTypeStagesForCompnay.length; i++) {
		let container = {};
		container.name = fetchShipmentTypeStagesForCompnay[i].name;
		container.shipment_type_id = shipmentId;
		container.order_of_stages = fetchShipmentTypeStagesForCompnay[i].order_of_stages;
		container.status = fetchShipmentTypeStagesForCompnay[i].status;
		container.scan_require = fetchShipmentTypeStagesForCompnay[i].scan_require ? 1 : 0;
		container.remove_scan_require = fetchShipmentTypeStagesForCompnay[i].remove_scan_require ? 1 : 0;
		container.scan_into_storage = fetchShipmentTypeStagesForCompnay[i].scan_into_storage ? 1 : 0;
		container.scan_out_of_storage = fetchShipmentTypeStagesForCompnay[i].scan_out_of_storage ? 1 : 0;
		container.is_add_item = fetchShipmentTypeStagesForCompnay[i].is_add_item ? 1 : 0;
		container.is_add_exceptions = fetchShipmentTypeStagesForCompnay[i].is_add_exceptions ? 1 : 0;
		container.show_no_exceptions = fetchShipmentTypeStagesForCompnay[i].show_no_exceptions ? 1 : 0;
		container.PDF_time_require = fetchShipmentTypeStagesForCompnay[i].PDF_time_require ? 1 : 0;
		container.supervisor_signature_require = fetchShipmentTypeStagesForCompnay[i].supervisor_signature_require ? 1 : 0;
		container.customer_signature_require = fetchShipmentTypeStagesForCompnay[i].customer_signature_require ? 1 : 0;
		container.why_supervisor_signature_require_note = fetchShipmentTypeStagesForCompnay[i].why_supervisor_signature_require_note;
		container.why_customer_signature_require_note = fetchShipmentTypeStagesForCompnay[i].why_customer_signature_require_note;
		container.supervisor_signature_require_note_by_user = fetchShipmentTypeStagesForCompnay[i].supervisor_signature_require_note_by_user;
		container.customer_signature_require_note_by_user = fetchShipmentTypeStagesForCompnay[i].customer_signature_require_note_by_user;
		container.supervisor_signature_require_at_origin_to_all_pages = fetchShipmentTypeStagesForCompnay[i].supervisor_signature_require_at_origin_to_all_pages;
		container.supervisor_signature_require_at_destination_to_all_pages = fetchShipmentTypeStagesForCompnay[i].supervisor_signature_require_at_destination_to_all_pages;
		container.customer_signature_require_at_origin_to_all_pages = fetchShipmentTypeStagesForCompnay[i].customer_signature_require_at_origin_to_all_pages;
		container.customer_signature_require_at_destination_to_all_pages = fetchShipmentTypeStagesForCompnay[i].customer_signature_require_at_destination_to_all_pages;

		container.allow_default_manual_label = fetchShipmentTypeStagesForCompnay[i].allow_default_manual_label ? 1 : 0
		container.add_items_to_inventory = fetchShipmentTypeStagesForCompnay[i].add_items_to_inventory ? 1 : 0
		container.assign_storage_units_to_items = fetchShipmentTypeStagesForCompnay[i].assign_storage_units_to_items ? 1 : 0
		container.unassign_storage_units_from_items = fetchShipmentTypeStagesForCompnay[i].unassign_storage_units_from_items ? 1 : 0
		container.remove_items_to_inventory = fetchShipmentTypeStagesForCompnay[i].remove_items_to_inventory ? 1 : 0
		container.enable_partial_complete_stage = fetchShipmentTypeStagesForCompnay[i].enable_partial_complete_stage ? 1 : 0

		newArrayData.push(container)
	}
	return await shipment_type_stage.bulkCreate(newArrayData);
}


exports.addShipmentType = async (request, getUserDetails) => {

	if (getUserDetails.admin_id !== null) {
		let shipmentTypeObj = {
			name: request.name && request.name,
			number_of_stages: request.number_of_stages,
			is_pickup_date_mandatory: request.is_pickup_date_mandatory,
			is_make_user_mandatory: request.is_make_user_mandatory,
			company_id: "-1",
			admin_id: getUserDetails.admin_id,
			staff_id: getUserDetails.staff_id,
			stages: request.stages && request.stages
		};


		let shipmentDetails = await shipment_type.create(shipmentTypeObj);


		if (
			shipmentDetails.shipment_type_id > 0 &&
			Object.keys(request.stages[0]).length !== 0
		) {
			let stagesArr = request.stages.map((result) => {
				return { ...result, shipment_type_id: shipmentDetails.shipment_type_id };
			});
			await shipment_type_stage.bulkCreate(
				stagesArr,
				{
					returning: true,
				}
			);
		}
		return shipmentDetails;
	}
	else if (getUserDetails.staff_id !== null) {

		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);

		let shipmentTypeObj = {
			name: request.name && request.name,
			number_of_stages: request.number_of_stages,
			is_pickup_date_mandatory: request.is_pickup_date_mandatory,
			is_make_user_mandatory: request.is_make_user_mandatory,
			company_id: getStaffDetails.company_id,
			admin_id: getUserDetails.admin_id,
			staff_id: getUserDetails.staff_id,
			stages: request.stages && request.stages
		};

		let shipmentDetails = await shipment_type.create(shipmentTypeObj);

		if (
			shipmentDetails.shipment_type_id > 0 &&
			Object.keys(request.stages[0]).length !== 0
		) {
			let stagesArr = request.stages.map((result) => {
				return { ...result, shipment_type_id: shipmentDetails.shipment_type_id };
			});


			await shipment_type_stage.bulkCreate(
				stagesArr,
				{
					returning: true,
				}
			);
		}
		return shipmentDetails;
	}
	else {
		let shipmentTypeObj = {
			name: request.name && request.name,
			number_of_stages: request.number_of_stages,
			is_pickup_date_mandatory: request.is_pickup_date_mandatory,
			is_make_user_mandatory: request.is_make_user_mandatory,
			company_id: getUserDetails.company_id,
			admin_id: getUserDetails.admin_id,
			staff_id: getUserDetails.staff_id,
			stages: request.stages && request.stages
		};

		let shipmentDetails = await shipment_type.create(shipmentTypeObj);

		if (
			shipmentDetails.shipment_type_id > 0 &&
			Object.keys(request.stages[0]).length !== 0
		) {
			let stagesArr = request.stages.map((result) => {
				return { ...result, shipment_type_id: shipmentDetails.shipment_type_id };
			});


			await shipment_type_stage.bulkCreate(
				stagesArr,
				{
					returning: true,
				}
			);
		}
		return shipmentDetails;
	}
};



exports.listShipmentType = async (request, userDetails) => {
	const status = request.filter ? request.filter : "active";
	const pageNo = request.pageNo ? request.pageNo : 1;
	const pageSize = request.pageSize ? request.pageSize : 10;
	const orderBy = request.orderBy ? request.orderBy : "created_at";
	const orderSequence = request.orderSequence ? request.orderSequence : "DESC";
	const search = request.search ? request.search : "";
	pageNo > 1 ? (start = (pageNo - 1) * pageSize) : (start = 0);

	if (userDetails.admin_id !== null) {
		return await
			shipment_type.findAndCountAll({
				where: {
					admin_id: {
						[Op.not]: null,
					},
					[Op.or]: [{ name: { [Op.like]: "%" + search + "%" } }],
					is_deleted: "0",
					status: status,
				},
				order: [[orderBy, orderSequence]],
				limit: pageSize,
				offset: start,
				distinct: true,
				include: status === "active"
					? [
						{
							model: shipment_type_stage,
							as: "shipment_stage",
							where: { status: "active" },
						},
					]
					: [],
			});
	}
	else if (userDetails.staff_id !== null) {

		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(userDetails.staff_id);

		return await shipment_type.findAndCountAll({
			where: {
				[Op.or]: [
					{ staff_id: userDetails.staff_id },
					{ company_id: getStaffDetails.company_id }
				],
				is_deleted: "0",
				status: status,
			},
			order: [[orderBy, orderSequence]],
			limit: pageSize,
			offset: start,
			distinct: true,
			include: status === "active"
				? [
					{
						model: shipment_type_stage,
						as: "shipment_stage",
						where: { status: "active" },
					},
				]
				: [],
		});
	}
	else {
		return await shipment_type.findAndCountAll({
			where: {
				company_id: userDetails.company_id,
				[Op.or]: [{ name: { [Op.like]: "%" + search + "%" } }],
				is_deleted: "0",
				status: status,
			},
			order: [[orderBy, orderSequence]],
			limit: pageSize,
			offset: start,
			distinct: true,
			include: status === "active"
				? [
					{
						model: shipment_type_stage,
						as: "shipment_stage",
						where: { status: "active" },
					},
				]
				: [],
		});

	}
};


exports.basicShipmentTypeModel = async (filter, userDetails) => {
	if (userDetails.admin_id !== null) {
		return await shipment_type.findAll({
			order: [["name"]],
			attributes: ["shipment_type_id", "name", "company_id", "is_pickup_date_mandatory"],
			where: {
				[Op.or]: {
					name: { [Op.like]: "%" + filter.search + "%" },
				},
				admin_id: {
					[Op.not]: null,
				},
				is_deleted: "0",
				status: "active"
			},
		});
	}

	else if (userDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(userDetails.staff_id);
		return await shipment_type.findAll({
			order: [["name"]],
			attributes: ["shipment_type_id", "name", "company_id", "is_pickup_date_mandatory"],
			where: {
				[Op.or]: {
					name: { [Op.like]: "%" + filter.search + "%" },
				},
				[Op.or]: [
					{ staff_id: userDetails.staff_id },
					{ company_id: getStaffDetails.company_id }
				],
				is_deleted: "0",
				status: "active"
			},
		});
	}
	else {
		return await shipment_type.findAll({
			order: [["name"]],
			attributes: ["shipment_type_id", "name", "company_id", "is_pickup_date_mandatory"],
			where: {
				[Op.or]: {
					name: { [Op.like]: "%" + filter.search + "%" },
				},
				company_id: userDetails.company_id,
				is_deleted: "0",
				status: "active"
			},
		});
	}
};

exports.ShipmentTypeListStorageModel = async (filter, body) => {
	return await shipment_type.findAll({
		order: [["name"]],
		attributes: ["shipment_type_id", "name", "company_id"],
		where: {
			[Op.or]: {
				name: { [Op.like]: "%" + filter.search + "%" },
			},
			company_id: body.company_id,
			is_deleted: "0",
			status: "active"
		},
	});
};

exports.isShipmentTypeModel = async (shipmentTypeId) => {
	const isType = await shipment_type
		.findOne({
			where: { shipment_type_id: shipmentTypeId },
			attributes: ["shipment_type_id"],
			raw: true,
		});
	if (isType !== null) {
		return true
	}
	else {
		return false
	}
}

exports.isShipmentStageModel = async (shipmentStageId) => {
	const isType = await shipment_type_stage_for_shipment
		.findOne({
			where: { local_shipment_stage_id: shipmentStageId },
			attributes: ["local_shipment_stage_id"],
		})
	if (isType !== null) {
		return true
	}
	else {
		return false
	}
}

exports.listShipmentTypeStage = async (request) => {
	const pageNo = request.pageNo ? request.pageNo : 1;
	const pageSize = request.pageSize ? request.pageSize : 10;
	const orderBy = request.orderBy ? request.orderBy : "created_at";
	const orderSequence = request.orderSequence ? request.orderSequence : "DESC";
	const search = request.search ? request.search : "";
	pageNo > 1 ? (start = (pageNo - 1) * pageSize) : (start = 0);
	return await shipment_type_stage.findAndCountAll({
		limit: pageSize,
		offset: start,
		where: {
			[Op.or]: [{ name: { [Op.like]: "%" + search + "%" } }],
			[Op.and]: [{ shipment_type_id: request.shipment_type_id }],
		},
		order: [[orderBy, orderSequence]],
		raw: true,
	});
};

exports.findShipmentType = async (request) => {
	return await shipment_type.findOne({
		where: { shipment_type_id: request.shipment_type_id },
	});
};
exports.updateShipmentType = async (request, getUserDetails) => {
	if (getUserDetails.admin_id !== null) {
		let updateObj = {
			name: request.name && request.name,
			number_of_stages: request.number_of_stages,
			is_pickup_date_mandatory: request.is_pickup_date_mandatory,
			is_make_user_mandatory: request.is_make_user_mandatory,
			company_id: "-1",
			admin_id: getUserDetails.admin_id,
			staff_id: getUserDetails.staff_id,
			stages: request.stages && request.stages
		};
		let updateShipmentType = await shipment_type.update(
			updateObj,
			{
				where: { shipment_type_id: request.shipment_type_id },
			}
		);

		if (request.stages && Object.keys(request.stages[0]).length !== 0) {
			let stagesArr = request.stages.map((result) => {
				return { ...result, shipment_type_id: request.shipment_type_id };
			});

			await shipment_type_stage.bulkCreate(
				stagesArr,
				{
					updateOnDuplicate: [
						"shipment_stage_id",
						"shipment_type_id",
						"name",
						"scan_require",
						"remove_scan_require",
						"scan_into_storage",
						"scan_out_of_storage",
						"is_add_item",
						"allow_default_manual_label",
						"add_items_to_inventory",
						"assign_storage_units_to_items",
						"unassign_storage_units_from_items",
						"remove_items_to_inventory",
						"enable_partial_complete_stage",
						"is_add_exceptions",
						"show_no_exceptions",
						"PDF_time_require",
						"supervisor_signature_require",
						"why_supervisor_signature_require_note",
						"customer_signature_require",
						"why_customer_signature_require_note",
						"supervisor_signature_require_at_origin_to_all_pages",
						"supervisor_signature_require_at_destination_to_all_pages",
						"customer_signature_require_at_origin_to_all_pages",
						"customer_signature_require_at_destination_to_all_pages",
						"order_of_stages",
					],
				},
				{
					returning: true,
				}
			);
		}
		return 1;
	}


	else if (getUserDetails.staff_id !== null) {

		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);

		let updateObj = {
			name: request.name && request.name,
			number_of_stages: request.number_of_stages,
			is_pickup_date_mandatory: request.is_pickup_date_mandatory,
			is_make_user_mandatory: request.is_make_user_mandatory,
			company_id: getStaffDetails.company_id,
			admin_id: getUserDetails.admin_id,
			staff_id: getUserDetails.staff_id,
			stages: request.stages && request.stages
		};
		let updateShipmentType = await shipment_type.update(
			updateObj,
			{
				where: { shipment_type_id: request.shipment_type_id },
			}
		);

		if (request.stages && Object.keys(request.stages[0]).length !== 0) {
			let stagesArr = request.stages.map((result) => {
				return { ...result, shipment_type_id: request.shipment_type_id };
			});

			await shipment_type_stage.bulkCreate(
				stagesArr,
				{
					updateOnDuplicate: [
						"shipment_stage_id",
						"shipment_type_id",
						"name",
						"scan_require",
						"remove_scan_require",
						"scan_into_storage",
						"scan_out_of_storage",
						"is_add_item",
						"allow_default_manual_label",
						"add_items_to_inventory",
						"assign_storage_units_to_items",
						"unassign_storage_units_from_items",
						"remove_items_to_inventory",
						"enable_partial_complete_stage",
						"is_add_exceptions",
						"show_no_exceptions",
						"PDF_time_require",
						"supervisor_signature_require",
						"why_supervisor_signature_require_note",
						"customer_signature_require",
						"why_customer_signature_require_note",
						"supervisor_signature_require_at_origin_to_all_pages",
						"supervisor_signature_require_at_destination_to_all_pages",
						"customer_signature_require_at_origin_to_all_pages",
						"customer_signature_require_at_destination_to_all_pages",
						"order_of_stages",
					],
				},
				{
					returning: true,
				}
			);
		}
		return 1;
	}


	else {
		let updateObj = {

			name: request.name && request.name,
			number_of_stages: request.number_of_stages,
			is_pickup_date_mandatory: request.is_pickup_date_mandatory,
			is_make_user_mandatory: request.is_make_user_mandatory,
			company_id: getUserDetails.company_id,
			admin_id: getUserDetails.admin_id,
			staff_id: getUserDetails.staff_id,
			stages: request.stages && request.stages
		};
		let updateShipmentType = await shipment_type.update(
			updateObj,
			{
				where: { shipment_type_id: request.shipment_type_id },
			}
		);

		if (request.stages && Object.keys(request.stages[0]).length !== 0) {
			let stagesArr = request.stages.map((result) => {
				return { ...result, shipment_type_id: request.shipment_type_id };
			});

			await shipment_type_stage.bulkCreate(
				stagesArr,
				{
					updateOnDuplicate: [
						"shipment_stage_id",
						"shipment_type_id",
						"name",
						"scan_require",
						"remove_scan_require",
						"scan_into_storage",
						"scan_out_of_storage",
						"is_add_item",
						"allow_default_manual_label",
						"add_items_to_inventory",
						"assign_storage_units_to_items",
						"unassign_storage_units_from_items",
						"remove_items_to_inventory",
						"enable_partial_complete_stage",
						"is_add_exceptions",
						"show_no_exceptions",
						"PDF_time_require",
						"supervisor_signature_require",
						"why_supervisor_signature_require_note",
						"customer_signature_require",
						"why_customer_signature_require_note",
						"supervisor_signature_require_at_origin_to_all_pages",
						"supervisor_signature_require_at_destination_to_all_pages",
						"customer_signature_require_at_origin_to_all_pages",
						"customer_signature_require_at_destination_to_all_pages",
						"order_of_stages",
					],
				},
				{
					returning: true,
				}
			);
		}
		return 1;
	}
};
exports.statusShipmentType = async (request) => {
	let updateStatus = await shipment_type.update(
		{
			status: literal(
				'CASE WHEN status = "active" THEN "inactive" ELSE "active" END'
			),
		},
		{ where: { shipment_type_id: request.shipment_type_id } }
	);
	await shipment_type_stage.update(
		{
			status: literal(
				'CASE WHEN status = "active" THEN "inactive" ELSE "active" END'
			),
		},
		{ where: { shipment_type_id: request.shipment_type_id } }
	);
	return 1;
};
exports.deleteShipmentType = async (request) => {
	let deleteType = await shipment_type.update(
		{
			is_deleted: literal("CASE WHEN is_deleted = 0 THEN 1 ELSE 0 END"),
		},
		{ where: { shipment_type_id: request.shipment_type_id } }
	);
	let deleteStage = await shipment_type_stage.update(
		{
			status: literal(
				'CASE WHEN status = "active" THEN "inactive" ELSE "active" END'
			),
		},
		{ where: { shipment_type_id: request.shipment_type_id } }
	);
	return 1;
};

exports.changeShipmentStageStatus = async (request) => {
	return await shipment_type_stage.update(
		{
			status: literal(
				'CASE WHEN status = "active" THEN "inactive" ELSE "active" END'
			),
		},
		{ where: { shipment_stage_id: request.shipment_stage_id } }
	);
};

exports.viewShipmentStage = async (shipment_stage_id) => {
	return await shipment_type_stage.findOne({
		where: { shipment_stage_id: shipment_stage_id }
	});
};

exports.viewShipmentType = async (shipment_type_id) => {
	return await shipment_type.findOne({
		where: { shipment_type_id: shipment_type_id },
		include: [
			{
				model: shipment_type_stage,
				as: "shipment_stage",
			},
		]
	});
};

exports.editShipmentStage = async (request) => {
	let shipmentStageObj = {
		name: request.name && request.name,
		order_of_stages: request.order_of_stages && request.order_of_stages,
		allow_default_manual_label: request.allow_default_manual_label && request.allow_default_manual_label,
		add_items_to_inventory: request.add_items_to_inventory && request.add_items_to_inventory,
		assign_storage_units_to_items: request.assign_storage_units_to_items && request.assign_storage_units_to_items,
		unassign_storage_units_from_items: request.unassign_storage_units_from_items && request.unassign_storage_units_from_items,
		remove_items_to_inventory: request.remove_items_to_inventory && request.remove_items_to_inventory,
		enable_partial_complete_stage: request.enable_partial_complete_stage && request.enable_partial_complete_stage,
		scan_require: request.scan_require && request.scan_require,
		remove_scan_require: request.remove_scan_require && request.remove_scan_require,
		scan_into_storage: request.scan_into_storage && request.scan_into_storage,
		scan_out_of_storage: request.scan_out_of_storage && request.scan_out_of_storage,
		is_add_item: request.is_add_item && request.is_add_item,
		is_add_exceptions: request.is_add_exceptions && request.is_add_exceptions,
		show_no_exceptions: request.show_no_exceptions && request.show_no_exceptions,
		PDF_time_require: request.PDF_time_require && request.PDF_time_require,
		supervisor_signature_require:
			request.supervisor_signature_require &&
			request.supervisor_signature_require,
		customer_signature_require:
			request.customer_signature_require && request.customer_signature_require,
		why_supervisor_signature_require_note: request.why_supervisor_signature_require_note && request.why_supervisor_signature_require_note,
		why_customer_signature_require_note: request.why_customer_signature_require_note && request.why_customer_signature_require_note,
		supervisor_signature_require_at_origin_to_all_pages: request.supervisor_signature_require_at_origin_to_all_pages && request.supervisor_signature_require_at_origin_to_all_pages,
		supervisor_signature_require_at_destination_to_all_pages: request.supervisor_signature_require_at_destination_to_all_pages && request.supervisor_signature_require_at_destination_to_all_pages,
		customer_signature_require_at_origin_to_all_pages: request.customer_signature_require_at_origin_to_all_pages && request.customer_signature_require_at_origin_to_all_pages,
		customer_signature_require_at_destination_to_all_pages: request.customer_signature_require_at_destination_to_all_pages && request.customer_signature_require_at_destination_to_all_pages,
		updated_at: new Date(),
	};

	return await shipment_type_stage.update(
		shipmentStageObj,
		{ where: { shipment_stage_id: request.shipment_stage_id } }
	);
};

exports.checkShipmentTypeAssignToJob = async (id) => {
	return await shipment_job.findAndCountAll({
		where: {
			shipment_type_id: id,
		},
	});
};

exports.updateOtherShipmentDetails = async (request, shipmentStageObj) => {
	return await shipment_type_stage.update(
		shipmentStageObj,
		{ where: { shipment_stage_id: request.shipment_stage_id } }
	);
};

exports.deleteShipmentTypeStageModel = async (stageId) =>
	await shipment_type_stage.destroy({ where: { shipment_stage_id: stageId } })

