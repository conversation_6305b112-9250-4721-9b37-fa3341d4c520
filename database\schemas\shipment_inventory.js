"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_inventory = sequelize.define(
		"shipment_inventory",
		{
			shipment_inventory_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			storage_unit_id: DataTypes.STRING(150),
			shipment_job_id: DataTypes.INTEGER,
			unit_id: DataTypes.INTEGER,
			inventory_stage_id: DataTypes.INTEGER,
			assign_to_storage_stage_id: DataTypes.INTEGER,
			remove_from_storage_stage_id: DataTypes.INTEGER,
			remove_from_inventory_stage_id: DataTypes.INTEGER,
			room_id: DataTypes.INTEGER,
			qr_id: DataTypes.INTEGER,
			item_name: DataTypes.STRING,
			description: DataTypes.STRING,
			is_carton: DataTypes.TINYINT,
			packed_by_owner: DataTypes.TINYINT,
			carrier_packed_user_id: DataTypes.INTEGER,
			carrier_packed_user_name_manually: DataTypes.STRING,
			disassembled_user_name_manually: DataTypes.STRING,
			prepared_user_name_manually: DataTypes.STRING,
			is_disassembled: DataTypes.TINYINT,
			isScannedFlag: DataTypes.TINYINT,
			disassembled_by_owner: DataTypes.TINYINT,
			disassembled_user_id: DataTypes.INTEGER,
			is_electronics: DataTypes.TINYINT,
			serial_number: DataTypes.STRING,
			is_high_value: DataTypes.TINYINT,
			declared_value: DataTypes.DECIMAL(11, 2),
			is_pro_gear: DataTypes.TINYINT,
			pro_gear_weight: DataTypes.DECIMAL(11, 2),
			volume: DataTypes.DECIMAL(11, 2),
			weight: DataTypes.DECIMAL(11, 2),
			pads_used: DataTypes.INTEGER,
			prepared_by: DataTypes.INTEGER,
			notes: DataTypes.TEXT,
			signature: DataTypes.TEXT,
			firmarm_serial_number: DataTypes.STRING,
			progear_name: DataTypes.STRING,
			is_firearm: DataTypes.TINYINT,
			isManualLabel: DataTypes.TINYINT,
			label_no: DataTypes.INTEGER,
			lot_no: DataTypes.STRING,
			color: DataTypes.STRING,
			seal_number: DataTypes.STRING,

			is_add_dimension: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			is_item_scanned_remove_from_storage: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			length: {
				type: DataTypes.DECIMAL(11, 2),
				allowNull: true,
				defaultValue: 0
			},
			width: {
				type: DataTypes.DECIMAL(11, 2),
				allowNull: true,
				defaultValue: 0
			},
			height: {
				type: DataTypes.DECIMAL(11, 2),
				allowNull: true,
				defaultValue: 0
			},
			is_additional_scan: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			is_remove_scan: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			make_disassembled_user_mandatory: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			make_carton_user_mandatory: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			is_item_assign_to_unit_completed: {
				type: DataTypes.TINYINT,
				allowNull: true,
				defaultValue: 0
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			deletedAt: {
				type: DataTypes.DATE
			}
		},
		{ createdAt: false, updatedAt: false, paranoid: true }
	);
	shipment_inventory.associate = function (models) {
		// associations can be defined here
		shipment_inventory.belongsTo(models.shipment_job, {
			foreignKey: "shipment_job_id",
		});

		shipment_inventory.belongsTo(models.unit_list, {
			foreignKey: "unit_id",
		});

		shipment_inventory.belongsTo(models.shipment_room, {
			as: "room",
			foreignKey: "room_id",
		});

		shipment_inventory.belongsTo(models.qr_code, {
			as: "item_qr",
			foreignKey: "qr_id",
		});

		shipment_inventory.belongsTo(models.staff, {
			as: "carrier_packed",
			foreignKey: "carrier_packed_user_id",
		});

		shipment_inventory.belongsTo(models.staff, {
			as: "disassembled_user",
			foreignKey: "disassembled_user_id",
		});

		shipment_inventory.belongsTo(models.staff, {
			as: "prepared_staff",
			foreignKey: "prepared_by",
		});

		shipment_inventory.hasMany(models.shipment_inventory_exception_note, {
			as: "exceptions",
			foreignKey: "shipment_inventory_id",
		});

		shipment_inventory.hasMany(models.shipment_inventory_exception, {
			foreignKey: "shipment_inventory_id",
		});

		shipment_inventory.hasMany(models.shipment_inventory_location, {
			foreignKey: "shipment_inventory_id",
		});

		shipment_inventory.hasMany(models.shipment_inventory_photo, {
			as: "item_photos",
			foreignKey: "shipment_inventory_id",
		});
		shipment_inventory.hasMany(models.shipment_inventory_thumbnail_photo, {
			as: "item_thumbnail_photo",
			foreignKey: "shipment_inventory_id",
		});

		shipment_inventory.hasMany(models.shipment_inventory_forced, {
			as: "forced_inventory",
			foreignKey: "shipment_inventory_id",
		});
		shipment_inventory.hasMany(models.shipment_inventory_job_scanned, {
			as: "scanned_inventory",
			foreignKey: "shipment_inventory_id",
		});

		shipment_inventory.hasMany(models.shipment_inventory_comments, {
			as: "comments",
			foreignKey: "shipment_inventory_id",
		});
		shipment_inventory.hasMany(models.shipment_inventory_unit_history, {
			as: "inventory_history",
			foreignKey: "shipment_inventory_id",
		});
		shipment_inventory.hasMany(models.tag_item, {
			as: "item_tag",
			foreignKey: "shipment_inventory_id",
		});
	};
	return shipment_inventory;
};
