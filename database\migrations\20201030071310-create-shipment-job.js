"use strict";
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable("shipment_jobs", {
      shipment_job_id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      job_number: {
        type: Sequelize.STRING(50),
        defaultValue: "",
      },
      shipment_type_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "shipment_types",
          key: "shipment_type_id",
        },
      },
      job_type: {
        type: Sequelize.STRING(150),
        defaultValue: "",
      },
      activity_type: {
        type: Sequelize.STRING(150),
        defaultValue: "",
      },
      source: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      warehouse: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      contact_name: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      opportunity_name: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      account_name: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      phone: {
        type: Sequelize.STRING(15),
        defaultValue: "",
      },
      phone2: {
        type: Sequelize.STRING(15),
        defaultValue: "",
      },
      phone3: {
        type: Sequelize.STRING(15),
        defaultValue: "",
      },
      email: {
        type: Sequelize.STRING,
      },
      email2: {
        type: Sequelize.STRING,
      },
      customer_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        references: {
          model: "customers",
          key: "customer_id",
        },
      },
      pickup_address: {
        type: Sequelize.TEXT,
        defaultValue: "",
      },
      pickup_city: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      pickup_state: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      pickup_country: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      pickup_zip: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      delivery_address: {
        type: Sequelize.TEXT,
        defaultValue: "",
      },
      delivery_city: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      delivery_state: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      delivery_country: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      delivery_zip: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      wo_reference: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      opportunity_reference: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      contact_reference: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      external_reference: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      pickup_date: {
        type: Sequelize.DATE,
      },
      delivery_date: {
        type: Sequelize.DATE,
      },
      sales_represent: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      move_coordinator: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      estimated_weight: {
        type: Sequelize.DECIMAL(11, 2),
        defaultValue: 0,
      },
      estimated_units: {
        type: Sequelize.DECIMAL(11, 2),
        defaultValue: 0,
      },
      term: {
        type: Sequelize.STRING,
        defaultValue: "",
      },
      frequent_access: {
        type: Sequelize.TINYINT,
        defaultValue: 1,
      },
      notes: {
        type: Sequelize.TEXT,
        defaultValue: "",
      },
      shipment_summary: {
        type: Sequelize.TEXT,
        defaultValue: "",
      },
      job_status: {
        type: Sequelize.ENUM("pending", "pickup", "progress", "delivered"),
        defaultValue: "pending",
      },
      total_items: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      total_cartons: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      total_cartons_cp: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      total_cartons_pbo: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      total_volume: {
        type: Sequelize.DECIMAL(11, 2),
        defaultValue: 0,
      },
      total_weight: {
        type: Sequelize.DECIMAL(11, 2),
        defaultValue: 0,
      },
      total_high_value: {
        type: Sequelize.DECIMAL(11, 2),
        defaultValue: 0,
      },
      total_pads_used: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      total_pro_gear_items: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      total_pro_gear_weight: {
        type: Sequelize.DECIMAL(11, 2),
        defaultValue: 0,
      },
      damaged_items: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      status: {
        type: Sequelize.ENUM("inactive", "active"),
        defaultValue: "active",
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: new Date(),
      },
    });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable("shipment_jobs");
  },
};
