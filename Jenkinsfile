project_name = "mover-inventory-api"
    development_branch = 'development'
    staging_branch = 'Preprod'
    production_branch = 'master'
    image_name = "${project_name}"
    container_name = "${image_name}" + "-${env.BRANCH_NAME}" + '-cont'
    user = 'ubuntu'
    cont_port = '8421'
    system_port = '8421'
    if (env.BRANCH_NAME == "${staging_branch}") {
        ip_address = '0.0.0.0'
        user = 'ubuntu'
        ssh_agent = "mover-inventory-live"
        app_env = "preproduction"
    }

    if (env.BRANCH_NAME == "${development_branch}") {
        ip_address = '************'
        user = 'ubuntu'
        ssh_agent = "mover-inventory-live"
        cont_port = '8421'
        system_port = '8421'
        app_env = ".env.staging"
    }

    if (env.BRANCH_NAME == "${production_branch}") {
        ip_address = '0.0.0.0'
        user = 'ubuntu'
        ssh_agent = "mover-inventory-live"
        app_env = "production"
    }

    pipeline {
        agent any
        
        options {
            buildDiscarder(logRotator(numToKeepStr: '7'))
        }

        stages {
            stage (build) {

                steps {
                    
                    sh "mv ${app_env} .env || exit 0"
                    sh "pwd && ls -la"
                    echo "Checkout Done.  Docker build started..."
                    sh "docker build --tag ${image_name} . "
                    echo "Docker build Done. Saving docker image now..."
                    sh "docker save -o ${image_name}.tar ${image_name}"
                    stash includes: '*.tar', name: 'dockerimage'
                }
            }

            stage (deploy) {

                steps {
                    unstash 'dockerimage'
                    script {
                        if (env.BRANCH_NAME == "${production_branch}") {

                            sshagent(["${ssh_agent}"]) {
                                sh "scp -o StrictHostKeyChecking=no ${project_name}.tar ${user}@${ip_address}:~/"
                                sh "ssh -o StrictHostKeyChecking=no ${user}@${ip_address} sudo docker rmi -f ${image_name} || exit 0"
                                sh "ssh -o StrictHostKeyChecking=no ${user}@${ip_address} sudo docker load -i /home/<USER>/${image_name}.tar"
                                sh "ssh -o StrictHostKeyChecking=no ${user}@${ip_address} sudo docker rm -f ${container_name} || exit 0"
                                sh "ssh -o StrictHostKeyChecking=no ubuntu@${ip_address} 'sudo docker run --restart always  -d -p ${system_port}:${cont_port} --name ${container_name} ${image_name}:latest'"
        
                            }
                        } else if ((env.BRANCH_NAME == "${staging_branch}") || (env.BRANCH_NAME == "${development_branch}")) {

                            sshagent(["${ssh_agent}"]) {
                                sh "scp -o StrictHostKeyChecking=no ${project_name}.tar ${user}@${ip_address}:~/"
                                sh "ssh -o StrictHostKeyChecking=no ${user}@${ip_address} sudo docker rmi -f ${image_name} || exit 0"
                                sh "ssh -o StrictHostKeyChecking=no ${user}@${ip_address} sudo docker load -i /home/<USER>/${image_name}.tar"
                                sh "ssh -o StrictHostKeyChecking=no ${user}@${ip_address} sudo docker rm -f ${container_name} | exit 0"
                                sh "ssh -o StrictHostKeyChecking=no ubuntu@${ip_address} 'sudo docker run --restart always  -d -p ${system_port}:${cont_port} --name ${container_name} ${image_name}:latest'"
        
                            }
                        }
                    }
                }
            }
        }
        post { 
            always { 
                cleanWs()
            }
        }


    }
