"use strict";
module.exports = (sequelize, DataTypes) => {
  const warehouse_man = sequelize.define(
    "warehouse_man",
    {
      warehouse_man_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      shipment_job_id: DataTypes.INTEGER,
      staff_id: DataTypes.INTEGER,
      unit_id: DataTypes.INTEGER,
      storage_unit_id: DataTypes.STRING,
      shipment_stage_id: DataTypes.INTEGER,
      warehouse_man_notes: DataTypes.STRING,
      warehouse_man_name: DataTypes.STRING,
      photo: DataTypes.TEXT,
      created_at: {
        type: DataTypes.DATE,
      },
      updated_at: {
        type: DataTypes.DATE,
      },
      deletedAt: {
        type: DataTypes.DATE
      }
    },
    { createdAt: false, updatedAt: false, paranoid: true }
  );
  warehouse_man.associate = function (models) {

  };
  return warehouse_man;
};
