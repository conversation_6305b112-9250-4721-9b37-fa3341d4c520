"use strict";
module.exports = (sequelize, DataTypes) => {
	const StaticPages = sequelize.define(
		"StaticPages",
		{
			page_id: {
				autoIncrement: true,
				primaryKey: true,
				type: DataTypes.INTEGER,
			},
			page_title: {
				type: DataTypes.STRING,
			},
			page_content: {
				type: DataTypes.TEXT("long"),
			},
			status: {
				type: DataTypes.ENUM,
				values: ["Active", "Inactive"],
				defaultValue: "Active",
			},
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{
			tableName: "static_pages",
			createdAt: false,
			updatedAt: false,
		}
	);
	StaticPages.associate = function (models) {
		// associations can be defined here
	};
	return StaticPages;
};
