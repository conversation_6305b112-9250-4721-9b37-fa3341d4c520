"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "item_suggestion",
        "admin_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );

      await queryInterface.addColumn(
        "item_suggestion",
        "company_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );

      await queryInterface.addColumn(
        "item_suggestion",
        "staff_id",
        {
          type: Sequelize.INTEGER,
          allowNull: true,
        }
      );
   
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("item_suggestion", "company_id");
      await queryInterface.removeColumn("item_suggestion", "admin_id");
      await queryInterface.removeColumn("item_suggestion", "staff_id");

      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
