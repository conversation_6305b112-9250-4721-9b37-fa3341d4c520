"use strict";
module.exports = (sequelize, DataTypes) => {
  const shipment_type_for_shipment = sequelize.define(
    "shipment_type_for_shipment",
    {
      local_shipment_type_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: DataTypes.STRING,
      createdByUserRole: DataTypes.STRING,
      shipment_job_id:
      {
        type: DataTypes.INTEGER,
      },
      ref_shipment_type_id:
      {
        type: DataTypes.INTEGER,
      },
      admin_id:
      {
        type: DataTypes.INTEGER,
      },
      company_id: {
        type: DataTypes.INTEGER,
      },
      staff_id: {
        type: DataTypes.INTEGER,
      },
      number_of_stages: DataTypes.INTEGER,
      signature_require: DataTypes.TINYINT,
      is_pickup_date_mandatory: DataTypes.TINYINT,
      is_make_user_mandatory: DataTypes.TINYINT,
      status: {
        type: DataTypes.ENUM("inactive", "active"),
        defaultValue: "active",
      },

      is_deleted: {
        type: DataTypes.TINYINT,
        defaultValue: 0,
      },
      created_at: {
        type: DataTypes.DATE,
        // defaultValue: Date.now()
      },
      updated_at: {
        type: DataTypes.DATE,
        // defaultValue: Date.now()
      },
    },
    { createdAt: false, updatedAt: false }
  );
  shipment_type_for_shipment.associate = function (models) {

    shipment_type_for_shipment.hasMany(models.shipment_type_stage_for_shipment, {
      as: "local_shipment_stage",
      foreignKey: "local_shipment_type_id",
    });

    shipment_type_for_shipment.hasMany(models.shipment_job, {
      foreignKey: "local_shipment_type_id",
    });
  };
  return shipment_type_for_shipment;
};
