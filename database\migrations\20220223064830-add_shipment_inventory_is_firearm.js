"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        "shipment_inventories",
        "is_firearm",
        {
          type: Sequelize.TINYINT,
          defaultValue: 0,
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_inventories", "is_firearm");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
