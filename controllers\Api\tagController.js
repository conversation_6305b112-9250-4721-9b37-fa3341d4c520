const tagModelApi = require("../../models/Api/tagModel");
const commonFunction = require("../../assets/common");


/**Post /tag/ createTag Controller */
exports.createTagController = async (request, response) => {
    try {
        //here we create tag
         const tagDetails = await tagModelApi.createTagModel(request.body)
        if (tagDetails)
            commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_ADDED_SUCCESS, tagDetails)
        else
            commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_ADDED_FAILURE, {})
    }
    catch (reason) {
		console.log("exports.createTagController -> error: ", reason);
        commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
    }
}

/**Post /tag/list getTagListing Controller */
exports.getTagListingController = async (request, response) => {
    try {
        request.query.search = request.query.search ? request.query.search : "";
        //here we fetch tagList
        const tagListing = await tagModelApi.getTagListModel(request.query, request.body)
        if (tagListing)
            commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_RETRIEVED_SUCCESS, tagListing)
        else
            commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_NOT_FOUND, {})
    }
    catch (reason) {
		console.log("exports.getTagListingController -> error: ", reason);
        commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
    }
}

/**Post /tag/list/customer getTagForCustomerListing Controller */
exports.getTagForCustomerListingController = async (request, response) => {
	try {
        //here we fetch tagList of customer
		const tagListing = await tagModelApi.getTagListingForCustomerModel(request.query,request.body)
		if (tagListing)
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_RETRIEVED_SUCCESS, tagListing)
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_NOT_FOUND, {})
	}
	catch (reason) {
		console.log("exports.getTagForCustomerListingController -> error: ", reason);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}

/**Post /tag/list/shipment getTagForShipmentListing Controller */
exports.getTagForShipmentListingController = async (request, response) => {
	try {
        //here we fetch tagList of shipment
		const tagListing = await tagModelApi.getTagForShipmentListingModel(request.query,request.body)
		if (tagListing)
			commonFunction.generateResponse(response, SUCCESS_CODE, 1, TAG_RETRIEVED_SUCCESS, tagListing)
		else
			commonFunction.generateResponse(response, SUCCESS_CODE, 0, TAG_NOT_FOUND, {})
	}
	catch (reason) {
		console.log("exports.getTagForShipmentListingController -> error: ", reason);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, reason, {})
	}
}