const express = require("express");
const router = express();
const authentication = require("../middlewares/authentication");
const validators = require("../assets/validators");
const { errorHandler } = require("../assets/common");
const homeControllerV2 = require("../controllers/APP/homeControllerV2")
const multer = require("multer");
const upload = multer({ dest: "temp/" });

router
  .route("/job-list")
  .post(
    authentication.validateToken,
    upload.none(),
    errorHandler,
    homeControllerV2.homeControllerV2
  );

module.exports = router;

