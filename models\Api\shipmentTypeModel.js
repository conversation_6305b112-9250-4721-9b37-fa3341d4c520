
const {
    shipment_type,
    shipment_type_for_shipment,
    shipment_type_stage_for_shipment,
} = require("../../database/schemas");
const { Op, literal } = require("sequelize");

exports.listShipmentType = async (request, body) => {

    const status = request.filter ? request.filter : "active";
    const pageNo = request.pageNo ? request.pageNo : 1;
    const pageSize = request.pageSize ? request.pageSize : 10;
    const orderBy = request.orderBy ? request.orderBy : "created_at";
    const orderSequence = request.orderSequence ? request.orderSequence : "DESC";
    const search = request.search ? request.search : "";
    pageNo > 1 ? (start = (pageNo - 1) * pageSize) : (start = 0);
    return await shipment_type.findAndCountAll({
        where: {
            company_id: body.company_id,
            [Op.or]: [{ name: { [Op.like]: "%" + search + "%" } }],
            is_deleted: "0",
            status: status,
        },
        order: [[orderBy, orderSequence]],
        limit: parseInt(pageSize),
        offset: parseInt(start),
    });
}


exports.getShipmentTypeStageList = async (body) => {
    return await shipment_type_stage_for_shipment.findAndCountAll({
        where: {
            shipment_job_id: body.shipment_job_id,
            status: "active",
        },
    });
}

exports.shipmentTypestagesupdate = async (request) => {
    const data = await shipment_type_for_shipment.update(
        {
            number_of_stages: request.count + 1
        },
        {
            where: { local_shipment_type_id: request.rows[0].local_shipment_type_id },
        }
    );
    return data
}

exports.addShipmentTypeStage = async (request, shipmentStage) => {

    let add_items_to_inventory = request.add_items_to_inventory === "true" || request.add_items_to_inventory === true ? 1 : 0;
    let assign_storage_units_to_items = request.assign_storage_units_to_items === "true" || request.assign_storage_units_to_items === true ? 1 : 0;
    let unassign_storage_units_from_items = request.unassign_storage_units_from_items === "true" || request.unassign_storage_units_from_items === true ? 1 : 0;
    let remove_items_to_inventory = request.remove_items_to_inventory === "true" || request.remove_items_to_inventory === "true" ? 1 : 0;

    if (add_items_to_inventory) {
        assign_storage_units_to_items = 0;
        unassign_storage_units_from_items = 0;
        remove_items_to_inventory = 0;
    }

    if (assign_storage_units_to_items) {
        add_items_to_inventory = 0;
        unassign_storage_units_from_items = 0;
        remove_items_to_inventory = 0;
    }

    if (unassign_storage_units_from_items) {
        add_items_to_inventory = 0;
        assign_storage_units_to_items = 0;
        remove_items_to_inventory = 0;
    }

    if (remove_items_to_inventory) {
        add_items_to_inventory = 0;
        assign_storage_units_to_items = 0;
        unassign_storage_units_from_items = 0;
    }

    let shipmentTypeObj = {
        name: request.name,
        order_of_stages: shipmentStage.count + 1,
        scan_require: request.scan_require === "true" ? 1 : 0,
        remove_scan_require: request.remove_scan_require === "true" ? 1 : 0,
        scan_into_storage: request.scan_into_storage === "true" ? 1 : 0,
        scan_out_of_storage: request.scan_out_of_storage === "true" ? 1 : 0,
        is_add_item: request.is_add_item === "true" ? 1 : 0,
        is_add_exceptions: request.is_add_exceptions === "true" ? 1 : 0,
        show_no_exceptions: request.show_no_exceptions === "true" ? 1 : 0,
        allow_default_manual_label: request.allow_default_manual_label === "true" ? 1 : 0,
        add_items_to_inventory,
        assign_storage_units_to_items,
        unassign_storage_units_from_items,
        remove_items_to_inventory,
        enable_partial_complete_stage: remove_items_to_inventory || unassign_storage_units_from_items ? request.enable_partial_complete_stage === "true" ? 1 : 0 : 0,
        PDF_time_require: request.PDF_time_require === "true" ? 1 : 0,
        supervisor_signature_require: request.supervisor_signature_require === "true" ? 1 : 0,
        customer_signature_require: request.customer_signature_require === "true" ? 1 : 0,
        why_customer_signature_require_note: request.why_customer_signature_require_note,
        why_supervisor_signature_require_note: request.why_supervisor_signature_require_note,
        local_shipment_type_id: shipmentStage.rows[0].local_shipment_type_id,
        shipment_job_id: request.shipment_job_id,
        supervisor_signature_require_at_origin_to_all_pages: request.supervisor_signature_require_at_origin_to_all_pages === "true" ? 1 : 0,
        supervisor_signature_require_at_destination_to_all_pages: request.supervisor_signature_require_at_destination_to_all_pages === "true" ? 1 : 0,
        customer_signature_require_at_origin_to_all_pages: request.customer_signature_require_at_origin_to_all_pages === "true" ? 1 : 0,
        customer_signature_require_at_destination_to_all_pages: request.customer_signature_require_at_destination_to_all_pages === "true" ? 1 : 0
    };

    return shipment_type_stage_for_shipment.create(shipmentTypeObj);
};



exports.editShipmentStage = async (request) => {
    let add_items_to_inventory = request.add_items_to_inventory === "true" ? 1 : 0;
    let assign_storage_units_to_items = request.assign_storage_units_to_items === "true" ? 1 : 0;
    let unassign_storage_units_from_items = request.unassign_storage_units_from_items === "true" ? 1 : 0;
    let remove_items_to_inventory = request.remove_items_to_inventory === "true" ? 1 : 0;

    if (add_items_to_inventory) {
        assign_storage_units_to_items = 0;
        unassign_storage_units_from_items = 0;
        remove_items_to_inventory = 0;
    }

    if (assign_storage_units_to_items) {
        add_items_to_inventory = 0;
        unassign_storage_units_from_items = 0;
        remove_items_to_inventory = 0;
    }

    if (unassign_storage_units_from_items) {
        add_items_to_inventory = 0;
        assign_storage_units_to_items = 0;
        remove_items_to_inventory = 0;
    }

    if (remove_items_to_inventory) {
        add_items_to_inventory = 0;
        assign_storage_units_to_items = 0;
        unassign_storage_units_from_items = 0;
    }

    let shipmentStageObj = {
        name: request.name,
        scan_require: request.scan_require === "true" ? 1 : 0,
        remove_scan_require: request.remove_scan_require === "true" ? 1 : 0,
        scan_into_storage: request.scan_into_storage === "true" ? 1 : 0,
        scan_out_of_storage: request.scan_out_of_storage === "true" ? 1 : 0,
        is_add_item: request.is_add_item === "true" ? 1 : 0,
        is_add_exceptions: request.is_add_exceptions === "true" ? 1 : 0,
        show_no_exceptions: request.show_no_exceptions === "true" ? 1 : 0,
        allow_default_manual_label: request.allow_default_manual_label === "true" ? 1 : 0,
        add_items_to_inventory,
        assign_storage_units_to_items,
        unassign_storage_units_from_items,
        remove_items_to_inventory,
        enable_partial_complete_stage: remove_items_to_inventory || unassign_storage_units_from_items ? request.enable_partial_complete_stage === "true" ? 1 : 0 : 0,
        PDF_time_require: request.PDF_time_require === "true" ? 1 : 0,
        supervisor_signature_require: request.supervisor_signature_require === "true" ? 1 : 0,
        customer_signature_require: request.customer_signature_require === "true" ? 1 : 0,
        why_customer_signature_require_note: request.why_customer_signature_require_note,
        why_supervisor_signature_require_note: request.why_supervisor_signature_require_note,
        shipment_job_id: request.shipment_job_id,
        supervisor_signature_require_at_origin_to_all_pages: request.supervisor_signature_require_at_origin_to_all_pages === "true" ? 1 : 0,
        supervisor_signature_require_at_destination_to_all_pages: request.supervisor_signature_require_at_destination_to_all_pages === "true" ? 1 : 0,
        customer_signature_require_at_origin_to_all_pages: request.customer_signature_require_at_origin_to_all_pages === "true" ? 1 : 0,
        customer_signature_require_at_destination_to_all_pages: request.customer_signature_require_at_destination_to_all_pages === "true" ? 1 : 0,
        updated_at: new Date(),

    };



    return await shipment_type_stage_for_shipment.update(
        shipmentStageObj,
        { where: { local_shipment_stage_id: request.local_shipment_stage_id } }
    );

};


exports.addShipmentType = async (request) => {
    let shipmentTypeObj = {
        name: request.name && request.name,
        number_of_stages: request.number_of_stages && request.number_of_stages,
        company_id: request.company_id && request.company_id,
        admin_id: null,
        staff_id: null,
        stages: request.stages && request.stages
    };

    let shipmentDetails = await shipment_type.create(shipmentTypeObj);

    if (
        shipmentDetails.shipment_type_id > 0 &&
        Object.keys(request.stages[0]).length !== 0
    ) {
        let stagesArr = request.stages.map((result) => {
            return { ...result, shipment_type_id: shipmentDetails.shipment_type_id };
        });


        await shipment_type_stage.bulkCreate(
            stagesArr,
            {
                returning: true,
            }
        );
    }
    return shipmentDetails;
}
