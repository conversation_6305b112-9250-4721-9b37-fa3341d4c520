const express = require("express");
const router = express();
const commonController = require("../controllers/Admin/commonController");
const customerController = require("../controllers/Admin/customerController");
const companyController = require("../controllers/Admin/companyController");
const tagController = require("../controllers/Admin/tagController");
const shipmentTypeController = require("../controllers/Admin/shipmentTypeController");
const shipmentController = require("../controllers/Admin/shipmentController");
const authentication = require("../middlewares/authentication");
const validators = require("../assets/validators");
const multer = require("multer");
const { errorHandler } = require("../assets/common");
const upload = multer({ dest: "temp/" });

/**
* @desc Api for fetch External Api Users.
* @method GET
*/

router
	.route("/list")
	.get(
		authentication.validateToken,
		upload.none(),
		commonController.lisExternalApiUsers
	);


/**
* @desc Api for generate user for External Api.
* @method POST
*/

router
	.route("/add_user")
	.post(
		authentication.validateToken,
		upload.none(),
		validators.admin.addApiUser,
		errorHandler,
		commonController.addExternalApiUser
	);

/**
* @desc Api for generate reload token.
* @method POST
*/

router
	.route("/reload_token")
	.post(
		upload.none(),
		validators.admin.reloadToken,
		errorHandler,
		commonController.reloadToken
	);

/**
* @desc Api for fetch customer list.
* @method GET
*/

router
	.route("/customer/list")
	.get(
		authentication.validateExternalToken,
		upload.none(),
		validators.admin.getCustomerList,
		customerController.getCustomerList
	);

/**
* @desc Api for generate customer.
* @method POST
*/

router
	.route("/customer/add")
	.post(
		authentication.validateExternalToken,
		upload.single("photo"),
		validators.admin.addCustomer,
		tagController.isValidTagController,
		customerController.addCustomer
	);

/**
* @desc Api for edit customer.
* @method POST
*/

router
	.route("/customer/edit-customer")
	.post(
		authentication.validateExternalToken,
		upload.single("photo"),
		validators.admin.editCustomer,
		tagController.isValidTagController,
		customerController.isValidCustomerController,
		customerController.editCustomer
	);

/**
* @desc Api for generate shipment.
* @method POST
*/

router
	.route("/shipment/add")
	.post(
		authentication.validateExternalToken,
		upload.none(),
		validators.admin.shipmentCreate,
		validators.admin.viewCompany,
		validators.admin.viewCustomer,
		errorHandler,
		customerController.isValidCustomerController,
		shipmentTypeController.isValidShipmentTypeController,
		tagController.isValidTagController,
		companyController.isValidCompanyController,
		shipmentController.createShipmentController
	);

/**
* @desc Api for upadte shipment using shipmentId.
* @method PUT
*/

router
	.route("/shipment/:shipmentId")
	.put(
		authentication.validateExternalToken,
		upload.none(),
		validators.admin.shipmentUpdate,
		errorHandler,
		shipmentController.isValidShipmentController,
		shipmentController.updateShipmentController
	);

/**
* @desc Api for fetch shipment-type list.
* @method GET
*/

router
	.route("/shipment-type/list")
	.get(
		authentication.validateExternalToken,
		upload.none(),
		shipmentTypeController.basicShipmentTypeController
	);

/**
* @desc Api for fetch company list.
* @method GET
*/

router
	.route("/company/list")
	.get(
		authentication.validateExternalToken,
		upload.none(),
		validators.admin.getCompanyList,
		companyController.getCompanyList
	);

/**
* @desc Api for fetch company using companyId.
* @method GET
*/

router
	.route("/company/view-company/:companyId")
	.get(
		authentication.validateExternalToken,
		upload.none(),
		validators.admin.viewCompanyGet,
		companyController.viewCompany
	);

/**
* @desc Api for fetch tag list.
* @method GET
*/

router
	.route("/tag/list")
	.get(
		authentication.validateExternalToken,
		upload.none(),
		validators.admin.shipmentList,
		errorHandler,
		tagController.getTagListingController
	);

module.exports = router;
