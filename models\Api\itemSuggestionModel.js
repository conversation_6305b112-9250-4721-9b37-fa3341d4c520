const { item_suggestion: item } = require("../../database/schemas");
const { Op, literal } = require("sequelize");



exports.createItemSuggestionModel = async (itemData) => {
    return item.create({
        name: itemData.item_name,
        volume: itemData.item_volume,
        weight: itemData.item_weight,
        admin_id: null,
        company_id: itemData.company_id,
        staff_id: null
    })
}

exports.getItemListingModel = async (fieldsAndValues, body) => {
    return await item.findAndCountAll({
        limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
        offset:
            fieldsAndValues.page_no > 1
                ? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
                : 0,
        where: {
            company_id: body.company_id,
            [Op.or]: [{ name: { [Op.like]: "%" + fieldsAndValues.search + "%" } }],
            status: fieldsAndValues.filter ? fieldsAndValues.filter : "active",
        },
        attributes: ["item_suggestion_id", "name", "volume", "weight", "status", "admin_id", "company_id", "staff_id"],
        order: [
            [
                fieldsAndValues.order_by_fields
                    ? fieldsAndValues.order_by_fields
                    : "created_at",
                fieldsAndValues.order_sequence
                    ? fieldsAndValues.order_sequence
                    : "DESC",
            ],
        ],
    });
}

