"use strict";
let bcrypt = require("bcrypt");

module.exports = (sequelize, DataTypes) => {
	const company = sequelize.define(
		"company",
		{
			company_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			storage_company_id: DataTypes.STRING(150),
			group_id: DataTypes.INTEGER,
			company_name: DataTypes.STRING(150),
			photo: DataTypes.TEXT,
			address1: DataTypes.STRING,
			address2: DataTypes.STRING,
			city: DataTypes.STRING,
			state: DataTypes.STRING,
			zipCode: DataTypes.STRING,
			country: DataTypes.STRING,
			phone: DataTypes.STRING(15),
			country_code: DataTypes.STRING(10),
			company_identity: DataTypes.STRING,
			email: DataTypes.STRING,
			password: DataTypes.STRING,
			notes: DataTypes.TEXT,
			push_notification: DataTypes.TINYINT,
			is_verified: DataTypes.TINYINT,
			verification_code: DataTypes.STRING,
			notificationStatus: DataTypes.TINYINT,

			status: {
				type: DataTypes.ENUM("inactive", "active"),
				defaultValue: "active",
			},
			roles: {
				type: DataTypes.ENUM("COMPANYADMIN","COMPANYSUPERADMIN","MAINADMIN"),
				defaultValue: "COMPANYADMIN",
			},
			is_deleted: { type: DataTypes.TINYINT, defaultValue: 0 },
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{
			createdAt: false,
			updatedAt: false,
			indexes: [
				{
					unique: true,
					fields: ["company_identity", "email"],
				},
			],
			hooks: {
				beforeCreate: (company) => {
					if (
						company.password &&
						company.password !== "" &&
						company.password !== "undefined"
					) {
						const salt = bcrypt.genSaltSync(10);
						company.password = bcrypt.hashSync(company.password, salt);
					}
				},
			},
		}
	);
	company.associate = function (models) {
		// associations can be defined here
		company.hasMany(models.staff, { foreignKey: "company_id" });
		company.hasMany(models.company_token, { foreignKey: "company_id" });
		company.hasMany(models.company_integration_key, { foreignKey: "company_id" });

		// company.hasMany(models.generic_label, {
		// 	as: "company_qr",
		// 	foreignKey: "company_id",
		// });
		company.hasMany(models.qr_code, {
			as: "company_qr",
			foreignKey: "company_id",
		});

		company.hasMany(models.batch_generic_label, {
			as: "company_batch",
			foreignKey: "company_id",
		});

		company.belongsTo(models.group, {
			as: "group_company",
			foreignKey: "group_id",
		});

		company.hasMany(models.shipment_job, { foreignKey: "company_id" });
	};
	return company;
};
