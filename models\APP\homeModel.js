const {
  staff,
  company,
  ccompany_integration_key,
  shipment_job,
  customer,
  shipment_job_assign_worker,
  shipment_job_assign_worker_list,
  shipment_room,
  shipment_exception,
  shipment_location,
  shipment_inventory,
  shipment_inventory_exception,
  shipment_inventory_location,
  shipment_inventory_photo,
  shipment_inventory_thumbnail_photo,
  shipment_inventory_exception_note,
  shipment_inventory_comments,
  qr_code,
  unit_list,
  shipment_job_signature,
  shipment_type_for_shipment,
  shipment_inventory_unit_history,
  shipment_type_stage_for_shipment,
  shipment_type_stage,
  shipment_inventory_forced,
  shipment_inventory_job_scanned,
  shipment_job_forced,
  shipment_type,
  warehouse_man,
  sequelize,
  tag_shipment,
  tag_customer,
  tag_item,
  tag,
  group,
} = require("../../database/schemas");
const { Op } = require("sequelize");
const moment = require("moment");

const StaffModel = require("../../models/Admin/staffModel");

exports.itemsHistoryCountByUnitsModel = async (request, shipmentId) => {
  return shipment_inventory_unit_history.findAndCountAll({
    where: {
      shipment_job_id: shipmentId,
      storage_unit_id: request,
      deletedAt: null,
    },
  });
};

exports.fetchStorageIditemsHistory = async (request) => {
  return shipment_job.findOne({
    where: {
      shipment_job_id: request,
    },
    attributes: ["shipment_job_id", "storage_shipment_job_id"],
  });
};

exports.itemsHistoryCountByShipmentsModel = async (request, unitId) => {
  return shipment_inventory_unit_history.findAndCountAll({
    where: {
      shipment_job_id: request,
      storage_unit_id: unitId,
      deletedAt: null,
    },
  });
};

exports.updateDeliverShipmentUnit = async (request) => {
  const updateData = await unit_list.update(
    { shipment_job_id: null, status: "empty" },
    {
      where: { storage_unit_id: request },
    }
  );
  return updateData;
};

exports.updateDeliverShipmentInventory = async (request, shipmentId) => {
  const updateData = await shipment_inventory.update(
    { storage_unit_id: null, unit_id: null },
    {
      where: {
        storage_unit_id: request,
        shipment_job_id: shipmentId,
      },
    }
  );
  return updateData;
};

exports.updateShipmentInventoryWithAssignUnitCompleted = async (stageId) => {
  try {
    const [affectedRows] = await shipment_inventory.update(
      { is_item_assign_to_unit_completed: true },
      {
        where: { assign_to_storage_stage_id: stageId },
      }
    );
    return affectedRows;
  } catch (error) {
    console.error(`Error updating shipment inventory for stageId ${stageId}:`, error);
    throw error;
  }
};


exports.itemsHistoryByArray = async (
  fieldsAndValues,
  unitArray,
  shipmentArray
) => {
  return shipment_inventory.findAll({
    where: {
      shipment_job_id: shipmentArray,
      [Op.or]: [
        { item_name: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
        { volume: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
        { weight: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
      ],
      deletedAt: null,
    },

    attributes: [
      ...COMMON_INVENTORY_ATTRIBUTES,
      [
        sequelize.literal(
          "(IF((select job_id from shipment_inventory_job_scanned where shipment_inventory_job_scanned.local_current_stage_id = (select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id) and shipment_inventory_job_scanned.shipment_inventory_id = shipment_inventory.shipment_inventory_id and shipment_inventory_job_scanned.shipment_job_id = shipment_inventory.shipment_job_id) is not null, 'yes', 'no'))"
        ),
        "isScanned",
      ],
      [
        sequelize.literal(
          "(select random_number FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "qr_generate_code",
      ],
      [
        sequelize.literal(
          "(select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id)"
        ),
        "status",
      ],
      [
        sequelize.literal(
          "(select type FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "type",
      ],

      [
        sequelize.literal(
          "(select LPAD(label_number,8,0) FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "label_number",
      ],
      [
        sequelize.literal(
          "coalesce((select name FROM `shipment_rooms` where shipment_room_id = room_id), '')"
        ),
        "room_name",
      ],

      [
        sequelize.literal(
          "(IF((select shipment_inventory_id from shipment_inventory_forced where shipment_inventory_forced.local_current_stage_id = (select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id) and shipment_inventory_forced.shipment_inventory_id = shipment_inventory.shipment_inventory_id) is not null, 'yes', 'no'))"
        ),
        "isOverride",
      ],
      [
        sequelize.literal(
          `(IF(packed_by_owner = '1', 'Packed by Owner', 'Carrier Packed'))`
        ),
        "packed_by",
      ],
      [
        sequelize.literal(
          `(IF(disassembled_by_owner = '1', 'By Customer', 'By Company'))`
        ),
        "disassembled_by",
      ],
      [
        sequelize.literal(`(CASE WHEN is_electronics = '1' THEN 1 ELSE 0 END)`),
        "is_electronics",
      ],
      [
        sequelize.literal(`(CASE WHEN is_firearm = '1' THEN 1 ELSE 0 END)`),
        "is_firearm",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_add_dimension = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_add_dimension",
      ],

      [
        sequelize.literal(`(CASE WHEN is_high_value = '1' THEN 1 ELSE 0 END)`),
        "is_high_value",
      ],

      [
        sequelize.literal(`(CASE WHEN is_pro_gear = '1' THEN 1 ELSE 0 END)`),
        "is_pro_gear",
      ],

      [
        sequelize.literal(`(CASE WHEN isManualLabel = '1' THEN 1 ELSE 0 END)`),
        "isManualLabel",
      ],

      [
        sequelize.literal(`(CASE WHEN is_carton = '1' THEN 1 ELSE 0 END)`),
        "is_carton",
      ],

      [
        sequelize.literal(
          `(CASE WHEN is_disassembled = '1' THEN 1 ELSE 0 END)`
        ),
        "is_disassembled",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.prepared_by)"
        ),
        "prepared_by_username",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.disassembled_user_id)"
        ),
        "disassembled_by_username",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.carrier_packed_user_id)"
        ),
        "packed_by_username",
      ],
    ],

    include: [
      {
        model: shipment_inventory_unit_history,
        attributes: ["shipment_inventory_id"],
        where: {
          storage_unit_id: unitArray,
          deletedAt: null,
        },
        as: "inventory_history",
      },
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
        ],
        required: false,
        as: "unit_list",
      },
      {
        model: shipment_inventory_comments,
        attributes: ["id", "shipment_inventory_id", "comment"],
        as: "comments",
      },
      {
        model: shipment_inventory_exception_note,
        attributes: [
          ["shipment_inventory_exception_note_id", "note_id"],
          "notes",
        ],
        required: false,
        as: "exceptions",
        include: [
          {
            model: shipment_inventory_exception,
            required: false,
            attributes: [
              "shipment_exception_id",
              [
                sequelize.literal(
                  "(select name FROM `shipment_exceptions` where shipment_exception_id = `exceptions->eid`.`shipment_exception_id`) "
                ),
                "exception_name",
              ],
            ],
            as: "eid",
            include: [
              {
                model: shipment_exception,
                attributes: [],
                required: true,
                as: "exception_list",
              },
            ],
          },
          {
            model: shipment_inventory_location,
            required: false,
            attributes: [
              "shipment_location_id",
              [
                sequelize.literal(
                  "(select name FROM `shipment_locations` where shipment_location_id = `exceptions->lid`.`shipment_location_id`) "
                ),
                "location_name",
              ],
            ],
            as: "lid",
            include: [
              {
                model: shipment_location,
                attributes: [],
                required: true,
                as: "location_list",
              },
            ],
          },
        ],
      },

      {
        model: shipment_inventory_forced,
        attributes: [
          "shipment_inventory_id",
          "current_stage_id",
          "local_current_stage_id",
          "shipment_job_id",
          "updated_at",
          "reason",
          "override_by_staff",
        ],
        as: "forced_inventory",
        include: [
          {
            model: staff,
            as: "staff",
            attributes: ["first_name", "last_name"],
          },
          {
            model: shipment_type_stage_for_shipment,
            attributes: ["name"],
            as: "shipment_type_stage_for_shipment",
          },
        ],
      },
      {
        model: shipment_inventory_job_scanned,
        attributes: [
          "current_stage_id",
          "local_current_stage_id",
          "shipment_inventory_id",
          "shipment_job_id",
          "created_at",
          "scanned_by_staff",
        ],
        as: "scanned_inventory",
        include: [
          {
            model: staff,
            as: "staff",
            attributes: ["first_name", "last_name"],
          },
          {
            model: shipment_type_stage_for_shipment,
            attributes: ["name"],
            as: "shipment_type_stage_for_shipment",
          },
        ],
      },
      {
        model: qr_code,
        attributes: [
          ...COMMON_QR_ATTRIBUTES,
          [
            sequelize.literal(`CONCAT('${Const_AWS_BASE}', qr_image)`),
            "qr_image",
          ],
          [
            sequelize.literal(
              "(select LPAD(label_number,8,0) FROM `qr_codes` where item_qr.qr_code_id =qr_codes.qr_code_id)"
            ),
            "label_number",
          ],
        ],
        required: false,
        as: "item_qr",
      },
      {
        model: shipment_room,
        as: "room",
        attributes: ["name"],
      },
      {
        model: staff,
        as: "prepared_staff",
        attributes: ["first_name", "last_name"],
      },
      {
        model: staff,
        as: "carrier_packed",
        attributes: ["first_name", "last_name"],
      },
      {
        model: staff,
        as: "disassembled_user",
        attributes: ["first_name", "last_name"],
      },
      {
        model: shipment_inventory_photo,
        attributes: [
          ["shipment_inventory_photo_id", "photo_id"],
          "description",
          "stage_id",
          "local_stage_id",

          "media",
          [
            sequelize.literal(
              `(CASE WHEN media IS NOT NULL AND local_stage_id is NULL AND stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', shipment_inventory.shipment_job_id, '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', media) END)`
            ),
            "item_photo",
          ],
        ],
        required: false,
        as: "item_photos",
        include: [
          {
            model: shipment_type_stage_for_shipment,
            attributes: ["name"],
            as: "shipment_type_stage_for_shipment",
          },
        ],
      },
      {
        model: shipment_inventory_thumbnail_photo,
        attributes: [
          ["shipment_inventory_photo_id", "photo_id"],
          "description",
          "stage_id2",
          "local_stage_id2",
          "mediaUrl",
          [
            sequelize.literal(
              `(CASE WHEN mediaUrl IS NOT NULL AND local_stage_id2 is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item_thumbnail}', shipment_inventory.shipment_job_id, '/original/', mediaUrl) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', mediaUrl) END)`
            ),
            "item_photo",
          ],
        ],
        required: false,
        as: "item_thumbnail_photo",
        include: [
          {
            model: shipment_type_stage_for_shipment,
            attributes: ["name"],
            as: "shipment_type_stage_for_shipment",
          },
        ],
      },
      {
        model: shipment_job,
        as: "shipment_job",
        attributes: [
          "shipment_job_id",
          "company_id",
          "job_status",
          "local_job_status",
        ],
      },
    ],
    limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 25,
    offset:
      fieldsAndValues.page_no > 1
        ? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
        : 0,
    order: [
      [
        fieldsAndValues.orderingField
          ? fieldsAndValues.orderingField
          : "created_at",
        fieldsAndValues.orderingWay ? fieldsAndValues.orderingWay : "DESC",
      ],
    ],
  });
};

exports.addItemHistory = async (request, inventory) => {
  return shipment_inventory_unit_history.create({
    shipment_inventory_id: inventory,
    shipment_job_id: request.job_id,
    qr_id: request.qr_id && request.qr_id,
    unit_id: request.unit_id,
    storage_unit_id: request.storage_unit_id,
    item_name: request.item_name,
  });
};

exports.editItemHistory = async (request) => {
  const updateData = await shipment_inventory_unit_history.update(
    {
      unit_id:
        request.unit_id == 0 ||
          request.unit_id == "0" ||
          request.unit_id == "null"
          ? null
          : request.unit_id,
      storage_unit_id: request.storage_unit_id,
      item_name: request.item_name,
      qr_id: request.qr_id && request.qr_id,
      item_weight: request.weight && request.weight,
    },
    {
      where: { shipment_inventory_id: request.inventory_id },
    }
  );
  return updateData;
};

exports.editItemHistoryStorgae = async (body) => {
  const updateData = await shipment_inventory_unit_history.update(
    {
      unit_id: body.unit_id,
      storage_unit_id: body.storage_unit_id,
      item_name: body.item_name,
      add_stage_id: body.stageId,
    },
    {
      where: { shipment_inventory_id: body.inventory_id },
    }
  );
  return updateData;
};

exports.editBulkItemHistoryStorgae = async (body, item) => {
  const updateData = await shipment_inventory_unit_history.update(
    {
      unit_id: body.unit_id,
      storage_unit_id: body.storage_unit_id,
      item_name: item.item_name,
      add_stage_id: body.stageId,
      item_weight: item.weight,
    },
    {
      where: { shipment_inventory_id: item.shipment_inventory_id },
    }
  );
  return updateData;
};

exports.addItemHistoryStorgae = async (body, findOldUnitOfItem) => {
  const updateData = await shipment_inventory_unit_history.create({
    unit_id: body.unit_id,
    storage_unit_id: body.storage_unit_id,
    item_name: findOldUnitOfItem.item_name,
    shipment_inventory_id: body.inventory_id,
    shipment_job_id: body.job_id,
    qr_id: findOldUnitOfItem.qr_id,
    item_weight: findOldUnitOfItem.weight,
    add_stage_id: body.stageId,
  });
  return updateData;
};

exports.addBulkItemHistoryStorgae = async (body, findOldUnitOfItem) => {
  const updateData = await shipment_inventory_unit_history.create({
    unit_id: body.unit_id,
    storage_unit_id: body.storage_unit_id,
    item_name: findOldUnitOfItem.item_name,
    shipment_inventory_id: findOldUnitOfItem.shipment_inventory_id,
    shipment_job_id: body.job_id,
    qr_id: findOldUnitOfItem.qr_id,
    item_weight: findOldUnitOfItem.weight,
    add_stage_id: body.stageId,
  });
  return updateData;
};

exports.findOldUnitOfBulkItemUsingShipmentId = async (body) => {
  return shipment_inventory.findAll({
    where: {
      shipment_job_id: body.shipment_id,
    },
    include: [
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
        ],
        required: false,
        as: "unit_list",
      },
    ],
  });
};

exports.findOldUnitOfBulkItemUsingShipmentIdWhichNotScanned = async (body) => {
  const whereClause = {
    shipment_job_id: body.shipment_id,
    isScannedFlag: 0,
    storage_unit_id: { [Op.is]: null },
  };

  if (body.search !== "" && body.search !== undefined && body.search !== null) {
    whereClause.item_name = { [Op.like]: `%${body.search}%` };
  }

  return shipment_inventory.findAll({
    where: whereClause,
    include: [
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
        ],
        required: false,
        as: "unit_list",
      },
    ],
  });
};

exports.findBulkAdditionalItemsToScanWhichNotScanned = async (body) => {
  const whereClause = {
    shipment_job_id: body.shipment_id,
    is_additional_scan: 0,
  };

  if (body.search !== "" && body.search !== undefined && body.search !== null) {
    whereClause.item_name = { [Op.like]: `%${body.search}%` };
  }

  return shipment_inventory.findAll({
    where: whereClause,
    attributes: ["shipment_inventory_id"],
  });
};

exports.findBulkRemoveItemsToScanWhichNotScanned = async (body) => {
  const whereClause = {
    shipment_job_id: body.shipment_id,
    storage_unit_id: { [Op.is]: null },
    is_remove_scan: 0,
  };

  if (body.search !== "" && body.search !== undefined && body.search !== null) {
    whereClause.item_name = { [Op.like]: `%${body.search}%` };
  }

  return shipment_inventory.findAll({
    where: whereClause,
    attributes: ["shipment_inventory_id"],
  });
};

exports.findOldUnitOfBulkItemUsingShipmentIdForUnassign = async (body) => {
  const whereClause = {
    shipment_job_id: body.shipment_id,
    storage_unit_id: { [Op.not]: null },
    isScannedFlag: 0,
  };

  if (body.search !== "" && body.search !== undefined && body.search !== null) {
    whereClause.item_name = { [Op.like]: `%${body.search}%` };
  }
  return shipment_inventory.findAll({
    where: whereClause,
    include: [
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
        ],
        required: false,
        as: "unit_list",
      },
    ],
  });
};

exports.findOldUnitOfBulkItemUsingShipment = async (body) => {
  const whereClause = {
    shipment_job_id: body.job_id,
    isScannedFlag: 0,
    is_item_assign_to_unit_completed: 0
  };

  if (body.search !== "" && body.search !== undefined && body.search !== null) {
    whereClause.item_name = { [Op.like]: `%${body.search}%` };
  }
  return shipment_inventory.findAll({
    where: whereClause,
    include: [
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
        ],
        required: false,
        as: "unit_list",
      },
    ],
  });
};

exports.findOldUnitOfBulkItem = async (body) => {
  return shipment_inventory.findAll({
    where: {
      shipment_inventory_id: { [Op.in]: body.itemArray },
      isScannedFlag: 0,
    },
    include: [
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
        ],
        required: false,
        as: "unit_list",
      },
    ],
  });
};

exports.findOldUnitOfBulkItemNotScanned = async (body) => {
  return shipment_inventory.findAll({
    where: {
      shipment_inventory_id: { [Op.in]: body.itemArray },
      isScannedFlag: 0,
      storage_unit_id: { [Op.is]: null },
    },
    include: [
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
        ],
        required: false,
        as: "unit_list",
      },
    ],
  });
};

exports.findBulkAdditionalItemsToScan = async (body) => {
  return shipment_inventory.findAll({
    where: {
      shipment_inventory_id: { [Op.in]: body.itemArray },
    },
    attributes: ["shipment_inventory_id"],
  });
};

exports.findOldUnitOfItem = async (body) => {
  return shipment_inventory.findOne({
    where: {
      shipment_inventory_id: body.inventory_id,
    },
    include: [
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
        ],
        required: false,
        as: "unit_list",
      },
    ],
  });
};

exports.findOldUnitOfBulkAssignItem = async (body) => {
  return shipment_inventory.findOne({
    where: {
      shipment_inventory_id: body.shipment_inventory_id,
    },
    include: [
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
        ],
        required: false,
        as: "unit_list",
      },
    ],
  });
};

exports.findNewUnitInfo = async (body) => {
  return unit_list.findOne({
    where: {
      unit_id: body.unit_id,
    },
    attributes: ["name"],
  });
};

exports.checkAssignUnitAvailableInInventory = async (body) => {
  return unit_list.findOne({
    where: {
      storage_unit_id: body.storage_unit_id,
    },
  });
};

exports.checkValidationWarehouseMan = async (body) => {
  return warehouse_man.findOne({
    where: {
      shipment_job_id: body.shipmentId,
      shipment_stage_id: body.stageId,
    },
  });
};

exports.fetchWarehouseManDetails = async (body) => {
  return warehouse_man.findOne({
    where: {
      warehouse_man_id: body.warehouse_man_id,
    },
    attributes: [
      "warehouse_man_id",
      "shipment_job_id",
      "staff_id",
      "unit_id",
      "shipment_stage_id",
      "shipment_stage_id",
      "warehouse_man_notes",
      "warehouse_man_name",
      "photo",
      [
        sequelize.literal(
          `(CASE WHEN warehouse_man.photo IS NULL THEN '' WHEN warehouse_man.photo NOT LIKE 'http%' THEN CONCAT('${Const_AWS_BASE_Warehouse_Man_Profile}', 'original/', warehouse_man.photo) ELSE warehouse_man.photo END)`
        ),
        "profile_photo",
      ],
    ],
  });
};

exports.addWarehouseMan = async (body, file) =>
  await warehouse_man.create({
    shipment_job_id: body.shipmentId,
    staff_id: body.staffId,
    unit_id: body.unitId,
    storage_unit_id: body.storageUnitId,
    shipment_stage_id: body.stageId,
    warehouse_man_notes: body.warehouse_man_notes,
    warehouse_man_name: body.warehouse_man_name,
    photo: file !== "" ? file : null,
  });

exports.getInventoryDetailsByLabelNo = async (InventoryId) => {
  return shipment_inventory.findOne({
    where: {
      shipment_inventory_id: InventoryId,
    },
  });
};

exports.checkAssignUnitAvailable = async (request) => {
  return unit_list.findOne({
    where: {
      storage_unit_id: request.storage_unit_id,
    },
  });
};

exports.assignUnitToShipment = async (request) => {
  const updateData = await unit_list.update(
    { shipment_job_id: request.job_id, status: "occupied" },
    {
      where: { storage_unit_id: request.storage_unit_id },
    }
  );
  return updateData;
};

exports.storageLogin = async (request) => {
  return staff.findOne({
    where: {
      email: request.email,
      company_id: "-1",
    },
  });
};

exports.getInventoryDetailsByLabelNumber = async (jobId, labelNumber) => {
  return shipment_inventory.findOne({
    where: {
      label_no: labelNumber,
      shipment_job_id: jobId,
    },
  });
};

exports.updateShipmentInventoryScanFlag = async (inventoryId) => {
  return shipment_inventory.update(
    {
      isScannedFlag: 1,
    },
    {
      where: {
        shipment_inventory_id: inventoryId,
      },
    }
  );
};

exports.updateShipmentInventoryScanFlagRemoveFromStorage = async (
  inventoryId
) => {
  return shipment_inventory.update(
    {
      is_item_scanned_remove_from_storage: 1,
    },
    {
      where: {
        shipment_inventory_id: inventoryId,
      },
    }
  );
};

exports.chnageScanItemStatusOfJob = async (jobId) => {
  return shipment_inventory.update(
    {
      isScannedFlag: 0,
    },
    {
      where: {
        shipment_job_id: jobId,
      },
    }
  );
};

exports.getInventoryByQrId = async (qrId) => {
  return shipment_inventory.findOne({
    where: {
      qr_id: qrId,
    },
  });
};

exports.isValidInventoryWithJobModel = async (inventoryId, JobId) => {
  return shipment_inventory.findOne({
    where: {
      shipment_inventory_id: inventoryId,
      shipment_job_id: JobId,
    },
  });
};

exports.checkAllItemArrayBelongToShipment = async (itemArray, JobId) => {
  return await shipment_inventory.findAll({
    where: {
      shipment_inventory_id: { [Op.in]: itemArray },
      shipment_job_id: JobId,
    },
    attributes: ["shipment_inventory_id", "shipment_job_id", "item_name"],
  });
};

exports.getInventoryByinventoryId = async (inventoryId) => {
  return shipment_inventory.findOne({
    where: {
      shipment_inventory_id: inventoryId,
    },
  });
};

exports.jobDetialsForAddItem = async (request) => {
  return await shipment_job.findOne({
    where: {
      shipment_job_id: request.job_id,
    },
    attributes: [
      "job_number",
      "job_status",
      "local_job_status",
      "shipment_name",
    ],
  });
};

exports.jobCompleteFlag = async (shipmentId) => {
  const updateData = await shipment_job.update(
    { is_job_complete_flag: 1 },
    {
      where: { shipment_job_id: shipmentId },
    }
  );
  return updateData;
};

exports.jobCompleteFlagFromStorage = async (shipmentId) => {
  const updateData = await shipment_job.update(
    {
      is_job_complete_flag: 1,
      storage_shipment_job_id: null,
      warehouseId: null,
    },
    {
      where: { shipment_job_id: shipmentId },
    }
  );
  return updateData;
};

exports.allItemsListJobDetailsModel = async (shipmentId) => {
  return shipment_job.findOne({
    attributes: [
      ...COMMON_JOB_ATTRIBUTES,

      [
        sequelize.literal(
          "(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_items",
      ],
      [
        sequelize.literal(
          "(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_volume",
      ],
      [
        sequelize.literal(
          "(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
        ),
        "firearms_total_quantity",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
        ),
        "total_cartons",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
        ),
        "total_cartons_cp",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
        ),
        "total_cartons_pbo",
      ],

      [
        sequelize.literal(
          '(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
        ),
        "total_high_value",
      ],

      [
        sequelize.literal(
          "(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_pads_used",
      ],

      [
        sequelize.literal(
          '(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_items",
      ],

      [
        sequelize.literal(
          "(select CONCAT(first_name,' ', last_name)  FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "customer_name",
      ],
      [
        sequelize.literal(
          "(select account_id FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "account_id",
      ],
      [
        sequelize.literal("(IF(shipment_name IS NULL, '', shipment_name))"),
        "shipment_name",
      ],
      [
        sequelize.literal(
          "(select name FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_status",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_item = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_item",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_exceptions",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN show_no_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "show_no_exceptions",
      ],
      [
        sequelize.literal(
          "(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_stage",
      ],
      [
        sequelize.literal(
          "(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
        ),
        "total_stages",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN order_of_stages = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_first_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN allow_default_manual_label = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "allow_default_manual_label",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN add_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "add_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN enable_partial_complete_stage = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "enable_partial_complete_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN allow_default_manual_label = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "allow_default_manual_label",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN add_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "add_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN enable_partial_complete_stage = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "enable_partial_complete_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_into_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_into_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "assign_storage_units_to_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "unassign_storage_units_from_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_out_of_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_out_of_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN supervisor_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_supervisor_signature_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN customer_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_customer_signature_require",
      ],

      [
        sequelize.literal(
          "(select why_supervisor_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_supervisor_signature_require_note",
      ],

      [
        sequelize.literal(
          "(select why_customer_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_customer_signature_require_note",
      ],
      [
        sequelize.literal(
          `(if(
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL) = 
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "1")
					,"yes","no"))`
        ),
        "isAllItemScanned",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`
        ),
        "is_job_complete",
      ],
    ],
    where: {
      shipment_job_id: shipmentId,
    },
    include: [
      {
        model: company,
        attributes: ["company_id"],
        required: true,
        as: "job_company",
        include: [
          {
            model: staff,
            where: { roles: "ADMIN", is_deleted: 0, status: "active" },
            attributes: [
              "staff_id",
              ["roles", "role"],
              "first_name",
              "last_name",
            ],
            required: true,
            as: "staffs",
          },
        ],
      },
      {
        model: customer,
        attributes: [],
        required: true,
        as: "customer_job",
      },
      {
        model: shipment_type_stage_for_shipment,
        attributes: [],
        required: true,
        as: "local_shipment_job_status",
      },
      {
        model: shipment_job_assign_worker_list,
        required: false,
        attributes: [
          "staff_id",
          "role",
          [
            sequelize.literal(
              "(select first_name FROM `staffs` where staff_id = assign_worker.staff_id) "
            ),
            "first_name",
          ],
          [
            sequelize.literal(
              "(select last_name FROM `staffs` where staff_id = assign_worker.staff_id) "
            ),
            "last_name",
          ],
        ],
        as: "assign_worker",
        include: [
          {
            model: staff,
            attributes: [],
            required: true,
            as: "assign_worker_detail",
          },
        ],
      },
    ],
  });
};

exports.allItemsListModel = async (fieldsAndValues, shipmentId) => {
  const include = [
    {
      model: tag_item,
      as: "item_tag",
      required: false,
      attributes: [
        COMMON_ITEM_TAG_ATTRIBUTES[0],
        [sequelize.literal("`item_tag->m2m_item_tag`.tag_id"), "tag_id"],
        [sequelize.literal("`item_tag->m2m_item_tag`.name"), "name"],
        [sequelize.literal("`item_tag->m2m_item_tag`.color"), "color"],
        [
          sequelize.literal("`item_tag->m2m_item_tag`.company_id"),
          "company_id",
        ],
      ],
      include: {
        // required: false,
        model: tag,
        as: "m2m_item_tag",
        attributes: [],
      },
    },
    {
      model: unit_list,
      attributes: [
        "unit_id",
        "name",
        "storage_unit_id",
        "unitCode",
        "currentLocation",
      ],
      required: false,
      as: "unit_list",
    },
    {
      model: shipment_inventory_comments,
      attributes: ["id", "shipment_inventory_id", "comment"],
      as: "comments",
    },
    {
      model: shipment_inventory_exception_note,
      attributes: [
        ["shipment_inventory_exception_note_id", "note_id"],
        "notes",
      ],
      required: false,
      as: "exceptions",
      include: [
        {
          model: shipment_inventory_exception,
          required: false,
          attributes: [
            "shipment_exception_id",
            [
              sequelize.literal(
                "(select name FROM `shipment_exceptions` where shipment_exception_id = `exceptions->eid`.`shipment_exception_id`) "
              ),
              "exception_name",
            ],
          ],
          as: "eid",
          include: [
            {
              model: shipment_exception,
              attributes: [],
              required: true,
              as: "exception_list",
            },
          ],
        },
        {
          model: shipment_inventory_location,
          required: false,
          attributes: [
            "shipment_location_id",
            [
              sequelize.literal(
                "(select name FROM `shipment_locations` where shipment_location_id = `exceptions->lid`.`shipment_location_id`) "
              ),
              "location_name",
            ],
          ],
          as: "lid",
          include: [
            {
              model: shipment_location,
              attributes: [],
              required: true,
              as: "location_list",
            },
          ],
        },
      ],
    },
    {
      model: shipment_inventory_forced,
      attributes: [
        "shipment_inventory_id",
        "current_stage_id",
        "local_current_stage_id",
        "shipment_job_id",
        "updated_at",
        "reason",
        "override_by_staff",
      ],
      as: "forced_inventory",
      include: [
        {
          model: staff,
          as: "staff",
          attributes: ["first_name", "last_name"],
        },
        {
          model: shipment_type_stage_for_shipment,
          attributes: ["name"],
          as: "shipment_type_stage_for_shipment",
        },
      ],
    },
    {
      model: shipment_inventory_job_scanned,
      attributes: [
        "current_stage_id",
        "local_current_stage_id",
        "shipment_inventory_id",
        "shipment_job_id",
        "created_at",
        "scanned_by_staff",
      ],
      as: "scanned_inventory",
      include: [
        {
          model: staff,
          as: "staff",
          attributes: ["first_name", "last_name"],
        },
        {
          model: shipment_type_stage_for_shipment,
          attributes: ["name"],
          as: "shipment_type_stage_for_shipment",
        },
      ],
    },
    {
      model: qr_code,
      attributes: [
        ...COMMON_QR_ATTRIBUTES,
        [
          sequelize.literal(`CONCAT('${Const_AWS_BASE}', qr_image)`),
          "qr_image",
        ],
        [
          sequelize.literal(
            "(select LPAD(label_number,8,0) FROM `qr_codes` where item_qr.qr_code_id =qr_codes.qr_code_id)"
          ),
          "label_number",
        ],
      ],
      required: false,
      as: "item_qr",
    },
    {
      model: shipment_room,
      as: "room",
      attributes: ["name"],
    },
    {
      model: staff,
      as: "prepared_staff",
      attributes: ["first_name", "last_name"],
    },
    {
      model: staff,
      as: "carrier_packed",
      attributes: ["first_name", "last_name"],
    },
    {
      model: staff,
      as: "disassembled_user",
      attributes: ["first_name", "last_name"],
    },
    {
      model: shipment_inventory_photo,
      attributes: [
        ["shipment_inventory_photo_id", "photo_id"],
        "description",
        "stage_id",
        "local_stage_id",

        "media",
        [
          sequelize.literal(
            `(CASE WHEN media IS NOT NULL AND local_stage_id is NULL AND stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', shipment_inventory.shipment_job_id, '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', media) END)`
          ),
          "item_photo",
        ],
      ],
      required: false,
      as: "item_photos",
      include: [
        {
          model: shipment_type_stage_for_shipment,
          attributes: ["name"],
          as: "shipment_type_stage_for_shipment",
        },
      ],
    },
    {
      model: shipment_inventory_thumbnail_photo,
      attributes: [
        ["shipment_inventory_photo_id", "photo_id"],
        "description",
        "stage_id2",
        "local_stage_id2",
        "mediaUrl",
        [
          sequelize.literal(
            `(CASE WHEN mediaUrl IS NOT NULL AND local_stage_id2 is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item_thumbnail}', shipment_inventory.shipment_job_id, '/original/', mediaUrl) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', mediaUrl) END)`
          ),
          "item_photo",
        ],
      ],
      required: false,
      as: "item_thumbnail_photo",
      include: [
        {
          model: shipment_type_stage_for_shipment,
          attributes: ["name"],
          as: "shipment_type_stage_for_shipment",
        },
      ],
    },

    {
      model: shipment_job,
      as: "shipment_job",
      attributes: [
        "shipment_job_id",
        "company_id",
        "job_status",
        "local_job_status",
      ],
    },
  ];

  if (
    Array.isArray(fieldsAndValues.tagIds) &&
    fieldsAndValues.tagIds.length > 0
  ) {
    include[0].include.where = [
      {
        tag_id: {
          [Op.in]: fieldsAndValues.tagIds,
        },
      },
    ];
  }

  const whereCondition = {
    shipment_job_id: shipmentId,
    [Op.or]: [
      { item_name: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
      { volume: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
      { weight: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
    ],
    deletedAt: null,
  };

  if (
    Array.isArray(fieldsAndValues.roomIds) &&
    fieldsAndValues.roomIds.length > 0
  ) {
    whereCondition.room_id = {
      [Op.in]: fieldsAndValues.roomIds,
    };
  }

  return shipment_inventory.findAndCountAll({
    where: whereCondition,
    attributes: [
      ...COMMON_INVENTORY_ATTRIBUTES,
      [
        sequelize.literal(
          "(IF((select job_id from shipment_inventory_job_scanned where shipment_inventory_job_scanned.local_current_stage_id = (select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id) and shipment_inventory_job_scanned.shipment_inventory_id = shipment_inventory.shipment_inventory_id and shipment_inventory_job_scanned.shipment_job_id = shipment_inventory.shipment_job_id) is not null, 'yes', 'no'))"
        ),
        "isScanned",
      ],

      [
        sequelize.literal(
          "(select random_number FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "qr_generate_code",
      ],
      [
        sequelize.literal(
          "(select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id)"
        ),
        "status",
      ],
      [
        sequelize.literal(
          "(select type FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "type",
      ],

      [
        sequelize.literal(
          "(select LPAD(label_number,8,0) FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "label_number",
      ],
      [
        sequelize.literal(
          "coalesce((select name FROM `shipment_rooms` where shipment_room_id = room_id), '')"
        ),
        "room_name",
      ],

      [
        sequelize.literal(
          "(IF((select shipment_inventory_id from shipment_inventory_forced where shipment_inventory_forced.local_current_stage_id = (select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id) and shipment_inventory_forced.shipment_inventory_id = shipment_inventory.shipment_inventory_id) is not null, 'yes', 'no'))"
        ),
        "isOverride",
      ],
      [
        sequelize.literal(
          `(IF(packed_by_owner = '1', 'Packed by Owner', 'Carrier Packed'))`
        ),
        "packed_by",
      ],
      [
        sequelize.literal(
          `(IF(disassembled_by_owner = '1', 'By Customer', 'By Company'))`
        ),
        "disassembled_by",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_electronics = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_electronics",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_firearm = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_firearm",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_add_dimension = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_add_dimension",
      ],

      [
        sequelize.literal(
          `(CASE WHEN is_high_value = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_high_value",
      ],

      [
        sequelize.literal(
          `(CASE WHEN is_pro_gear = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_pro_gear",
      ],

      [
        sequelize.literal(
          `(CASE WHEN isManualLabel = '1' THEN 'true' ELSE 'false' END)`
        ),
        "isManualLabel",
      ],

      [
        sequelize.literal(
          `(CASE WHEN is_carton = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_carton",
      ],

      [
        sequelize.literal(
          `(CASE WHEN is_disassembled = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_disassembled",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.prepared_by)"
        ),
        "prepared_by_username",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.disassembled_user_id)"
        ),
        "disassembled_by_username",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.carrier_packed_user_id)"
        ),
        "packed_by_username",
      ],
    ],
    include,
    distinct: true,
    limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 25,
    offset:
      fieldsAndValues.page_no > 1
        ? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
        : 0,
    order: [
      [
        fieldsAndValues.orderingField
          ? fieldsAndValues.orderingField
          : "created_at",
        fieldsAndValues.orderingWay ? fieldsAndValues.orderingWay : "DESC",
      ],
    ],
  });
};

exports.allItemsListModelForCms = async (body, shipmentId) => {
  const include = [
    {
      model: tag_item,
      as: "item_tag",
      required: false,
      attributes: [
        COMMON_ITEM_TAG_ATTRIBUTES[0],
        [sequelize.literal("`item_tag->m2m_item_tag`.tag_id"), "tag_id"],
        [sequelize.literal("`item_tag->m2m_item_tag`.name"), "name"],
        [sequelize.literal("`item_tag->m2m_item_tag`.color"), "color"],
        [
          sequelize.literal("`item_tag->m2m_item_tag`.company_id"),
          "company_id",
        ],
      ],
      include: {
        // required: false,
        model: tag,
        as: "m2m_item_tag",
        attributes: [],
      },
    },
    {
      model: unit_list,
      attributes: [
        "unit_id",
        "name",
        "storage_unit_id",
        "unitCode",
        "currentLocation",
      ],
      required: false,
      as: "unit_list",
    },
    {
      model: shipment_inventory_comments,
      attributes: ["id", "shipment_inventory_id", "comment"],
      as: "comments",
    },
    {
      model: shipment_inventory_exception_note,
      attributes: [
        ["shipment_inventory_exception_note_id", "note_id"],
        "notes",
      ],
      required: false,
      as: "exceptions",
      include: [
        {
          model: shipment_inventory_exception,
          required: false,
          attributes: [
            "shipment_exception_id",
            [
              sequelize.literal(
                "(select name FROM `shipment_exceptions` where shipment_exception_id = `exceptions->eid`.`shipment_exception_id`) "
              ),
              "exception_name",
            ],
          ],
          as: "eid",
          include: [
            {
              model: shipment_exception,
              attributes: [],
              required: true,
              as: "exception_list",
            },
          ],
        },
        {
          model: shipment_inventory_location,
          required: false,
          attributes: [
            "shipment_location_id",
            [
              sequelize.literal(
                "(select name FROM `shipment_locations` where shipment_location_id = `exceptions->lid`.`shipment_location_id`) "
              ),
              "location_name",
            ],
          ],
          as: "lid",
          include: [
            {
              model: shipment_location,
              attributes: [],
              required: true,
              as: "location_list",
            },
          ],
        },
      ],
    },
    {
      model: shipment_inventory_forced,
      attributes: [
        "shipment_inventory_id",
        "current_stage_id",
        "local_current_stage_id",
        "shipment_job_id",
        "updated_at",
        "reason",
        "override_by_staff",
      ],
      as: "forced_inventory",
      include: [
        {
          model: staff,
          as: "staff",
          attributes: ["first_name", "last_name"],
        },
        {
          model: shipment_type_stage_for_shipment,
          attributes: ["name"],
          as: "shipment_type_stage_for_shipment",
        },
      ],
    },
    {
      model: shipment_inventory_job_scanned,
      attributes: [
        "current_stage_id",
        "local_current_stage_id",
        "shipment_inventory_id",
        "shipment_job_id",
        "created_at",
        "scanned_by_staff",
      ],
      as: "scanned_inventory",
      include: [
        {
          model: staff,
          as: "staff",
          attributes: ["first_name", "last_name"],
        },
        {
          model: shipment_type_stage_for_shipment,
          attributes: ["name"],
          as: "shipment_type_stage_for_shipment",
        },
      ],
    },
    {
      model: qr_code,
      attributes: [
        ...COMMON_QR_ATTRIBUTES,
        [
          sequelize.literal(`CONCAT('${Const_AWS_BASE}', qr_image)`),
          "qr_image",
        ],
        [
          sequelize.literal(
            "(select LPAD(label_number,8,0) FROM `qr_codes` where item_qr.qr_code_id =qr_codes.qr_code_id)"
          ),
          "label_number",
        ],
      ],
      required: false,
      as: "item_qr",
    },
    {
      model: shipment_room,
      as: "room",
      attributes: ["name"],
    },
    {
      model: staff,
      as: "prepared_staff",
      attributes: ["first_name", "last_name"],
    },
    {
      model: staff,
      as: "carrier_packed",
      attributes: ["first_name", "last_name"],
    },
    {
      model: staff,
      as: "disassembled_user",
      attributes: ["first_name", "last_name"],
    },
    {
      model: shipment_inventory_photo,
      attributes: [
        ["shipment_inventory_photo_id", "photo_id"],
        "description",
        "stage_id",
        "local_stage_id",
        "is_thumbnail",
        "media",
        [
          sequelize.literal(
            `(CASE WHEN media IS NOT NULL AND local_stage_id is NULL AND stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', shipment_inventory.shipment_job_id, '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', media) END)`
          ),
          "item_photo",
        ],
      ],
      required: false,
      as: "item_photos",
      include: [
        {
          model: shipment_type_stage_for_shipment,
          attributes: ["name"],
          as: "shipment_type_stage_for_shipment",
        },
      ],
    },
    {
      model: shipment_inventory_thumbnail_photo,
      attributes: [
        ["shipment_inventory_photo_id", "photo_id"],
        "description",
        "stage_id2",
        "local_stage_id2",
        "mediaUrl",
        [
          sequelize.literal(
            `(CASE WHEN mediaUrl IS NOT NULL AND local_stage_id2 is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item_thumbnail}', shipment_inventory.shipment_job_id, '/original/', mediaUrl) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', mediaUrl) END)`
          ),
          "item_photo",
        ],
      ],
      required: false,
      as: "item_thumbnail_photo",
      include: [
        {
          model: shipment_type_stage_for_shipment,
          attributes: ["name"],
          as: "shipment_type_stage_for_shipment",
        },
      ],
    },

    {
      model: shipment_job,
      as: "shipment_job",
      attributes: [
        "shipment_job_id",
        "company_id",
        "job_status",
        "local_job_status",
      ],
    },
  ];

  if (Array.isArray(body.tagIds) && body.tagIds.length > 0) {
    include[0].include.where = [
      {
        tag_id: {
          [Op.in]: body.tagIds,
        },
      },
    ];
  }

  const escapedSearch = sequelize.escape("%" + body.search + "%");

  const whereCondition = {
    shipment_job_id: shipmentId,
    [Op.or]: [
      { item_name: { [Op.like]: `%${body.search}%` } },
      { volume: { [Op.like]: `%${body.search}%` } },
      { weight: { [Op.like]: `%${body.search}%` } },
      sequelize.literal(
        `(CASE WHEN isManualLabel = true THEN CONCAT('M-', LEFT(UPPER(shipment_inventory.color), 1), '/', shipment_inventory.lot_no, '/', shipment_inventory.label_no) ELSE NULL END) LIKE ${escapedSearch}`
      ),
      sequelize.literal(
        `EXISTS (SELECT 1 FROM qr_codes WHERE qr_codes.qr_code_id = shipment_inventory.qr_id AND qr_codes.random_number LIKE ${escapedSearch})`
      ),
      sequelize.literal(
        `EXISTS (SELECT 1 FROM qr_codes WHERE qr_codes.qr_code_id = shipment_inventory.qr_id AND LPAD(qr_codes.label_number, 8, '0') LIKE ${escapedSearch})`
      ),
    ],
    deletedAt: null,
  };

  if (Array.isArray(body.roomIds) && body.roomIds.length > 0) {
    whereCondition.room_id = {
      [Op.in]: body.roomIds,
    };
  }

  const stageConditions = {
    isScannedFlag: { isScannedFlag: 0, storage_unit_id: { [Op.is]: null } },
    is_additional_scan: { is_additional_scan: 0, isScannedFlag: 0 },
    is_remove_scan: { is_remove_scan: 0, storage_unit_id: { [Op.is]: null } },
    is_item_scanned_remove_from_storage: { storage_unit_id: { [Op.not]: null }, },
    is_item_add_to_storage: { isScannedFlag: 0, is_item_assign_to_unit_completed: 0 },
  };

  if (body.stageName && stageConditions[body.stageName]) {
    Object.assign(whereCondition, stageConditions[body.stageName]);
  }

  return shipment_inventory.findAndCountAll({
    where: whereCondition,
    attributes: [
      ...COMMON_INVENTORY_ATTRIBUTES,
      [
        sequelize.literal(
          "(IF((select job_id from shipment_inventory_job_scanned where shipment_inventory_job_scanned.local_current_stage_id = (select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id) and shipment_inventory_job_scanned.shipment_inventory_id = shipment_inventory.shipment_inventory_id and shipment_inventory_job_scanned.shipment_job_id = shipment_inventory.shipment_job_id) is not null, 'yes', 'no'))"
        ),
        "isScanned",
      ],

      [
        sequelize.literal(
          "(select random_number FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "qr_generate_code",
      ],
      [
        sequelize.literal(
          "(select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id)"
        ),
        "status",
      ],
      [
        sequelize.literal(
          "(select type FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "type",
      ],

      [
        sequelize.literal(
          "(select LPAD(label_number,8,0) FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "label_number",
      ],
      [
        sequelize.literal(
          "coalesce((select name FROM `shipment_rooms` where shipment_room_id = room_id), '')"
        ),
        "room_name",
      ],

      [
        sequelize.literal(
          "(IF((select shipment_inventory_id from shipment_inventory_forced where shipment_inventory_forced.local_current_stage_id = (select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id) and shipment_inventory_forced.shipment_inventory_id = shipment_inventory.shipment_inventory_id) is not null, 'yes', 'no'))"
        ),
        "isOverride",
      ],
      [
        sequelize.literal(
          `(IF(packed_by_owner = '1', 'Packed by Owner', 'Carrier Packed'))`
        ),
        "packed_by",
      ],
      [
        sequelize.literal(
          `(IF(disassembled_by_owner = '1', 'By Customer', 'By Company'))`
        ),
        "disassembled_by",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_electronics = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_electronics",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_firearm = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_firearm",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_add_dimension = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_add_dimension",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_additional_scan = '1' THEN 'yes' ELSE 'no' END)`
        ),
        "is_additional_scan",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_remove_scan = '1' THEN 'yes' ELSE 'no' END)`
        ),
        "is_remove_scan",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_high_value = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_high_value",
      ],

      [
        sequelize.literal(
          `(CASE WHEN is_pro_gear = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_pro_gear",
      ],

      [
        sequelize.literal(
          `(CASE WHEN isManualLabel = '1' THEN 'true' ELSE 'false' END)`
        ),
        "isManualLabel",
      ],

      [
        sequelize.literal(
          `(CASE WHEN is_carton = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_carton",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_disassembled = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_disassembled",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.prepared_by)"
        ),
        "prepared_by_username",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.disassembled_user_id)"
        ),
        "disassembled_by_username",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.carrier_packed_user_id)"
        ),
        "packed_by_username",
      ],
    ],
    include,
    distinct: true,
    limit: body.page_size ? parseInt(body.page_size) : 25,
    offset:
      body.page_no > 1 ? (parseInt(body.page_no) - 1) * body.page_size : 0,
    order: [
      [
        body.orderingField ? body.orderingField : "created_at",
        body.orderingWay ? body.orderingWay : "DESC",
      ],
    ],
  });
};

exports.allItemsListModelForCmsWeb = async (body, shipmentId) => {
  const include = [
    {
      model: tag_item,
      as: "item_tag",
      required: false,
      attributes: [
        COMMON_ITEM_TAG_ATTRIBUTES[0],
        [sequelize.literal("`item_tag->m2m_item_tag`.tag_id"), "tag_id"],
        [sequelize.literal("`item_tag->m2m_item_tag`.name"), "name"],
        [sequelize.literal("`item_tag->m2m_item_tag`.color"), "color"],
        [
          sequelize.literal("`item_tag->m2m_item_tag`.company_id"),
          "company_id",
        ],
      ],
      include: {
        // required: false,
        model: tag,
        as: "m2m_item_tag",
        attributes: [],
      },
    },
    {
      model: unit_list,
      attributes: [
        "unit_id",
        "name",
        "storage_unit_id",
        "unitCode",
        "currentLocation",
      ],
      required: false,
      as: "unit_list",
    },
    {
      model: shipment_inventory_comments,
      attributes: ["id", "shipment_inventory_id", "comment"],
      as: "comments",
    },
    {
      model: shipment_inventory_exception_note,
      attributes: [
        ["shipment_inventory_exception_note_id", "note_id"],
        "notes",
      ],
      required: false,
      as: "exceptions",
      include: [
        {
          model: shipment_inventory_exception,
          required: false,
          attributes: [
            "shipment_exception_id",
            [
              sequelize.literal(
                "(select name FROM `shipment_exceptions` where shipment_exception_id = `exceptions->eid`.`shipment_exception_id`) "
              ),
              "exception_name",
            ],
          ],
          as: "eid",
          include: [
            {
              model: shipment_exception,
              attributes: [],
              required: true,
              as: "exception_list",
            },
          ],
        },
        {
          model: shipment_inventory_location,
          required: false,
          attributes: [
            "shipment_location_id",
            [
              sequelize.literal(
                "(select name FROM `shipment_locations` where shipment_location_id = `exceptions->lid`.`shipment_location_id`) "
              ),
              "location_name",
            ],
          ],
          as: "lid",
          include: [
            {
              model: shipment_location,
              attributes: [],
              required: true,
              as: "location_list",
            },
          ],
        },
      ],
    },
    {
      model: shipment_inventory_forced,
      attributes: [
        "shipment_inventory_id",
        "current_stage_id",
        "local_current_stage_id",
        "shipment_job_id",
        "updated_at",
        "reason",
        "override_by_staff",
      ],
      as: "forced_inventory",
      include: [
        {
          model: staff,
          as: "staff",
          attributes: ["first_name", "last_name"],
        },
        {
          model: shipment_type_stage_for_shipment,
          attributes: ["name"],
          as: "shipment_type_stage_for_shipment",
        },
      ],
    },
    {
      model: shipment_inventory_job_scanned,
      attributes: [
        "current_stage_id",
        "local_current_stage_id",
        "shipment_inventory_id",
        "shipment_job_id",
        "created_at",
        "scanned_by_staff",
      ],
      as: "scanned_inventory",
      include: [
        {
          model: staff,
          as: "staff",
          attributes: ["first_name", "last_name"],
        },
        {
          model: shipment_type_stage_for_shipment,
          attributes: ["name"],
          as: "shipment_type_stage_for_shipment",
        },
      ],
    },
    {
      model: qr_code,
      attributes: [
        ...COMMON_QR_ATTRIBUTES,
        [
          sequelize.literal(`CONCAT('${Const_AWS_BASE}', qr_image)`),
          "qr_image",
        ],
        [
          sequelize.literal(
            "(select LPAD(label_number,8,0) FROM `qr_codes` where item_qr.qr_code_id =qr_codes.qr_code_id)"
          ),
          "label_number",
        ],
      ],
      required: false,
      as: "item_qr",
    },
    {
      model: shipment_room,
      as: "room",
      attributes: ["name"],
    },
    {
      model: staff,
      as: "prepared_staff",
      attributes: ["first_name", "last_name"],
    },
    {
      model: staff,
      as: "carrier_packed",
      attributes: ["first_name", "last_name"],
    },
    {
      model: staff,
      as: "disassembled_user",
      attributes: ["first_name", "last_name"],
    },
    {
      model: shipment_inventory_photo,
      attributes: [
        ["shipment_inventory_photo_id", "photo_id"],
        "description",
        "stage_id",
        "local_stage_id",
        "is_thumbnail",
        "media",
        [
          sequelize.literal(
            `(CASE WHEN media IS NOT NULL AND local_stage_id is NULL AND stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', shipment_inventory.shipment_job_id, '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', media) END)`
          ),
          "item_photo",
        ],
      ],
      required: false,
      as: "item_photos",
      include: [
        {
          model: shipment_type_stage_for_shipment,
          attributes: ["name"],
          as: "shipment_type_stage_for_shipment",
        },
      ],
    },
    {
      model: shipment_inventory_thumbnail_photo,
      attributes: [
        ["shipment_inventory_photo_id", "photo_id"],
        "description",
        "stage_id2",
        "local_stage_id2",
        "mediaUrl",
        [
          sequelize.literal(
            `(CASE WHEN mediaUrl IS NOT NULL AND local_stage_id2 is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item_thumbnail}', shipment_inventory.shipment_job_id, '/original/', mediaUrl) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', mediaUrl) END)`
          ),
          "item_photo",
        ],
      ],
      required: false,
      as: "item_thumbnail_photo",
      include: [
        {
          model: shipment_type_stage_for_shipment,
          attributes: ["name"],
          as: "shipment_type_stage_for_shipment",
        },
      ],
    },

    {
      model: shipment_job,
      as: "shipment_job",
      attributes: [
        "shipment_job_id",
        "company_id",
        "job_status",
        "local_job_status",
      ],
    },
  ];

  if (Array.isArray(body.tagIds) && body.tagIds.length > 0) {
    include[0].include.where = [
      {
        tag_id: {
          [Op.in]: body.tagIds,
        },
      },
    ];
  }

  const originalSearch = body.search || '';
  const mirrorSearch = originalSearch.split('').reverse().join('');

  const escapedSearch = sequelize.escape("%" + originalSearch + "%");
  const escapedMirrorSearch = sequelize.escape("%" + mirrorSearch + "%");

  const whereCondition = {
    shipment_job_id: shipmentId,
    [Op.or]: [
      { item_name: { [Op.like]: `%${originalSearch}%` } },
      { volume: { [Op.like]: `%${originalSearch}%` } },
      { weight: { [Op.like]: `%${originalSearch}%` } },

      ...(originalSearch !== mirrorSearch ? [
        { item_name: { [Op.like]: `%${mirrorSearch}%` } },
        { volume: { [Op.like]: `%${mirrorSearch}%` } },
        { weight: { [Op.like]: `%${mirrorSearch}%` } },
      ] : []),

      sequelize.literal(
        `(CASE WHEN isManualLabel = true THEN CONCAT('M-', LEFT(UPPER(shipment_inventory.color), 1), '/', shipment_inventory.lot_no, '/', shipment_inventory.label_no) ELSE NULL END) LIKE ${escapedSearch}`
      ),
      ...(originalSearch !== mirrorSearch ? [
        sequelize.literal(
          `(CASE WHEN isManualLabel = true THEN CONCAT('M-', LEFT(UPPER(shipment_inventory.color), 1), '/', shipment_inventory.lot_no, '/', shipment_inventory.label_no) ELSE NULL END) LIKE ${escapedMirrorSearch}`
        )
      ] : []),

      sequelize.literal(
        `EXISTS (SELECT 1 FROM qr_codes WHERE qr_codes.qr_code_id = shipment_inventory.qr_id AND qr_codes.random_number LIKE ${escapedSearch})`
      ),
      ...(originalSearch !== mirrorSearch ? [
        sequelize.literal(
          `EXISTS (SELECT 1 FROM qr_codes WHERE qr_codes.qr_code_id = shipment_inventory.qr_id AND qr_codes.random_number LIKE ${escapedMirrorSearch})`
        )
      ] : []),

      sequelize.literal(
        `EXISTS (SELECT 1 FROM qr_codes WHERE qr_codes.qr_code_id = shipment_inventory.qr_id AND LPAD(qr_codes.label_number, 8, '0') LIKE ${escapedSearch})`
      ),
      ...(originalSearch !== mirrorSearch ? [
        sequelize.literal(
          `EXISTS (SELECT 1 FROM qr_codes WHERE qr_codes.qr_code_id = shipment_inventory.qr_id AND LPAD(qr_codes.label_number, 8, '0') LIKE ${escapedMirrorSearch})`
        )
      ] : []),

      sequelize.literal(
        `EXISTS (SELECT 1 FROM unit_lists WHERE unit_lists.storage_unit_id = shipment_inventory.storage_unit_id AND unit_lists.name LIKE ${escapedSearch})`
      ),
      ...(originalSearch !== mirrorSearch ? [
        sequelize.literal(
          `EXISTS (SELECT 1 FROM unit_lists WHERE unit_lists.storage_unit_id = shipment_inventory.storage_unit_id AND unit_lists.name LIKE ${escapedMirrorSearch})`
        )
      ] : []),

      // Unit current location searches (original and mirror)
      sequelize.literal(
        `EXISTS (SELECT 1 FROM unit_lists WHERE unit_lists.storage_unit_id = shipment_inventory.storage_unit_id AND unit_lists.currentLocation LIKE ${escapedSearch})`
      ),
      ...(originalSearch !== mirrorSearch ? [
        sequelize.literal(
          `EXISTS (SELECT 1 FROM unit_lists WHERE unit_lists.storage_unit_id = shipment_inventory.storage_unit_id AND unit_lists.currentLocation LIKE ${escapedMirrorSearch})`
        )
      ] : []),
    ],
    deletedAt: null,
  };

  if (Array.isArray(body.roomIds) && body.roomIds.length > 0) {
    whereCondition.room_id = {
      [Op.in]: body.roomIds,
    };
  }

  const stageConditions = {
    isScannedFlag: { isScannedFlag: 0, storage_unit_id: { [Op.is]: null } },
    is_additional_scan: { is_additional_scan: 0, isScannedFlag: 0 },
    is_remove_scan: { is_remove_scan: 0, storage_unit_id: { [Op.is]: null } },
    is_item_scanned_remove_from_storage: {
      storage_unit_id: { [Op.not]: null },
    },
    is_item_add_to_storage: { isScannedFlag: 0 },
  };

  const currentShipmentStageNameConditions = {
    add_items_to_inventory: { inventory_stage_id: body.currentShipmentStageId },
    add_items_to_storage: {
      assign_to_storage_stage_id: body.currentShipmentStageId,
    },
    remove_items_from_storage: {
      remove_from_storage_stage_id: body.currentShipmentStageId,
    },
    remove_items_from_inventory: {
      remove_from_inventory_stage_id: body.currentShipmentStageId,
    },
    remove_scan_require: { is_remove_scan: 1 },
    additional_scan_require: { is_additional_scan: 1 },
  };

  if (body.stageName && stageConditions[body.stageName]) {
    Object.assign(whereCondition, stageConditions[body.stageName]);
  }

  if (
    body.currentShipmentStageName &&
    currentShipmentStageNameConditions[body.currentShipmentStageName]
  ) {
    Object.assign(
      whereCondition,
      currentShipmentStageNameConditions[body.currentShipmentStageName]
    );
  }

  return shipment_inventory.findAndCountAll({
    where: whereCondition,
    attributes: [
      ...COMMON_INVENTORY_ATTRIBUTES,
      [
        sequelize.literal(
          "(IF((select job_id from shipment_inventory_job_scanned where shipment_inventory_job_scanned.local_current_stage_id = (select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id) and shipment_inventory_job_scanned.shipment_inventory_id = shipment_inventory.shipment_inventory_id and shipment_inventory_job_scanned.shipment_job_id = shipment_inventory.shipment_job_id) is not null, 'yes', 'no'))"
        ),
        "isScanned",
      ],

      [
        sequelize.literal(
          "(select random_number FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "qr_generate_code",
      ],
      [
        sequelize.literal(
          "(select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id)"
        ),
        "status",
      ],
      [
        sequelize.literal(
          "(select type FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "type",
      ],

      [
        sequelize.literal(
          "(select LPAD(label_number,8,0) FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "label_number",
      ],
      [
        sequelize.literal(
          "coalesce((select name FROM `shipment_rooms` where shipment_room_id = room_id), '')"
        ),
        "room_name",
      ],

      [
        sequelize.literal(
          "(IF((select shipment_inventory_id from shipment_inventory_forced where shipment_inventory_forced.local_current_stage_id = (select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id) and shipment_inventory_forced.shipment_inventory_id = shipment_inventory.shipment_inventory_id) is not null, 'yes', 'no'))"
        ),
        "isOverride",
      ],
      [
        sequelize.literal(
          `(IF(packed_by_owner = '1', 'Packed by Owner', 'Carrier Packed'))`
        ),
        "packed_by",
      ],
      [
        sequelize.literal(
          `(IF(disassembled_by_owner = '1', 'By Customer', 'By Company'))`
        ),
        "disassembled_by",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_electronics = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_electronics",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_firearm = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_firearm",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_add_dimension = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_add_dimension",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_additional_scan = '1' THEN 'yes' ELSE 'no' END)`
        ),
        "is_additional_scan",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_remove_scan = '1' THEN 'yes' ELSE 'no' END)`
        ),
        "is_remove_scan",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_high_value = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_high_value",
      ],

      [
        sequelize.literal(
          `(CASE WHEN is_pro_gear = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_pro_gear",
      ],

      [
        sequelize.literal(
          `(CASE WHEN isManualLabel = '1' THEN 'true' ELSE 'false' END)`
        ),
        "isManualLabel",
      ],

      [
        sequelize.literal(
          `(CASE WHEN is_carton = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_carton",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_disassembled = '1' THEN 'true' ELSE 'false' END)`
        ),
        "is_disassembled",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.prepared_by)"
        ),
        "prepared_by_username",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.disassembled_user_id)"
        ),
        "disassembled_by_username",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.carrier_packed_user_id)"
        ),
        "packed_by_username",
      ],
    ],
    include,
    distinct: true,
    limit: body.page_size ? parseInt(body.page_size) : 25,
    offset:
      body.page_no > 1 ? (parseInt(body.page_no) - 1) * body.page_size : 0,
    order: [
      [
        body.orderingField ? body.orderingField : "created_at",
        body.orderingWay ? body.orderingWay : "DESC",
      ],
    ],
  });
};

exports.shipmentListForStorageModel = async (request) => {
  let job_id = "`job_units->unit_items`.`shipment_job_id`";
  let shipment_inventory_id = "`job_units->unit_items`.`shipment_inventory_id`";
  return shipment_job.findAndCountAll({
    attributes: [...COMMON_JOB_ATTRIBUTES],
    where: {
      shipment_job_id: { [Op.in]: request.shipmentArray },
    },
    include: [
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "storage_unit_id",
          "shipment_job_id",
          "unitCode",
          "number",
          "currentLocation",
          "numericLocation",
          "addedBy",
          "unitNotes",
          "name",
          "status",
          "isActive",
          "warehouseId",
          "customerId",
          "shipmentId",
          "roomId",
          "unitTypeId",
          "unitTypeName",
        ],
        required: false,
        as: "job_units",
        include: [
          {
            model: shipment_inventory,
            attributes: [
              ...COMMON_INVENTORY_ATTRIBUTES,
              [
                sequelize.literal(`(IF(is_carton = '1', true, false))`),
                "is_carton",
              ],
              [
                sequelize.literal(
                  `(IF(packed_by_owner = '1', 'Packed by Owner', 'Carrier Packed'))`
                ),
                "packed_by",
              ],
              [
                sequelize.literal(`(IF(is_disassembled = '1', true, false))`),
                "is_disassembled",
              ],
              [
                sequelize.literal(
                  `(IF(disassembled_by_owner = '1', 'By Customer', 'By Company'))`
                ),
                "disassembled_by",
              ],
              [
                sequelize.literal(`(IF(is_electronics = '1', true, false))`),
                "is_electronics",
              ],
              [
                sequelize.literal(`(IF(is_high_value = '1', true, false))`),
                "is_high_value",
              ],
              [
                sequelize.literal(`(IF(is_pro_gear = '1', true, false))`),
                "is_pro_gear",
              ],
              [
                sequelize.literal(`(IF(isManualLabel = '1', true, false))`),
                "isManualLabel",
              ],
            ],
            include: [
              {
                model: unit_list,
                attributes: [
                  "unit_id",
                  "name",
                  "storage_unit_id",
                  "unitCode",
                  "currentLocation",
                ],
                required: false,
                as: "unit_list",
              },
              {
                model: shipment_inventory_comments,
                attributes: ["id", "shipment_inventory_id", "comment"],
                as: "comments",
              },

              {
                model: shipment_inventory_forced,
                attributes: [
                  "shipment_inventory_id",
                  "current_stage_id",
                  "local_current_stage_id",
                  "shipment_job_id",
                  "updated_at",
                  "reason",
                  "override_by_staff",
                ],
                as: "forced_inventory",
                include: [
                  {
                    model: staff,
                    as: "staff",
                    attributes: ["first_name", "last_name"],
                  },
                  {
                    model: shipment_type_stage_for_shipment,
                    attributes: ["name"],
                    as: "shipment_type_stage_for_shipment",
                  },
                ],
              },
              {
                model: shipment_inventory_job_scanned,
                attributes: [
                  "current_stage_id",
                  "local_current_stage_id",
                  "shipment_inventory_id",
                  "shipment_job_id",
                  "created_at",
                  "scanned_by_staff",
                ],
                as: "scanned_inventory",
                include: [
                  {
                    model: staff,
                    as: "staff",
                    attributes: ["first_name", "last_name"],
                  },
                  {
                    model: shipment_type_stage_for_shipment,
                    attributes: ["name"],
                    as: "shipment_type_stage_for_shipment",
                  },
                ],
              },
              {
                model: qr_code,
                attributes: [
                  ...COMMON_QR_ATTRIBUTES,
                  "type",
                  [
                    sequelize.literal(`CONCAT('${Const_AWS_BASE}', qr_image)`),
                    "qr_image",
                  ],
                  [
                    sequelize.literal(
                      "(select LPAD(label_number,8,0) FROM `qr_codes` where `job_units->unit_items->item_qr`.qr_code_id = qr_codes.qr_code_id)"
                    ),
                    "label_number",
                  ],
                ],
                required: false,
                as: "item_qr",
              },
              {
                model: shipment_room,
                as: "room",
                attributes: ["name"],
              },
              {
                model: staff,
                as: "prepared_staff",
                attributes: ["first_name", "last_name"],
              },
              {
                model: staff,
                as: "carrier_packed",
                attributes: ["first_name", "last_name"],
              },
              {
                model: staff,
                as: "disassembled_user",
                attributes: ["first_name", "last_name"],
              },
              {
                model: shipment_inventory_photo,
                attributes: [
                  ["shipment_inventory_photo_id", "photo_id"],
                  "description",
                  "stage_id",
                  "local_stage_id",
                  "media",
                  [
                    sequelize.literal(
                      `(CASE WHEN media IS NOT NULL AND local_stage_id is NULL AND stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', ${job_id} , '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', ${shipment_inventory_id}, '/original/', media) END)`
                    ),
                    "item_photo",
                  ],
                ],
                required: false,
                as: "item_photos",
                include: [
                  {
                    model: shipment_type_stage_for_shipment,
                    attributes: ["name", "order_of_stages"],
                    as: "shipment_type_stage_for_shipment",
                  },
                ],
              },

              {
                model: shipment_job,
                as: "shipment_job",
                attributes: ["shipment_job_id", "company_id"],
              },
            ],
            required: false,
            as: "unit_items",
          },
        ],
      },
    ],
  });
};

exports.checkExistQrCodeModel = async (random_number_check) => {
  const random_number = await qr_code.findOne({
    where: { random_number: random_number_check },
  });

  if (random_number !== null) {
    return true;
  } else {
    return false;
  }
};

exports.checkQrCodeBelongToItem = async (qrId) => {
  const checkQr = await shipment_inventory.findOne({
    where: { qr_id: qrId },
  });
  if (checkQr !== null) {
    return true;
  } else {
    return false;
  }
};

exports.getQrCodeDetialsForCheck = async (random_number_check) => {
  const random_number = await qr_code.findOne({
    where: { random_number: random_number_check },
    attributes: [
      "qr_code_id",
      "job_id",
      "company_id",
      "label_number",
      "qr_image",
      "type",
      "status",
      ["random_number", "qr_generate_code"],
    ],
  });
  if (random_number !== null) {
    return random_number;
  } else {
    return false;
  }
};

exports.isValidQrCodeBelongToShipment = async (
  random_number_check,
  shipmentCheck
) => {
  const random_number = await qr_code.findOne({
    where: {
      random_number: random_number_check,
      job_id: shipmentCheck,
    },
  });

  if (random_number !== null) {
    return true;
  } else {
    return false;
  }
};

exports.getShipmentDetailsForGenericLabel = async (shipmentId) => {
  return shipment_job.findOne({
    where: {
      shipment_job_id: shipmentId,
    },
  });
};

exports.isValidQrCodeBelongToCompany = async (
  random_number_check,
  companyId
) => {
  return qr_code.findOne({
    where: {
      random_number: random_number_check,
      company_id: companyId,
    },
  });
};

exports.getQrCodeDetailsModel = async (random_number) => {
  return await qr_code.findOne({
    where: { random_number },
    include: [
      {
        model: shipment_job,
        required: true,
        as: "shipment_job",
        attributes: [
          ...COMMON_JOB_ATTRIBUTES,
          [
            sequelize.literal(
              "(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
            ),
            "total_items",
          ],
          [
            sequelize.literal(
              "(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
            ),
            "total_volume",
          ],
          [
            sequelize.literal(
              "(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
            ),
            "total_weight",
          ],

          [
            sequelize.literal(
              '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
            ),
            "firearms_total_quantity",
          ],

          [
            sequelize.literal(
              '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
            ),
            "total_cartons",
          ],

          [
            sequelize.literal(
              '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
            ),
            "total_cartons_cp",
          ],

          [
            sequelize.literal(
              '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
            ),
            "total_cartons_pbo",
          ],

          [
            sequelize.literal(
              '(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
            ),
            "total_high_value",
          ],

          [
            sequelize.literal(
              "(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
            ),
            "total_pads_used",
          ],

          [
            sequelize.literal(
              '(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
            ),
            "total_pro_gear_weight",
          ],

          [
            sequelize.literal(
              '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
            ),
            "total_pro_gear_items",
          ],
          [
            sequelize.literal(
              "(select CONCAT(first_name,' ', last_name)  FROM `customers` where customer_id = shipment_job.customer_id) "
            ),
            "customer_name",
          ],
          [
            sequelize.literal(
              "(select account_id FROM `customers` where customer_id = shipment_job.customer_id) "
            ),
            "account_id",
          ],
          [
            sequelize.literal(
              "(select phone  FROM `customers` where customer_id = shipment_job.customer_id) "
            ),
            "customer_phone",
          ],
          [
            sequelize.literal(
              "(select name FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "current_job_status",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN is_add_item = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "is_add_item",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN is_add_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "is_add_exceptions",
          ],
          [
            sequelize.literal(
              "(select name FROM `shipment_type_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id) "
            ),
            "shipment_type_name",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN show_no_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "show_no_exceptions",
          ],
          [
            sequelize.literal(
              "(select name FROM `shipment_type_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id) "
            ),
            "shipment_name",
          ],
          [
            sequelize.literal(
              `(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`
            ),
            "is_job_complete",
          ],
          [
            sequelize.literal(
              "(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "current_job_stage",
          ],
          [
            sequelize.literal(
              "(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
            ),
            "total_stages",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN order_of_stages = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "is_first_stage",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "is_scan_require",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN remove_scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "remove_scan_require",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN allow_default_manual_label = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "allow_default_manual_label",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN add_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "add_items_to_inventory",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN remove_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "remove_items_to_inventory",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN enable_partial_complete_stage = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "enable_partial_complete_stage",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN scan_into_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "scan_into_storage",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "assign_storage_units_to_items",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "unassign_storage_units_from_items",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN scan_out_of_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "scan_out_of_storage",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN supervisor_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "is_supervisor_signature_require",
          ],

          [
            sequelize.literal(
              "(select why_supervisor_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "why_supervisor_signature_require_note",
          ],

          [
            sequelize.literal(
              "(select CASE WHEN customer_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "is_customer_signature_require",
          ],

          [
            sequelize.literal(
              "(select why_customer_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "why_customer_signature_require_note",
          ],
          [
            sequelize.literal(
              `(if(
							  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL) = 
							  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "1")
							,"yes","no"))`
            ),
            "isAllItemScanned",
          ],
        ],
        include: [
          {
            model: company,
            attributes: ["company_id"],
            required: true,
            as: "job_company",
            include: [
              {
                model: staff,
                where: { roles: "ADMIN", is_deleted: 0, status: "active" },
                attributes: [
                  "staff_id",
                  ["roles", "role"],
                  "first_name",
                  "last_name",
                ],
                required: true,
                as: "staffs",
              },
            ],
          },

          {
            model: customer,
            attributes: [],
            required: true,
            as: "customer_job",
          },
          {
            model: shipment_type_stage_for_shipment,
            attributes: [],
            required: true,
            as: "local_shipment_job_status",
          },

          {
            model: shipment_job_assign_worker_list,
            required: false,
            attributes: [
              "staff_id",
              "role",
              [
                sequelize.literal(
                  "(SELECT first_name FROM `staffs` WHERE staff_id = `shipment_job->assign_worker`.`staff_id`)"
                ),
                "first_name",
              ],
              [
                sequelize.literal(
                  "(SELECT last_name FROM `staffs` WHERE staff_id = `shipment_job->assign_worker`.`staff_id`)"
                ),
                "last_name",
              ],
            ],
            as: "assign_worker",
            include: [
              {
                model: staff,
                attributes: [],
                required: true,
                as: "assign_worker_detail",
              },
            ],
          },
        ],
      },
    ],
  });
};

exports.getQrCodeDetailForStorageModel = async (random_number) => {
  return await qr_code.findOne({
    where: { random_number },
    attributes: [...COMMON_QR_ATTRIBUTES_STORAGE],
    include: [
      {
        model: shipment_job,
        required: true,
        as: "shipment_job",
        attributes: [
          ...COMMON_JOB_ATTRIBUTES_STORAGE,
          [
            sequelize.literal(
              "(select CONCAT(first_name,' ', last_name)  FROM `customers` where customer_id = shipment_job.customer_id) "
            ),
            "customer_name",
          ],
          [
            sequelize.literal(
              "(select account_id FROM `customers` where customer_id = shipment_job.customer_id) "
            ),
            "account_id",
          ],
          [
            sequelize.literal(
              "(select phone  FROM `customers` where customer_id = shipment_job.customer_id) "
            ),
            "customer_phone",
          ],
          [
            sequelize.literal(
              "(select name FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "current_job_status",
          ],
          [
            sequelize.literal(
              `(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`
            ),
            "is_job_complete",
          ],
        ],
      },
    ],
  });
};

exports.inventoryDetailsForStorageQrcode = async (request) => {
  return await shipment_inventory.findOne({
    attributes: [
      ...COMMON_INVENTORY_ATTRIBUTES,
      [
        sequelize.literal(
          `(IF((select job_id from shipment_inventory_job_scanned where shipment_inventory_job_scanned.local_current_stage_id = shipment_job.local_job_status and shipment_inventory_job_scanned.shipment_inventory_id = shipment_inventory.shipment_inventory_id and shipment_inventory_job_scanned.shipment_job_id = shipment_inventory.shipment_job_id) is not null, 'yes', 'no'))`
        ),
        "isScanned",
      ],
      [
        sequelize.literal(
          `(IF((select shipment_inventory_id from shipment_inventory_forced where shipment_inventory_forced.local_current_stage_id = shipment_job.local_job_status and shipment_inventory_forced.shipment_inventory_id = shipment_inventory.shipment_inventory_id) is not null, 'yes', 'no'))`
        ),
        "isOverride",
      ],
      [sequelize.literal(`(IF(is_carton = '1', true, false))`), "is_carton"],
      [
        sequelize.literal(
          `(IF(packed_by_owner = '1', 'Packed by Owner', 'Carrier Packed'))`
        ),
        "packed_by",
      ],
      [
        sequelize.literal(`(IF(is_disassembled = '1', true, false))`),
        "is_disassembled",
      ],
      [
        sequelize.literal(
          `(IF(disassembled_by_owner = '1', 'By Customer', 'By Company'))`
        ),
        "disassembled_by",
      ],
      [
        sequelize.literal(`(IF(is_electronics = '1', true, false))`),
        "is_electronics",
      ],
      [
        sequelize.literal(`(IF(is_high_value = '1', true, false))`),
        "is_high_value",
      ],
      [
        sequelize.literal(`(IF(is_pro_gear = '1', true, false))`),
        "is_pro_gear",
      ],
      [
        sequelize.literal(`(IF(isManualLabel = '1', true, false))`),
        "isManualLabel",
      ],
      [sequelize.literal(`(IF(is_firearm = '1', true, false))`), "is_firearm"],
    ],
    where: {
      qr_id: request.qr_code_id,
    },
    include: [
      {
        model: qr_code,
        attributes: [
          ...COMMON_QR_ATTRIBUTES,
          [
            sequelize.literal(`CONCAT('${Const_AWS_BASE}', qr_image)`),
            "qr_image",
          ],
          [
            sequelize.literal(
              "(select LPAD(label_number,8,0) FROM `qr_codes` where item_qr.qr_code_id =qr_codes.qr_code_id)"
            ),
            "label_number",
          ],
        ],
        required: false,
        as: "item_qr",
      },
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
        ],
        required: false,
        as: "unit_list",
      },
      {
        model: shipment_inventory_photo,
        attributes: [
          ["shipment_inventory_photo_id", "photo_id"],
          "description",
          "stage_id",
          "local_stage_id",
          "media",
          [
            sequelize.literal(
              `(CASE WHEN media IS NOT NULL AND local_stage_id is NULL AND stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', shipment_inventory.shipment_job_id, '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', media) END)`
            ),
            "item_photo",
          ],
        ],
        required: false,
        as: "item_photos",
        include: [
          {
            model: shipment_type_stage_for_shipment,
            attributes: ["name", "order_of_stages"],
            as: "shipment_type_stage_for_shipment",
          },
        ],
      },
      {
        model: shipment_job,
        as: "shipment_job",
        attributes: [],
      },
    ],
    raw: false,
    subQuery: false,
  });
};

exports.getUnitList = async (request) => {
  return await shipment_job.findAndCountAll({
    where: {
      shipment_job_id: request.shipmentId,
    },
    logging: console.log,
    include: [
      {
        model: unit_list,
        where: {
          isActive: 1,
        },
        attributes: [
          "unit_id",
          "storage_unit_id",
          "shipment_job_id",
          "unitCode",
          "addedBy",
          "status",
          "isActive",
          "warehouseId",
          "name",
          "numericLocation",
          "currentLocation",
          "unitNotes",
        ],
        required: false,
        as: "job_warehouses",
      },
    ],
  });
};

exports.getUnitListByDate = async (request, fieldsAndValues, staff_id) => {
  const job_id_array = [];
  let job_id_list;
  const { roles, company_id } = await staff.findOne({
    where: { staff_id: staff_id },
    raw: true,
  });

  if (roles === "ADMIN") {
    job_id_list = await shipment_job.findAndCountAll({
      attributes: ["shipment_job_id", "warehouseId"],
      where: { company_id: company_id },
      raw: true,
    });
  } else {
    job_id_list = await shipment_job_assign_worker.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { staff_id: staff_id },
      raw: true,
    });
  }
  if (job_id_list.rows.length > 0) {
    job_id_list.rows.forEach((element) => {
      job_id_array.push(element.shipment_job_id);
    });
  }
  return await shipment_job.findAndCountAll({
    where: {
      shipment_job_id: { [Op.in]: job_id_array },
      warehouseId: { [Op.not]: null },
      is_job_complete_flag: 0,
    },

    logging: console.log,

    include: [
      {
        model: unit_list,
        where: {
          updated_at: { [Op.gte]: request.date, [Op.lt]: new Date() },
        },
        attributes: [
          "unit_id",
          "storage_unit_id",
          "shipment_job_id",
          "unitCode",
          "addedBy",
          "status",
          "isActive",
          "warehouseId",
        ],
        required: false,
        as: "job_warehouses",
      },
    ],
  });
};

exports.getJobList = async (request, fieldsAndValues, staff_id) => {
  const job_id_array = [];
  let job_id_list;
  const { roles, company_id } = await staff.findOne({
    where: { staff_id: staff_id },
    raw: true,
  });

  if (roles === "ADMIN") {
    job_id_list = await shipment_job.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { company_id: company_id },
      raw: true,
    });
  } else {
    job_id_list = await shipment_job_assign_worker.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { staff_id: staff_id },
      raw: true,
    });
  }
  if (job_id_list.rows.length > 0) {
    job_id_list.rows.forEach((element) => {
      job_id_array.push(element.shipment_job_id);
    });
  }

  let shipmentsResponse = await shipment_job.findAll({
    where: {
      company_id: company_id,
      shipment_job_id: { [Op.in]: job_id_array },
      is_job_complete_flag: 0,
    },
    attributes: [["shipment_job_id", "job_id"], "job_number", "shipment_name"],
    include: [
      {
        model: qr_code,
        where: {
          job_id: { [Op.in]: job_id_array },
          type: "Shipment",
        },
        attributes: [
          "qr_code_id",
          "type",
          "company_id",
          "job_id",
          ["random_number", "qr_generate_code"],
          [
            sequelize.literal(
              "(select LPAD(label_number,8,0) FROM qr_codes where `job_qr`.`qr_code_id` = qr_codes.qr_code_id)"
            ),
            "label_number",
          ],
          [
            sequelize.literal(
              "(IF(`job_qr`.`qr_code_id` in (select distinct qr_id from shipment_inventories), 'yes', 'no'))"
            ),
            "is_qr_scanned",
          ],
        ],
        required: false,
        as: "job_qr",
      },
    ],
    raw: false,
    order: [["shipment_job_id", "DESC"]],
    distinct: true,
  });
  return shipmentsResponse;
};

exports.getUpdatedJobList = async (request, fieldsAndValues, staff_id) => {
  const job_id_array = [];
  let job_id_list;
  const { roles, company_id } = await staff.findOne({
    where: { staff_id: staff_id },
    raw: true,
  });

  if (roles === "ADMIN") {
    job_id_list = await shipment_job.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { company_id: company_id },
      raw: true,
    });
  } else {
    job_id_list = await shipment_job_assign_worker.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { staff_id: staff_id },
      raw: true,
    });
  }
  if (job_id_list.rows.length > 0) {
    job_id_list.rows.forEach((element) => {
      job_id_array.push(element.shipment_job_id);
    });
  }
  let Const_Page_Count =
    request.page_no && request.page_no > 0 ? JOB_LIST_PER_PAGE : 1000000;
  let offset =
    request.page_no && request.page_no > 0
      ? Const_Page_Count * Number(request.page_no - 1)
      : 0;

  return await shipment_job.findAndCountAll({
    where: {
      company_id: company_id,
      [Op.or]: [
        {
          shipment_name: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
        {
          job_number: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
        {
          email: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
      ],
      is_job_complete_flag: 0,
    },
    logging: console.log,
    attributes: [
      ...COMMON_JOB_ATTRIBUTES,
      [
        sequelize.literal(
          "(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_items",
      ],
      [
        sequelize.literal(
          "(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_volume",
      ],
      [
        sequelize.literal(
          "(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
        ),
        "firearms_total_quantity",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
        ),
        "total_cartons",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
        ),
        "total_cartons_cp",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
        ),
        "total_cartons_pbo",
      ],

      [
        sequelize.literal(
          '(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
        ),
        "total_high_value",
      ],

      [
        sequelize.literal(
          "(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_pads_used",
      ],

      [
        sequelize.literal(
          '(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_items",
      ],
      [
        sequelize.literal(
          "(select CONCAT(first_name,' ',(IF(last_name IS NULL, '', last_name)))  FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "customer_name",
      ],
      [
        sequelize.literal(
          "(select account_id FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "account_id",
      ],
      [
        sequelize.literal(
          "(select name  FROM `shipment_type_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id ) "
        ),
        "shipment_type_name",
      ],

      [
        sequelize.literal(
          "(select (IF(phone IS NULL, '', phone))  FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "customer_phone",
      ],
      [
        sequelize.literal("(IF(shipment_name IS NULL, '', shipment_name))"),
        "shipment_name",
      ],
      [
        sequelize.literal(
          "(select name FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_status",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_item = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_item",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_exceptions",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN show_no_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "show_no_exceptions",
      ],
      [
        sequelize.literal(
          "(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_stage",
      ],
      [
        sequelize.literal(
          "(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
        ),
        "total_stages",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN order_of_stages = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_first_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN allow_default_manual_label = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "allow_default_manual_label",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN add_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "add_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN enable_partial_complete_stage = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "enable_partial_complete_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_into_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_into_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "assign_storage_units_to_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "unassign_storage_units_from_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_out_of_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_out_of_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN supervisor_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_supervisor_signature_require",
      ],

      [
        sequelize.literal(
          "(select why_supervisor_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_supervisor_signature_require_note",
      ],

      [
        sequelize.literal(
          "(select CASE WHEN customer_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_customer_signature_require",
      ],

      [
        sequelize.literal(
          "(select why_customer_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_customer_signature_require_note",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`
        ),
        "is_job_complete",
      ],
      [
        sequelize.literal(
          `(if(
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL) = 
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "1")
					,"yes","no"))`
        ),
        "isAllItemScanned",
      ],
    ],
    include: [
      {
        model: company,
        attributes: ["company_id"],
        required: true,
        as: "job_company",
        include: [
          {
            model: staff,
            where: { roles: "ADMIN", is_deleted: 0, status: "active" },
            attributes: [
              "staff_id",
              ["roles", "role"],
              "first_name",
              "last_name",
            ],
            required: true,
            as: "staffs",
          },
        ],
      },
      {
        model: tag_shipment,
        as: "shipment_tag",
        required: false,
        attributes: [
          COMMON_JOB_TAG_ATTRIBUTES[0],
          [sequelize.literal("`shipment_tag->m2m_tag`.tag_id"), "tag_id"],
          [sequelize.literal("`shipment_tag->m2m_tag`.name"), "name"],
          [sequelize.literal("`shipment_tag->m2m_tag`.color"), "color"],
          [
            sequelize.literal("`shipment_tag->m2m_tag`.company_id"),
            "company_id",
          ],
        ],
        include: {
          model: tag,
          as: "m2m_tag",
          attributes: [],
        },
      },
      {
        model: customer,
        required: true,
        as: "customer_job",
        attributes: [...COMMON_CUSTOMER_ATTRIBUTES],
        include: [
          {
            model: tag_customer,
            as: "customer_tag",
            required: false,
            attributes: [
              "tag_id",
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.tag_id"
                ),
                "tag_id",
              ],
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.name"
                ),
                "name",
              ],
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.color"
                ),
                "color",
              ],
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.company_id"
                ),
                "company_id",
              ],
            ],
            include: {
              model: tag,
              as: "m2m_customer_tag",
              attributes: [],
            },
          },
        ],
      },
      {
        model: shipment_job_assign_worker_list,
        where: {
          shipment_job_id: { [Op.in]: job_id_array },
        },
        attributes: [
          "staff_id",
          "role",
          [
            sequelize.literal(
              "(select first_name FROM `staffs` where staff_id = assign_worker.staff_id) "
            ),
            "first_name",
          ],
          [
            sequelize.literal(
              "(select last_name FROM `staffs` where staff_id = assign_worker.staff_id) "
            ),
            "last_name",
          ],
          [
            sequelize.literal(
              `(CASE WHEN assign_worker.staff_id = ${staff_id} THEN 'yes' ELSE 'no' END)`
            ),
            "is_login_user",
          ],
        ],
        required: roles === "ADMIN" ? false : true,
        as: "assign_worker",
        include: [
          {
            model: staff,
            attributes: [],
            where: {
              is_deleted: 0,
              status: "active",
            },
            as: "assign_worker_detail",
          },
        ],
      },

      // {
      // 	model: qr_code,
      // 	where: {
      // 		job_id: { [Op.in]: job_id_array },
      // 		updated_at: { [Op.gte]: request.date, [Op.lt]: new Date() },
      // 		type: "Shipment"
      // 	},
      // 	attributes: [
      // 		"qr_code_id",
      // 		"type",
      // 		"company_id",
      // 		"job_id",
      // 		["random_number", "qr_generate_code"],
      // 		[
      // 			sequelize.literal(
      // 				"(select LPAD(label_number,8,0) FROM qr_codes where `job_qr`.`qr_code_id` = qr_codes.qr_code_id)"
      // 			),
      // 			"label_number",
      // 		],
      // 		[
      // 			sequelize.literal(
      // 				"(IF(`job_qr`.`qr_code_id` in (select distinct qr_id from shipment_inventories), 'yes', 'no'))"
      // 			),
      // 			"is_qr_scanned",
      // 		],
      // 	],
      // 	required: false,
      // 	as: "job_qr",
      // },
    ],

    raw: false,
    order: [["shipment_job_id", "DESC"]],
    // limit: Const_Page_Count,
    // offset: offset,
    distinct: true,
  });
};

exports.deleteJobList = async (request, fieldsAndValues, staff_id) => {
  const job_id_array = [];
  let job_id_list;
  const { roles, company_id } = await staff.findOne({
    where: { staff_id: staff_id },
    raw: true,
  });

  if (roles === "ADMIN") {
    job_id_list = await shipment_job.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { company_id: company_id },
      raw: true,
    });
  } else {
    job_id_list = await shipment_job_assign_worker.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { staff_id: staff_id },
      raw: true,
    });
  }
  if (job_id_list.rows.length > 0) {
    job_id_list.rows.forEach((element) => {
      job_id_array.push(element.shipment_job_id);
    });
  }
  let Const_Page_Count =
    request.page_no && request.page_no > 0 ? JOB_LIST_PER_PAGE : 1000000;
  let offset =
    request.page_no && request.page_no > 0
      ? Const_Page_Count * Number(request.page_no - 1)
      : 0;

  return await shipment_job.findAndCountAll({
    where: {
      company_id: company_id,
      deletedAt: { [Op.gte]: request.date, [Op.lt]: new Date() },
      [Op.or]: [
        {
          shipment_name: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
        {
          job_number: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
        {
          email: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
      ],
      is_job_complete_flag: 0,
    },
    paranoid: false,
    logging: console.log,
    attributes: [
      ...COMMON_JOB_ATTRIBUTES,
      [
        sequelize.literal(
          "(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_items",
      ],
      [
        sequelize.literal(
          "(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_volume",
      ],
      [
        sequelize.literal(
          "(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
        ),
        "firearms_total_quantity",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
        ),
        "total_cartons",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
        ),
        "total_cartons_cp",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
        ),
        "total_cartons_pbo",
      ],

      [
        sequelize.literal(
          '(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
        ),
        "total_high_value",
      ],

      [
        sequelize.literal(
          "(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_pads_used",
      ],

      [
        sequelize.literal(
          '(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_items",
      ],
      [
        sequelize.literal(
          "(select CONCAT(first_name,' ',(IF(last_name IS NULL, '', last_name)))  FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "customer_name",
      ],
      [
        sequelize.literal(
          "(select account_id FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "account_id",
      ],
      [
        sequelize.literal(
          "(select name  FROM `shipment_type_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id ) "
        ),
        "shipment_type_name",
      ],

      [
        sequelize.literal(
          "(select (IF(phone IS NULL, '', phone))  FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "customer_phone",
      ],
      [
        sequelize.literal("(IF(shipment_name IS NULL, '', shipment_name))"),
        "shipment_name",
      ],
      [
        sequelize.literal(
          "(select name FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_status",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_item = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_item",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_exceptions",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN show_no_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "show_no_exceptions",
      ],
      [
        sequelize.literal(
          "(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_stage",
      ],
      [
        sequelize.literal(
          "(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
        ),
        "total_stages",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN order_of_stages = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_first_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN allow_default_manual_label = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "allow_default_manual_label",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN add_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "add_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN enable_partial_complete_stage = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "enable_partial_complete_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_into_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_into_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "assign_storage_units_to_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "unassign_storage_units_from_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_out_of_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_out_of_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN supervisor_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_supervisor_signature_require",
      ],

      [
        sequelize.literal(
          "(select why_supervisor_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_supervisor_signature_require_note",
      ],

      [
        sequelize.literal(
          "(select CASE WHEN customer_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_customer_signature_require",
      ],

      [
        sequelize.literal(
          "(select why_customer_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_customer_signature_require_note",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`
        ),
        "is_job_complete",
      ],
      [
        sequelize.literal(
          `(if(
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL) = 
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "1")
					,"yes","no"))`
        ),
        "isAllItemScanned",
      ],
    ],
    include: [
      {
        model: company,
        attributes: ["company_id"],
        required: true,
        as: "job_company",
        include: [
          {
            model: staff,
            where: { roles: "ADMIN", is_deleted: 0, status: "active" },
            attributes: [
              "staff_id",
              ["roles", "role"],
              "first_name",
              "last_name",
            ],
            required: true,
            as: "staffs",
          },
        ],
      },
      {
        model: tag_shipment,
        as: "shipment_tag",
        required: false,
        attributes: [
          COMMON_JOB_TAG_ATTRIBUTES[0],
          [sequelize.literal("`shipment_tag->m2m_tag`.tag_id"), "tag_id"],
          [sequelize.literal("`shipment_tag->m2m_tag`.name"), "name"],
          [sequelize.literal("`shipment_tag->m2m_tag`.color"), "color"],
          [
            sequelize.literal("`shipment_tag->m2m_tag`.company_id"),
            "company_id",
          ],
        ],
        include: {
          model: tag,
          as: "m2m_tag",
          attributes: [],
        },
      },
      {
        model: customer,
        required: true,
        as: "customer_job",
        attributes: [...COMMON_CUSTOMER_ATTRIBUTES],
        include: [
          {
            model: tag_customer,
            as: "customer_tag",
            required: false,
            attributes: [
              "tag_id",
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.tag_id"
                ),
                "tag_id",
              ],
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.name"
                ),
                "name",
              ],
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.color"
                ),
                "color",
              ],
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.company_id"
                ),
                "company_id",
              ],
            ],
            include: {
              model: tag,
              as: "m2m_customer_tag",
              attributes: [],
            },
          },
        ],
      },
      {
        model: shipment_job_assign_worker_list,
        where: {
          shipment_job_id: { [Op.in]: job_id_array },
        },
        attributes: [
          "staff_id",
          "role",
          [
            sequelize.literal(
              "(select first_name FROM `staffs` where staff_id = assign_worker.staff_id) "
            ),
            "first_name",
          ],
          [
            sequelize.literal(
              "(select last_name FROM `staffs` where staff_id = assign_worker.staff_id) "
            ),
            "last_name",
          ],
          [
            sequelize.literal(
              `(CASE WHEN assign_worker.staff_id = ${staff_id} THEN 'yes' ELSE 'no' END)`
            ),
            "is_login_user",
          ],
        ],
        required: roles === "ADMIN" ? false : true,
        as: "assign_worker",
        include: [
          {
            model: staff,
            attributes: [],
            required: false,
            as: "assign_worker_detail",
          },
        ],
      },

      {
        model: qr_code,
        where: {
          job_id: { [Op.in]: job_id_array },
          created_at: { [Op.gte]: request.date, [Op.lt]: new Date() },
          type: "Shipment",
        },
        attributes: [
          "qr_code_id",
          "type",
          "company_id",
          "job_id",
          ["random_number", "qr_generate_code"],
          [
            sequelize.literal(
              "(select LPAD(label_number,8,0) FROM qr_codes where `job_qr`.`qr_code_id` = qr_codes.qr_code_id)"
            ),
            "label_number",
          ],
          [
            sequelize.literal(
              "(IF(`job_qr`.`qr_code_id` in (select distinct qr_id from shipment_inventories), 'yes', 'no'))"
            ),
            "is_qr_scanned",
          ],
        ],
        required: false,
        as: "job_qr",
      },
    ],

    raw: false,
    order: [["shipment_job_id", "DESC"]],
    // limit: Const_Page_Count,
    // offset: offset,
    distinct: true,
  });
};

exports.completeJobList = async (request, fieldsAndValues, staff_id) => {
  const job_id_array = [];
  let job_id_list;
  const { roles, company_id } = await staff.findOne({
    where: { staff_id: staff_id },
    raw: true,
  });

  if (roles === "ADMIN") {
    job_id_list = await shipment_job.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { company_id: company_id },
      raw: true,
    });
  } else {
    job_id_list = await shipment_job_assign_worker.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { staff_id: staff_id },
      raw: true,
    });
  }
  if (job_id_list.rows.length > 0) {
    job_id_list.rows.forEach((element) => {
      job_id_array.push(element.shipment_job_id);
    });
  }
  let Const_Page_Count =
    request.page_no && request.page_no > 0 ? JOB_LIST_PER_PAGE : 1000000;
  let offset =
    request.page_no && request.page_no > 0
      ? Const_Page_Count * Number(request.page_no - 1)
      : 0;

  return await shipment_job.findAndCountAll({
    where: {
      company_id: company_id,
      updated_at: { [Op.gte]: request.date, [Op.lt]: new Date() },
      [Op.or]: [
        {
          shipment_name: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
        {
          job_number: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
        {
          email: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
      ],
      is_job_complete_flag: 1,
    },
    logging: console.log,
    attributes: [
      ...COMMON_JOB_ATTRIBUTES,
      [
        sequelize.literal(
          "(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_items",
      ],
      [
        sequelize.literal(
          "(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_volume",
      ],
      [
        sequelize.literal(
          "(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
        ),
        "firearms_total_quantity",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
        ),
        "total_cartons",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
        ),
        "total_cartons_cp",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
        ),
        "total_cartons_pbo",
      ],

      [
        sequelize.literal(
          '(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
        ),
        "total_high_value",
      ],

      [
        sequelize.literal(
          "(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_pads_used",
      ],

      [
        sequelize.literal(
          '(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_items",
      ],
      [
        sequelize.literal(
          "(select CONCAT(first_name,' ',(IF(last_name IS NULL, '', last_name)))  FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "customer_name",
      ],
      [
        sequelize.literal(
          "(select account_id FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "account_id",
      ],
      [
        sequelize.literal(
          "(select name  FROM `shipment_type_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id ) "
        ),
        "shipment_type_name",
      ],

      [
        sequelize.literal(
          "(select (IF(phone IS NULL, '', phone))  FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "customer_phone",
      ],
      [
        sequelize.literal("(IF(shipment_name IS NULL, '', shipment_name))"),
        "shipment_name",
      ],
      [
        sequelize.literal(
          "(select name FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_status",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_item = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_item",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_exceptions",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN show_no_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "show_no_exceptions",
      ],
      [
        sequelize.literal(
          "(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_stage",
      ],
      [
        sequelize.literal(
          "(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
        ),
        "total_stages",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN order_of_stages = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_first_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN allow_default_manual_label = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "allow_default_manual_label",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN add_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "add_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN enable_partial_complete_stage = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "enable_partial_complete_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_into_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_into_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "assign_storage_units_to_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "unassign_storage_units_from_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_out_of_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_out_of_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN supervisor_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_supervisor_signature_require",
      ],

      [
        sequelize.literal(
          "(select why_supervisor_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_supervisor_signature_require_note",
      ],

      [
        sequelize.literal(
          "(select CASE WHEN customer_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_customer_signature_require",
      ],

      [
        sequelize.literal(
          "(select why_customer_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_customer_signature_require_note",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`
        ),
        "is_job_complete",
      ],
      [
        sequelize.literal(
          `(if(
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL) = 
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "1")
					,"yes","no"))`
        ),
        "isAllItemScanned",
      ],
    ],
    include: [
      {
        model: company,
        attributes: ["company_id"],
        required: true,
        as: "job_company",
        include: [
          {
            model: staff,
            where: { roles: "ADMIN", is_deleted: 0, status: "active" },
            attributes: [
              "staff_id",
              ["roles", "role"],
              "first_name",
              "last_name",
            ],
            required: true,
            as: "staffs",
          },
        ],
      },
      {
        model: tag_shipment,
        as: "shipment_tag",
        required: false,
        attributes: [
          COMMON_JOB_TAG_ATTRIBUTES[0],
          [sequelize.literal("`shipment_tag->m2m_tag`.tag_id"), "tag_id"],
          [sequelize.literal("`shipment_tag->m2m_tag`.name"), "name"],
          [sequelize.literal("`shipment_tag->m2m_tag`.color"), "color"],
          [
            sequelize.literal("`shipment_tag->m2m_tag`.company_id"),
            "company_id",
          ],
        ],
        include: {
          model: tag,
          as: "m2m_tag",
          attributes: [],
        },
      },
      {
        model: customer,
        required: true,
        as: "customer_job",
        attributes: [...COMMON_CUSTOMER_ATTRIBUTES],
        include: [
          {
            model: tag_customer,
            as: "customer_tag",
            required: false,
            attributes: [
              "tag_id",
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.tag_id"
                ),
                "tag_id",
              ],
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.name"
                ),
                "name",
              ],
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.color"
                ),
                "color",
              ],
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.company_id"
                ),
                "company_id",
              ],
            ],
            include: {
              model: tag,
              as: "m2m_customer_tag",
              attributes: [],
            },
          },
        ],
      },
      {
        model: shipment_job_assign_worker_list,
        where: {
          shipment_job_id: { [Op.in]: job_id_array },
        },
        attributes: [
          "staff_id",
          "role",
          [
            sequelize.literal(
              "(select first_name FROM `staffs` where staff_id = assign_worker.staff_id) "
            ),
            "first_name",
          ],
          [
            sequelize.literal(
              "(select last_name FROM `staffs` where staff_id = assign_worker.staff_id) "
            ),
            "last_name",
          ],
          [
            sequelize.literal(
              `(CASE WHEN assign_worker.staff_id = ${staff_id} THEN 'yes' ELSE 'no' END)`
            ),
            "is_login_user",
          ],
        ],
        required: roles === "ADMIN" ? false : true,
        as: "assign_worker",
        include: [
          {
            model: staff,
            attributes: [],
            required: false,
            as: "assign_worker_detail",
          },
        ],
      },

      {
        model: qr_code,
        where: {
          job_id: { [Op.in]: job_id_array },
          created_at: { [Op.gte]: request.date, [Op.lt]: new Date() },
          type: "Shipment",
        },
        attributes: [
          "qr_code_id",
          "type",
          "company_id",
          "job_id",
          ["random_number", "qr_generate_code"],
          [
            sequelize.literal(
              "(select LPAD(label_number,8,0) FROM qr_codes where `job_qr`.`qr_code_id` = qr_codes.qr_code_id)"
            ),
            "label_number",
          ],
          [
            sequelize.literal(
              "(IF(`job_qr`.`qr_code_id` in (select distinct qr_id from shipment_inventories), 'yes', 'no'))"
            ),
            "is_qr_scanned",
          ],
        ],
        required: false,
        as: "job_qr",
      },
    ],

    raw: false,
    order: [["shipment_job_id", "DESC"]],
    // limit: Const_Page_Count,
    // offset: offset,
    distinct: true,
  });
};

exports.getQrList = async (request, fieldsAndValues, staff_id) => {
  const job_id_array = [];
  let job_id_list;
  const { roles, company_id } = await staff.findOne({
    where: { staff_id: staff_id },
    raw: true,
  });

  if (roles === "ADMIN") {
    job_id_list = await shipment_job.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { company_id: company_id },
      raw: true,
    });
  } else {
    job_id_list = await shipment_job_assign_worker.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { staff_id: staff_id },
      raw: true,
    });
  }
  if (job_id_list.rows.length > 0) {
    job_id_list.rows.forEach((element) => {
      job_id_array.push(element.shipment_job_id);
    });
  }

  return await shipment_job.findAndCountAll({
    where: {
      company_id: company_id,
      is_job_complete_flag: 0,
    },
    logging: console.log,
    attributes: [["shipment_job_id", "job_id"], "shipment_name"],
    include: [
      {
        model: qr_code,
        where: {
          job_id: { [Op.in]: job_id_array },
          type: "Shipment",
        },
        attributes: [
          "qr_code_id",
          "type",
          "company_id",
          "job_id",
          ["random_number", "qr_generate_code"],
          [
            sequelize.literal(
              "(select LPAD(label_number,8,0) FROM qr_codes where `job_qr`.`qr_code_id` = qr_codes.qr_code_id)"
            ),
            "label_number",
          ],
          [
            sequelize.literal(
              "(IF(`job_qr`.`qr_code_id` in (select distinct qr_id from shipment_inventories), 'yes', 'no'))"
            ),
            "is_qr_scanned",
          ],
        ],
        required: false,
        as: "job_qr",
      },
    ],
    raw: false,
    order: [["shipment_job_id", "DESC"]],
    distinct: true,
  });
};

exports.getJobList2 = async (request, fieldsAndValues, staff_id) => {
  const job_id_array = [];
  let job_id_list;
  const { roles, company_id } = await staff.findOne({
    where: { staff_id: staff_id },
    raw: true,
  });

  if (roles === "ADMIN") {
    job_id_list = await shipment_job.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { company_id: company_id },
      raw: true,
    });
  } else {
    job_id_list = await shipment_job_assign_worker.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { staff_id: staff_id },
      raw: true,
    });
  }
  if (job_id_list.rows.length > 0) {
    job_id_list.rows.forEach((element) => {
      job_id_array.push(element.shipment_job_id);
    });
  }

  return await shipment_job.findAndCountAll({
    where: {
      company_id: company_id,
      is_job_complete_flag: 0,
    },
    logging: console.log,
    attributes: [["shipment_job_id", "job_id"], "shipment_name"],
    include: [
      {
        model: qr_code,
        where: {
          job_id: { [Op.in]: job_id_array },
          created_at: { [Op.gte]: request.date, [Op.lt]: new Date() },
          type: "Shipment",
        },
        attributes: [
          "qr_code_id",
          "type",
          "company_id",
          "job_id",
          ["random_number", "qr_generate_code"],
          [
            sequelize.literal(
              "(select LPAD(label_number,8,0) FROM qr_codes where `job_qr`.`qr_code_id` = qr_codes.qr_code_id)"
            ),
            "label_number",
          ],
          [
            sequelize.literal(
              "(IF(`job_qr`.`qr_code_id` in (select distinct qr_id from shipment_inventories), 'yes', 'no'))"
            ),
            "is_qr_scanned",
          ],
        ],
        required: false,
        as: "job_qr",
      },
    ],
    raw: false,
    order: [["shipment_job_id", "DESC"]],
    distinct: true,
  });
};

exports.updateQrList = async (request, fieldsAndValues, staff_id) => {
  const job_id_array = [];
  let job_id_list;
  const { roles, company_id } = await staff.findOne({
    where: { staff_id: staff_id },
    raw: true,
  });

  if (roles === "ADMIN") {
    job_id_list = await shipment_job.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { company_id: company_id },
      raw: true,
    });
  } else {
    job_id_list = await shipment_job_assign_worker.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { staff_id: staff_id },
      raw: true,
    });
  }
  if (job_id_list.rows.length > 0) {
    job_id_list.rows.forEach((element) => {
      job_id_array.push(element.shipment_job_id);
    });
  }

  return await shipment_job.findAndCountAll({
    where: {
      company_id: company_id,
      is_job_complete_flag: 0,
    },
    logging: console.log,
    attributes: [["shipment_job_id", "job_id"], "shipment_name"],
    include: [
      {
        model: shipment_inventory,
        where: {
          shipment_job_id: { [Op.in]: job_id_array },
          created_at: { [Op.gte]: request.date, [Op.lt]: new Date() },
        },
        include: [
          {
            model: qr_code,
            required: false,
            as: "item_qr",
            attributes: [
              "qr_code_id",
              "type",
              "company_id",
              "job_id",
              ["random_number", "qr_generate_code"],
            ],
          },
        ],
        attributes: ["shipment_job_id"],
        required: false,
        as: "job_items",
      },
    ],
    raw: false,
    order: [["shipment_job_id", "DESC"]],
    distinct: true,
  });
};

exports.deleteQrList = async (request, fieldsAndValues, staff_id) => {
  const job_id_array = [];
  let job_id_list;
  const { roles, company_id } = await staff.findOne({
    where: { staff_id: staff_id },
    raw: true,
  });

  if (roles === "ADMIN") {
    job_id_list = await shipment_job.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { company_id: company_id },
      raw: true,
    });
  } else {
    job_id_list = await shipment_job_assign_worker.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { staff_id: staff_id },
      raw: true,
    });
  }
  if (job_id_list.rows.length > 0) {
    job_id_list.rows.forEach((element) => {
      job_id_array.push(element.shipment_job_id);
    });
  }

  return await shipment_job.findAndCountAll({
    where: {
      company_id: company_id,

      is_job_complete_flag: 0,
    },
    logging: console.log,
    attributes: [["shipment_job_id", "job_id"], "shipment_name"],
    include: [
      {
        model: qr_code,
        where: {
          job_id: { [Op.in]: job_id_array },
          deletedAt: { [Op.gte]: request.date, [Op.lt]: new Date() },
          type: "Shipment",
        },
        paranoid: false,

        attributes: [
          "qr_code_id",
          "type",
          "company_id",
          "job_id",
          ["random_number", "qr_generate_code"],
          [
            sequelize.literal(
              "(select LPAD(label_number,8,0) FROM qr_codes where `job_qr`.`qr_code_id` = qr_codes.qr_code_id)"
            ),
            "label_number",
          ],
          [
            sequelize.literal(
              "(IF(`job_qr`.`qr_code_id` in (select distinct qr_id from shipment_inventories), 'yes', 'no'))"
            ),
            "is_qr_scanned",
          ],
        ],
        required: false,
        as: "job_qr",
      },
    ],
    raw: false,
    order: [["shipment_job_id", "DESC"]],
    distinct: true,
  });
};

exports.getJobListByName = async (request, staff_id, fieldsAndValues) => {
  const job_id_array = [];
  let job_id_list;
  const { roles, company_id } = await staff.findOne({
    where: { staff_id: staff_id },
    raw: true,
  });

  if (roles === "ADMIN") {
    job_id_list = await shipment_job.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { company_id: company_id },
      raw: true,
    });
  } else {
    job_id_list = await shipment_job_assign_worker.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { staff_id: staff_id },
      raw: true,
    });
  }
  if (job_id_list.rows.length > 0) {
    job_id_list.rows.forEach((element) => {
      job_id_array.push(element.shipment_job_id);
    });
  }

  return await shipment_job.findAndCountAll({
    where: {
      company_id: company_id,
      [Op.or]: [
        {
          shipment_name: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
        {
          job_number: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
        {
          email: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
      ],
      is_job_complete_flag: 0,
    },
    logging: console.log,
    attributes: [
      ...COMMON_JOB_ATTRIBUTES,
      [
        sequelize.literal(
          "(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_items",
      ],
      [
        sequelize.literal(
          "(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_volume",
      ],
      [
        sequelize.literal(
          "(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
        ),
        "firearms_total_quantity",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
        ),
        "total_cartons",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
        ),
        "total_cartons_cp",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
        ),
        "total_cartons_pbo",
      ],

      [
        sequelize.literal(
          '(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
        ),
        "total_high_value",
      ],

      [
        sequelize.literal(
          "(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_pads_used",
      ],

      [
        sequelize.literal(
          '(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_items",
      ],
      [
        sequelize.literal(
          "(select CONCAT(first_name,' ',(IF(last_name IS NULL, '', last_name)))  FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "customer_name",
      ],
      [
        sequelize.literal(
          "(select account_id FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "account_id",
      ],
      [
        sequelize.literal(
          "(select name  FROM `shipment_type_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id ) "
        ),
        "shipment_type_name",
      ],

      [
        sequelize.literal(
          "(select (IF(phone IS NULL, '', phone))  FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "customer_phone",
      ],
      [
        sequelize.literal("(IF(shipment_name IS NULL, '', shipment_name))"),
        "shipment_name",
      ],
      [
        sequelize.literal(
          "(select name FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_status",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_item = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_item",
      ],

      [
        sequelize.literal(
          "(select CASE WHEN is_add_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_exceptions",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN show_no_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "show_no_exceptions",
      ],
      [
        sequelize.literal(
          "(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_stage",
      ],
      [
        sequelize.literal(
          "(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
        ),
        "total_stages",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN order_of_stages = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_first_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN allow_default_manual_label = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "allow_default_manual_label",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN add_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "add_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN enable_partial_complete_stage = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "enable_partial_complete_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_into_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_into_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "assign_storage_units_to_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "unassign_storage_units_from_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_out_of_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_out_of_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN supervisor_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_supervisor_signature_require",
      ],

      [
        sequelize.literal(
          "(select why_supervisor_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_supervisor_signature_require_note",
      ],

      [
        sequelize.literal(
          "(select CASE WHEN customer_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_customer_signature_require",
      ],

      [
        sequelize.literal(
          "(select why_customer_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_customer_signature_require_note",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`
        ),
        "is_job_complete",
      ],
      [
        sequelize.literal(
          `(if(
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL) = 
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "1")
					,"yes","no"))`
        ),
        "isAllItemScanned",
      ],
    ],
    include: [
      {
        model: company,
        attributes: ["company_id"],
        required: true,
        as: "job_company",
        include: [
          {
            model: staff,
            where: { roles: "ADMIN", is_deleted: 0, status: "active" },
            attributes: [
              "staff_id",
              ["roles", "role"],
              "first_name",
              "last_name",
            ],
            required: true,
            as: "staffs",
          },
        ],
      },
      {
        model: tag_shipment,
        as: "shipment_tag",
        required: false,
        attributes: [
          COMMON_JOB_TAG_ATTRIBUTES[0],
          [sequelize.literal("`shipment_tag->m2m_tag`.tag_id"), "tag_id"],
          [sequelize.literal("`shipment_tag->m2m_tag`.name"), "name"],
          [sequelize.literal("`shipment_tag->m2m_tag`.color"), "color"],
          [
            sequelize.literal("`shipment_tag->m2m_tag`.company_id"),
            "company_id",
          ],
        ],
        include: {
          model: tag,
          as: "m2m_tag",
          attributes: [],
        },
      },
      {
        model: customer,
        required: true,
        as: "customer_job",
        attributes: [...COMMON_CUSTOMER_ATTRIBUTES],
        include: [
          {
            model: tag_customer,
            as: "customer_tag",
            required: false,
            attributes: [
              "tag_id",
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.tag_id"
                ),
                "tag_id",
              ],
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.name"
                ),
                "name",
              ],
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.color"
                ),
                "color",
              ],
              [
                sequelize.literal(
                  "`customer_job->customer_tag->m2m_customer_tag`.company_id"
                ),
                "company_id",
              ],
            ],
            include: {
              model: tag,
              as: "m2m_customer_tag",
              attributes: [],
            },
          },
        ],
      },

      {
        model: qr_code,
        where: {
          job_id: { [Op.in]: job_id_array },
          type: "Shipment",
        },
        attributes: [
          "qr_code_id",
          "type",
          "company_id",
          "job_id",
          ["random_number", "qr_generate_code"],
          [
            sequelize.literal(
              "(select LPAD(label_number,8,0) FROM qr_codes where `job_qr`.`qr_code_id` = qr_codes.qr_code_id)"
            ),
            "label_number",
          ],
          [
            sequelize.literal(
              "(IF(`job_qr`.`qr_code_id` in (select distinct qr_id from shipment_inventories), 'yes', 'no'))"
            ),
            "is_qr_scanned",
          ],
        ],
        required: false,
        as: "job_qr",
      },
    ],

    raw: false,
    distinct: true,
    order: [["shipment_job_id", "DESC"]],
  });
};

exports.getJobSpecific = async (job_id, staff_id) => {
  const job_id_array = [];
  let job_id_list;

  const { roles, company_id } = await staff.findOne({
    where: { staff_id: staff_id },
    raw: true,
  });
  if (roles === "ADMIN") {
    job_id_list = await shipment_job.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { company_id: company_id },
      raw: true,
    });
  } else {
    job_id_list = await shipment_job_assign_worker.findAndCountAll({
      attributes: ["shipment_job_id"],
      where: { staff_id: staff_id },
      raw: true,
    });
  }

  if (job_id_list.rows.length > 0) {
    job_id_list.rows.forEach((element) => {
      job_id_array.push(element.shipment_job_id);
    });
  }

  return shipment_job.findByPk(job_id, {
    attributes: [
      ...COMMON_JOB_ATTRIBUTES,
      [
        sequelize.literal(
          "(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_items",
      ],
      [
        sequelize.literal(
          "(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_volume",
      ],
      [
        sequelize.literal(
          "(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
        ),
        "firearms_total_quantity",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
        ),
        "total_cartons",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
        ),
        "total_cartons_cp",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
        ),
        "total_cartons_pbo",
      ],

      [
        sequelize.literal(
          '(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
        ),
        "total_high_value",
      ],

      [
        sequelize.literal(
          "(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_pads_used",
      ],

      [
        sequelize.literal(
          '(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_items",
      ],
      [
        sequelize.literal(
          "(select first_name  FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "customer_name",
      ],
      [
        sequelize.literal(
          "(select account_id FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "account_id",
      ],
      [
        sequelize.literal(
          "(select name FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_status",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_item = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_item",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_exceptions",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN show_no_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "show_no_exceptions",
      ],
      [
        sequelize.literal(
          "(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_stage",
      ],
      [
        sequelize.literal(
          "(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
        ),
        "total_stages",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN order_of_stages = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_first_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN allow_default_manual_label = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "allow_default_manual_label",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN add_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "add_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN enable_partial_complete_stage = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "enable_partial_complete_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_into_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_into_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "assign_storage_units_to_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "unassign_storage_units_from_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_out_of_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_out_of_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN supervisor_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_supervisor_signature_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN customer_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_customer_signature_require",
      ],
      // [sequelize.literal(is_job_complete), "is_job_complete"],
      [
        sequelize.literal(
          `(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`
        ),
        "is_job_complete",
      ],
    ],
    include: [
      {
        model: company,
        attributes: ["company_id"],
        required: roles === "ADMIN" ? true : false,
        as: "job_company",
        include: [
          {
            model: staff,
            where: { roles: "ADMIN", is_deleted: 0, status: "active" },
            attributes: [
              "staff_id",
              ["roles", "role"],
              "first_name",
              "last_name",
            ],
            required: roles === "ADMIN" ? true : false,
            as: "staffs",
          },
        ],
      },
      {
        model: customer,
        attributes: [],
        required: true,
        as: "customer_job",
      },
      // {
      // 	model: shipment_job_assign_worker,
      // 	where: {
      // 		shipment_job_id: { [Op.in]: job_id_array },
      // 	},
      // 	attributes: [
      // 		"staff_id",
      // 		"role",
      // 		[
      // 			sequelize.literal("(select first_name FROM `staffs` where staff_id = job_worker.staff_id) "),
      // 			"first_name",
      // 		],
      // 		[
      // 			sequelize.literal("(select last_name FROM `staffs` where staff_id = job_worker.staff_id) "),
      // 			"last_name",
      // 		],
      // 	],
      // 	required: roles === "ADMIN" ? false : true,
      // 	as: "job_worker",
      // 	include: [
      // 		{
      // 			model: staff,
      // 			attributes: [],
      // 			required: roles === "ADMIN" ? false : true,
      // 			as: "job_worker_detail",
      // 		},
      // 	],
      // },
      // {
      // 	model: qr_code,
      // 	where: {
      // 		job_id: { [Op.in]: job_id_array },
      // 	},
      // 	attributes: ["qr_code_id", ["random_number", "qr_generate_code"]],
      // 	required: true,
      // 	as: "job_qr",
      // },
    ],
    raw: false,
    subQuery: false,
  });
};

exports.getRoomList = async (company, transaction) => {
  return await shipment_room.findAll({
    where: {
      company_id: company,
      status: "Active",
    },
    attributes: [["shipment_room_id", "room_id"], "name"],
    order: [["name", "ASC"]],
    raw: true,
  });
};

exports.jobItemRoomListModel = async (shipmentId) => {
  const rooms = await shipment_inventory.findAll({
    where: {
      shipment_job_id: shipmentId,
    },
    attributes: ["room_id"],
    include: [
      {
        model: shipment_room,
        as: "room",
        attributes: ["name"],
      },
    ],
    group: ["room_id"],
    raw: true,
  });
  const formattedRooms = rooms.map((item) => ({
    room_id: item.room_id,
    room_name: item["room.name"],
  }));
  return formattedRooms;
};

exports.jobItemTagListModel = async (shipmentId) => {
  const tags = await shipment_inventory.findAll({
    where: {
      shipment_job_id: shipmentId,
    },
    attributes: [],
    include: [
      {
        model: tag_item,
        as: "item_tag",
        required: false,
        attributes: [
          COMMON_ITEM_TAG_ATTRIBUTES[0],
          [sequelize.literal("`item_tag->m2m_item_tag`.tag_id"), "tag_id"],
          [sequelize.literal("`item_tag->m2m_item_tag`.name"), "name"],
          [sequelize.literal("`item_tag->m2m_item_tag`.color"), "color"],
          [
            sequelize.literal("`item_tag->m2m_item_tag`.company_id"),
            "company_id",
          ],
        ],
        include: {
          model: tag,
          as: "m2m_item_tag",
          attributes: [],
        },
      },
    ],
  });

  // Extract unique tags based on tag_id
  const uniqueTags = [];
  const uniqueTagIds = new Set();

  tags.forEach((tagObject) => {
    tagObject.item_tag.forEach((tag) => {
      const { tag_id } = tag;
      if (!uniqueTagIds.has(tag_id)) {
        uniqueTagIds.add(tag_id);
        uniqueTags.push(tag);
      }
    });
  });

  return uniqueTags;
};

exports.roomListEditItem = async (userDetails) => {
  if (userDetails.staff_id !== null) {
    let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(
      userDetails.staff_id
    );
    return await shipment_room.findAll({
      where: {
        company_id: getStaffDetails.company_id,
        status: "Active",
      },
      attributes: [["shipment_room_id", "room_id"], "name"],
      raw: true,
    });
  } else {
    return await shipment_room.findAll({
      where: {
        company_id: userDetails.company_id,
        status: "Active",
      },
      attributes: [["shipment_room_id", "room_id"], "name"],
      raw: true,
    });
  }
};

exports.userListEditItem = async (userDetails) => {
  if (userDetails.staff_id !== null) {
    let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(
      userDetails.staff_id
    );
    return await staff.findAll({
      where: {
        company_id: getStaffDetails.company_id,
        status: "Active",
        is_deleted: 0,
      },
      attributes: [
        "staff_id",
        [sequelize.literal('CONCAT(first_name," ",last_name)'), "first_name"],
        "email",
      ],
      raw: true,
    });
  } else {
    return await staff.findAll({
      where: {
        company_id: userDetails.company_id,
        status: "Active",
        is_deleted: 0,
      },
      attributes: [
        "staff_id",
        [sequelize.literal('CONCAT(first_name," ",last_name)'), "first_name"],
        "email",
      ],
      raw: true,
    });
  }
};

exports.unitListEditItem = async (fetchShipmentWarehouse) => {
  return await unit_list.findAll({
    where: {
      warehouseId: fetchShipmentWarehouse.warehouseId,
      [Op.or]: [
        { shipment_job_id: fetchShipmentWarehouse.shipment_job_id },
        { shipment_job_id: null },
      ],
      isActive: 1,
    },
    attributes: ["unit_id", "storage_unit_id", "name"],
    raw: true,
  });
};

exports.getExceptionList = async () => {
  return await shipment_exception.findAll({
    attributes: [["shipment_exception_id", "exception_id"], "name"],
    order: [["created_at", "ASC"]],
    raw: true,
  });
};

exports.getLocationList = async () => {
  return await shipment_location.findAll({
    attributes: [["shipment_location_id", "location_id"], "name"],
    order: [["created_at", "ASC"]],
    raw: true,
  });
};

exports.scriptAddItem = async (request, i, mediaFileList) => {
  let insertData = {
    shipment_job_id: request.job_id,
    room_id: request.room_id,
    qr_id: parseInt(request.qr_id) + i,
    item_name: request.item_name + i,
    description: request.desc,
    is_carton: request.is_carton == "true" || request.is_carton == true ? 1 : 0,
    packed_by_owner:
      request.packed_by &&
        (request.is_carton == "true" || request.is_carton == true) &&
        request.packed_by === "Packed by Owner"
        ? 1
        : 0,
    carrier_packed_user_id:
      request.packed_user_id &&
        (request.is_carton == "true" || request.is_carton == true) &&
        request.packed_user_id !== "" &&
        request.packed_user_id > 0
        ? request.packed_user_id
        : null,
    is_disassembled:
      request.is_disassembled == "true" || request.is_disassembled == true
        ? 1
        : 0,
    disassembled_by_owner:
      request.disassembled_by &&
        (request.is_disassembled == "true" || request.is_disassembled == true) &&
        request.disassembled_by.includes("Customer")
        ? 1
        : 0,
    disassembled_user_id:
      request.disassembled_user_id &&
        (request.is_disassembled == "true" || request.is_disassembled == true) &&
        request.disassembled_user_id !== "" &&
        request.disassembled_user_id > 0
        ? request.disassembled_user_id
        : null,
    is_electronics:
      request.is_electronics == "true" || request.is_electronics == true
        ? 1
        : 0,
    serial_number:
      request.serial_number &&
        (request.is_electronics == "true" || request.is_electronics == true)
        ? request.serial_number
        : "",
    is_high_value:
      request.is_high_value == "true" || request.is_high_value == true ? 1 : 0,
    declared_value:
      request.declared_value &&
        (request.is_high_value == "true" || request.is_high_value == true) &&
        request.declared_value !== ""
        ? request.declared_value
        : 0,
    is_pro_gear:
      request.is_pro_gear == "true" || request.is_pro_gear == true ? 1 : 0,
    pro_gear_weight:
      request.pro_gear_weight &&
        (request.is_pro_gear == "true" || request.is_pro_gear == true) &&
        request.pro_gear_weight !== ""
        ? request.pro_gear_weight
        : 0,
    volume:
      request.volume !== "" && request.volume !== null ? request.volume : 0,
    weight:
      request.weight !== "" && request.weight !== null ? request.weight : 0,
    pads_used:
      request.pads_used !== "" && request.pads_used !== null
        ? request.pads_used
        : 0,
    prepared_by:
      request.prepared_by &&
        request.prepared_by !== "" &&
        request.prepared_by > 0
        ? request.prepared_by
        : null,
    notes: request.notes,
    created_at: request.created_at ? request.created_at : moment(),
    progear_name:
      request.progear_name &&
        (request.is_pro_gear == "true" || request.is_pro_gear == true)
        ? request.progear_name
        : "",
    is_firearm:
      request.is_firearm == "true" || request.is_firearm == true ? 1 : 0,

    firmarm_serial_number:
      request.firmarm_serial_number &&
        (request.is_firearm == "true" || request.is_firearm == true)
        ? request.firmarm_serial_number
        : "",

    isManualLabel:
      request.isManualLabel == "true" || request.isManualLabel == true ? 1 : 0,

    label_no:
      request.label_no &&
        (request.isManualLabel == "true" || request.isManualLabel == true)
        ? request.label_no
        : 0,

    lot_no:
      request.lot_no &&
        (request.isManualLabel == "true" || request.isManualLabel == true)
        ? request.lot_no
        : "",

    color:
      request.color &&
        (request.isManualLabel == "true" || request.isManualLabel == true)
        ? request.color
        : "",
  };

  const itemData = await shipment_inventory.create(insertData);
  if (itemData.shipment_inventory_id > 0 && mediaFileList.length !== 0) {
    const multiData = [];

    mediaFileList.forEach((element) => {
      let Options = {};
      Options["shipment_inventory_id"] = itemData.shipment_inventory_id;
      Options["media"] = element.media;
      multiData.push(Options);
    });
    const multiDataCreate = await shipment_inventory_photo.bulkCreate(
      multiData,
      {
        returning: true,
      }
    );
    return multiDataCreate;
  }

  return itemData;
};

exports.addTagToInventoryModel = (tags) => {
  tag_item.bulkCreate(tags);
};

exports.deleteTagToInventoryModel = async (tags) =>
  tag_item.destroy({
    where: { tag_inventory_id: { [Op.in]: tags } },
  });

exports.addItem = async (
  request,
  mediaFileList,
  isThumbnailImageYes,
  thumbnailImageName
) => {
  let insertData = {
    shipment_job_id: request.job_id,
    storage_unit_id: request.storage_unit_id,
    unit_id: request.unit_id,
    inventory_stage_id: request.stage_id,
    room_id: request.room_id,
    qr_id: request.qr_id && request.qr_id,
    item_name: request.item_name,
    description: request.desc,
    is_carton: request.is_carton == "true" || request.is_carton == true ? 1 : 0,
    packed_by_owner:
      request.packed_by &&
        (request.is_carton == "true" || request.is_carton == true) &&
        request.packed_by === "Packed by Owner"
        ? 1
        : 0,
    carrier_packed_user_id:
      request.packed_user_id &&
        (request.is_carton == "true" || request.is_carton == true) &&
        request.packed_user_id !== "" &&
        request.packed_user_id == -1
        ? request.packed_user_id
        : request.packed_user_id > 0
          ? request.packed_user_id
          : null,

    carrier_packed_user_name_manually:
      (request.is_carton == "true" || request.is_carton == true) &&
        (request.packed_user_id == null ||
          request.packed_user_id == "" ||
          request.packed_user_id == undefined) &&
        request.packed_by !== "Packed by Owner"
        ? request.carrier_packed_user_name_manually == null ||
          request.carrier_packed_user_name_manually == "" ||
          request.carrier_packed_user_name_manually == undefined
          ? null
          : request.carrier_packed_user_name_manually
        : null,

    is_disassembled:
      request.is_disassembled == "true" || request.is_disassembled == true
        ? 1
        : 0,
    disassembled_by_owner:
      request.disassembled_by &&
        (request.is_disassembled == "true" || request.is_disassembled == true) &&
        request.disassembled_by.includes("Customer")
        ? 1
        : 0,

    disassembled_user_id:
      request.disassembled_user_id &&
        (request.is_disassembled == "true" || request.is_disassembled == true) &&
        request.disassembled_user_id !== "" &&
        request.disassembled_user_id == -1
        ? request.disassembled_user_id
        : request.disassembled_user_id > 0
          ? request.disassembled_user_id
          : null,

    disassembled_user_name_manually:
      (request.is_disassembled == "true" || request.is_disassembled == true) &&
        (request.disassembled_user_id == null ||
          request.disassembled_user_id == "" ||
          request.disassembled_user_id == undefined) &&
        request.disassembled_by !== "Customer"
        ? request.disassembled_user_name_manually == null ||
          request.disassembled_user_name_manually == "" ||
          request.disassembled_user_name_manually == undefined
          ? null
          : request.disassembled_user_name_manually
        : null,

    is_electronics:
      request.is_electronics == "true" || request.is_electronics == true
        ? 1
        : 0,
    serial_number:
      request.serial_number &&
        (request.is_electronics == "true" || request.is_electronics == true)
        ? request.serial_number
        : "",
    is_high_value:
      request.is_high_value == "true" || request.is_high_value == true ? 1 : 0,
    seal_number:
      request.seal_number &&
        (request.is_high_value == "true" || request.is_high_value == true)
        ? request.seal_number
        : "",
    declared_value:
      request.declared_value &&
        (request.is_high_value == "true" || request.is_high_value == true) &&
        request.declared_value !== ""
        ? request.declared_value
        : 0,
    is_pro_gear:
      request.is_pro_gear == "true" || request.is_pro_gear == true ? 1 : 0,
    pro_gear_weight:
      request.pro_gear_weight &&
        (request.is_pro_gear == "true" || request.is_pro_gear == true) &&
        request.pro_gear_weight !== ""
        ? request.pro_gear_weight
        : 0,
    volume:
      request.volume !== "" && request.volume !== null ? request.volume : 0,
    weight:
      request.weight !== "" && request.weight !== null ? request.weight : 0,
    pads_used:
      request.pads_used !== "" && request.pads_used !== null
        ? request.pads_used
        : 0,
    prepared_by:
      request.prepared_by &&
        request.prepared_by !== "" &&
        request.prepared_by == -1
        ? request.prepared_by
        : request.prepared_by > 0
          ? request.prepared_by
          : null,

    prepared_user_name_manually: request.prepared_user_name_manually,

    notes: request.notes,
    created_at: request.created_at ? request.created_at : moment(),
    progear_name:
      request.progear_name &&
        (request.is_pro_gear == "true" || request.is_pro_gear == true)
        ? request.progear_name
        : "",
    is_firearm:
      request.is_firearm == "true" || request.is_firearm == true ? 1 : 0,

    firmarm_serial_number:
      request.firmarm_serial_number &&
        (request.is_firearm == "true" || request.is_firearm == true)
        ? request.firmarm_serial_number
        : "",

    isManualLabel:
      request.isManualLabel == "true" || request.isManualLabel == true ? 1 : 0,

    label_no:
      request.label_no &&
        (request.isManualLabel == "true" || request.isManualLabel == true)
        ? request.label_no
        : 0,

    lot_no:
      request.lot_no &&
        (request.isManualLabel == "true" || request.isManualLabel == true)
        ? request.lot_no
        : "",

    color:
      request.color &&
        (request.isManualLabel == "true" || request.isManualLabel == true)
        ? request.color
        : "",

    is_add_dimension:
      request.is_add_dimension == "true" || request.is_add_dimension == true
        ? 1
        : 0,

    length:
      request.length &&
        (request.is_add_dimension == "true" || request.is_add_dimension == true)
        ? request.length
        : 0,

    width:
      request.width &&
        (request.is_add_dimension == "true" || request.is_add_dimension == true)
        ? request.width
        : 0,

    height:
      request.height &&
        (request.is_add_dimension == "true" || request.is_add_dimension == true)
        ? request.height
        : 0,
  };

  console.log("insertData", insertData);

  const itemData = await shipment_inventory.create(insertData);

  const updateResult = await shipment_type_stage_for_shipment.increment(
    {
      total_add_items_inventory_stage: 1,
    },
    {
      where: { local_shipment_stage_id: request.stage_id },
    }
  );

  let exceptions_array = new Function("return " + request.exceptions)();

  exceptions_array ? exceptions_array : (exceptions_array = []);
  if (itemData.shipment_inventory_id > 0 && exceptions_array.length > 0) {
    for (const element of exceptions_array) {
      let insertNoteData = {
        shipment_inventory_id: itemData.shipment_inventory_id,
        notes: element.notes,
      };
      const noteData = await shipment_inventory_exception_note.create(
        insertNoteData
      );
      if (
        noteData.shipment_inventory_exception_note_id > 0 &&
        element.eid !== ""
      ) {
        let exception_array = element.eid.split(",");

        const multiData = [];

        exception_array.forEach((element) => {
          let Options = {};
          Options["shipment_inventory_id"] = itemData.shipment_inventory_id;
          Options["exception_note_id"] =
            noteData.shipment_inventory_exception_note_id;
          Options["shipment_exception_id"] = element;
          multiData.push(Options);
        });
        const multiDataCreate = shipment_inventory_exception.bulkCreate(
          multiData,
          {
            returning: true,
          }
        );
      }

      if (
        noteData.shipment_inventory_exception_note_id > 0 &&
        element.lid !== ""
      ) {
        let location_array = element.lid.split(",");

        const multiData = [];

        location_array.forEach((element) => {
          let Options = {};
          Options["shipment_inventory_id"] = itemData.shipment_inventory_id;
          Options["exception_note_id"] =
            noteData.shipment_inventory_exception_note_id;
          Options["shipment_location_id"] = element;
          multiData.push(Options);
        });
        const multiDataCreate = await shipment_inventory_location.bulkCreate(
          multiData,
          {
            returning: true,
          }
        );
      }
    }
  }
  if (itemData.shipment_inventory_id > 0 && mediaFileList.length !== 0) {
    const multiData = [];
    const multiData2 = [];

    mediaFileList.forEach((element) => {
      let Options = {};
      Options["shipment_inventory_id"] = itemData.shipment_inventory_id;
      Options["media"] = element.media;
      Options["is_thumbnail"] = isThumbnailImageYes
        ? element.media == thumbnailImageName
          ? 1
          : 0
        : 0;
      multiData.push(Options);
    });
    const multiDataCreate = await shipment_inventory_photo.bulkCreate(
      multiData,
      {
        returning: true,
      }
    );

    mediaFileList.forEach((element) => {
      let Options = {};
      Options["shipment_inventory_id"] = itemData.shipment_inventory_id;
      Options["mediaUrl"] = element.media;
      multiData2.push(Options);
    });

    const multiDataCreate2 =
      await shipment_inventory_thumbnail_photo.bulkCreate(multiData2, {
        returning: true,
      });
    return multiDataCreate;
  }
  return itemData;
};

exports.updateJobDetail = async (request) => {
  if (
    request.inventory_id &&
    request.inventory_id !== "unknown" &&
    request.inventory_id > 0
  ) {
    let oldInventoryDetails = await shipment_inventory.findOne({
      where: {
        shipment_inventory_id: request.inventory_id,
      },
      raw: true,
    });
    let damageCount = await shipment_inventory_exception_note.count({
      where: {
        shipment_inventory_id: request.inventory_id,
      },
      raw: true,
    });

    let updateData = {
      total_items: sequelize.literal("total_items - " + 1),
      total_cartons: sequelize.literal(
        "total_cartons - " +
        (oldInventoryDetails.is_carton == "1" ||
          oldInventoryDetails.is_carton == 1
          ? 1
          : 0)
      ),
      firearms_total_quantity: sequelize.literal(
        "firearms_total_quantity - " +
        (oldInventoryDetails.is_firearm == "1" ||
          oldInventoryDetails.is_firearm == 1
          ? 1
          : 0)
      ),
      total_cartons_cp: sequelize.literal(
        "total_cartons_cp - " +
        ((oldInventoryDetails.is_carton == "1" ||
          oldInventoryDetails.is_carton == 1) &&
          !oldInventoryDetails.packed_by_owner == "1"
          ? 1
          : 0)
      ),
      total_cartons_pbo: sequelize.literal(
        "total_cartons_pbo - " +
        ((oldInventoryDetails.is_carton == "1" ||
          oldInventoryDetails.is_carton == 1) &&
          oldInventoryDetails.packed_by_owner == "1"
          ? 1
          : 0)
      ),
      total_volume: sequelize.literal(
        "total_volume - " + oldInventoryDetails.volume
      ),
      total_weight: sequelize.literal(
        "total_weight - " + oldInventoryDetails.weight
      ),
      total_high_value: sequelize.literal(
        "total_high_value - " +
        (oldInventoryDetails.is_high_value == "1" ||
          oldInventoryDetails.is_high_value == 1
          ? oldInventoryDetails.declared_value
          : 0)
      ),
      total_pads_used: sequelize.literal(
        "total_pads_used - " + oldInventoryDetails.pads_used
      ),
      total_pro_gear_items: sequelize.literal(
        "total_pro_gear_items - " +
        (oldInventoryDetails.is_pro_gear == "1" ||
          oldInventoryDetails.is_pro_gear == 1
          ? 1
          : 0)
      ),
      total_pro_gear_weight: sequelize.literal(
        "total_pro_gear_weight - " +
        (oldInventoryDetails.is_pro_gear == "1" ||
          oldInventoryDetails.is_pro_gear == 1
          ? oldInventoryDetails.pro_gear_weight
          : 0)
      ),
      damaged_items: sequelize.literal(
        "damaged_items - " + (damageCount > 0 ? 1 : 0)
      ),
    };
    const updateResult = await shipment_job.update(updateData, {
      where: { shipment_job_id: request.job_id },
    });
  }

  let exceptions_array = new Function("return " + request.exceptions)();
  exceptions_array ? exceptions_array : (exceptions_array = []);

  let updateData = {
    total_items: sequelize.literal("total_items + " + 1),
    damaged_items: sequelize.literal(
      "damaged_items + " + (exceptions_array.length > 0 ? 1 : 0)
    ),
    total_cartons: sequelize.literal(
      "total_cartons + " +
      (request.is_carton == "true" || request.is_carton == true ? 1 : 0)
    ),
    firearms_total_quantity: sequelize.literal(
      "firearms_total_quantity + " +
      (request.is_firearm == "true" || request.is_firearm == true ? 1 : 0)
    ),
    total_cartons_cp: sequelize.literal(
      "total_cartons_cp + " +
      ((request.is_carton == "true" || request.is_carton == true) &&
        request.packed_by !== "Packed by Owner"
        ? 1
        : 0)
    ),
    total_cartons_pbo: sequelize.literal(
      "total_cartons_pbo + " +
      ((request.is_carton == "true" || request.is_carton == true) &&
        request.packed_by === "Packed by Owner"
        ? 1
        : 0)
    ),
    total_volume: sequelize.literal(
      "total_volume + " +
      (request.volume !== "" &&
        request.volume !== null &&
        request.volume !== undefined
        ? request.volume
        : 0)
    ),
    total_weight: sequelize.literal(
      "total_weight + " +
      (request.weight !== "" &&
        request.weight !== null &&
        request.weight !== undefined
        ? request.weight
        : 0)
    ),
    total_high_value: sequelize.literal(
      "total_high_value + " +
      (request.is_high_value == "true" || request.is_high_value == true
        ? request.declared_value
          ? request.declared_value
          : 0
        : 0)
    ),
    total_pads_used: sequelize.literal(
      "total_pads_used + " +
      (request.pads_used !== "" &&
        request.pads_used !== null &&
        request.pads_used !== undefined
        ? request.pads_used
        : 0)
    ),
    total_pro_gear_items: sequelize.literal(
      "total_pro_gear_items + " +
      (request.is_pro_gear == "true" || request.is_pro_gear == true ? 1 : 0)
    ),
    total_pro_gear_weight: sequelize.literal(
      "total_pro_gear_weight + " +
      (request.is_pro_gear == "true" || request.is_pro_gear == true
        ? request.pro_gear_weight
        : 0)
    ),
    // damaged_items: sequelize.literal("damaged_items + " + exceptions_array.length > 0 ? 1 : 0),
  };

  const updateRes = await shipment_job.update(updateData, {
    where: { shipment_job_id: request.job_id },
    // transaction,
  });

  return updateRes;
};

exports.getJobCurrentJobStatus = async (request) => {
  return await shipment_job.findOne({
    where: {
      shipment_job_id: request.job_id,
    },
    attributes: [
      [
        sequelize.literal(
          "(select CASE WHEN add_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "add_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "assign_storage_units_to_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "unassign_storage_units_from_items",
      ],
      [
        sequelize.literal(
          "(select name FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_status",
      ],
      [
        sequelize.literal(
          "(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_stage",
      ],
      [
        sequelize.literal(
          "(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
        ),
        "total_stages",
      ],
    ],
  });
};

exports.inventoryDetailsForQrcode = async (qrCodeId, request) => {
  return await shipment_job.findOne({
    where: {
      shipment_job_id: request.job_id,
    },
    include: [
      {
        model: shipment_inventory,
        where: {
          qr_id: qrCodeId,
        },
        attributes: [
          ...COMMON_INVENTORY_ATTRIBUTES,
          [
            sequelize.literal(
              `(CASE WHEN is_carton = '1' THEN 'true' ELSE 'false' END)`
            ),
            "is_carton",
          ],
          [
            sequelize.literal(
              `(CASE WHEN room_id is not null THEN room_id ELSE '' END)`
            ),
            "room_id",
          ],
          [
            sequelize.literal(
              "(select type FROM `qr_codes` where qr_code_id = job_items.qr_id)"
            ),
            "type",
          ],
          [
            sequelize.literal(
              `(IF
								(
									(select job_id from shipment_inventory_job_scanned where shipment_inventory_job_scanned.local_current_stage_id = shipment_job.local_job_status and shipment_inventory_job_scanned.shipment_inventory_id = job_items.shipment_inventory_id and shipment_inventory_job_scanned.shipment_job_id = job_items.shipment_job_id) 
									is not null, 'yes', 'no'
									)
								)`
            ),
            "isScanned",
          ],
          [
            sequelize.literal(
              `(IF((select shipment_inventory_id from shipment_inventory_forced where shipment_inventory_forced.local_current_stage_id = shipment_job.local_job_status and shipment_inventory_forced.shipment_inventory_id = job_items.shipment_inventory_id) is not null, 'yes', 'no'))`
            ),
            "isOverride",
          ],
          [
            sequelize.literal(
              `(CASE WHEN packed_by_owner = '1' THEN 'Packed by Owner' ELSE 'Carrier Packed' END)`
            ),
            "packed_by",
          ],
          [
            sequelize.literal(
              `(CASE WHEN is_disassembled = '1' THEN 'true' ELSE 'false' END)`
            ),
            "is_disassembled",
          ],
          [
            sequelize.literal(
              `(CASE WHEN disassembled_by_owner = '1' THEN 'By Customer' ELSE 'By Company' END)`
            ),
            "disassembled_by",
          ],
          [
            sequelize.literal(
              `(CASE WHEN is_electronics = '1' THEN 'true' ELSE 'false' END)`
            ),
            "is_electronics",
          ],
          [
            sequelize.literal(
              `(CASE WHEN is_high_value = '1' THEN 'true' ELSE 'false' END)`
            ),
            "is_high_value",
          ],
          [
            sequelize.literal(
              `(CASE WHEN is_additional_scan = '1' THEN 'yes' ELSE 'no' END)`
            ),
            "is_additional_scan",
          ],
          [
            sequelize.literal(
              `(CASE WHEN is_remove_scan = '1' THEN 'yes' ELSE 'no' END)`
            ),
            "is_remove_scan",
          ],

          [
            sequelize.literal(
              `(CASE WHEN isManualLabel = '1' THEN 'true' ELSE 'false' END)`
            ),
            "isManualLabel",
          ],

          [
            sequelize.literal(
              `(CASE WHEN isManualLabel = '1' THEN 'true' ELSE 'false' END)`
            ),
            "isManualLabel",
          ],
          "label_no",
          [
            sequelize.literal(
              `(CASE WHEN isManualLabel = '1' THEN 'true' ELSE 'false' END)`
            ),
            "isManualLabel",
          ],
          "lot_no",

          [
            sequelize.literal(
              `(CASE WHEN isManualLabel = '1' THEN 'true' ELSE 'false' END)`
            ),
            "isManualLabel",
          ],
          "color",

          [
            sequelize.literal(
              `(CASE WHEN is_pro_gear = '1' THEN 'true' ELSE 'false' END)`
            ),
            "is_pro_gear",
          ],
          "progear_name",
          [
            sequelize.literal(
              `(CASE WHEN is_firearm = '1' THEN 'true' ELSE 'false' END)`
            ),
            "is_firearm",
          ],
          "firmarm_serial_number",

          [
            sequelize.literal(
              "(select random_number FROM `qr_codes` where qr_code_id = job_items.qr_id)"
            ),
            "qr_generate_code",
          ],
          [
            sequelize.literal(
              "(select LPAD(label_number,8,0) FROM `qr_codes` where qr_code_id = job_items.qr_id)"
            ),
            "label_number",
          ],
          [
            sequelize.literal(
              "coalesce((select name FROM `shipment_rooms` where shipment_room_id = room_id), '')"
            ),
            "room_name",
          ],
          [
            sequelize.fn(
              "CONCAT",
              sequelize.col("`job_items->disassembled_user`.first_name"),
              " ",
              sequelize.col("`job_items->disassembled_user`.last_name")
            ),
            "disassembled_by_username",
          ],
          [
            sequelize.fn(
              "CONCAT",
              sequelize.col("`job_items->carrier_packed`.first_name"),
              " ",
              sequelize.col("`job_items->carrier_packed`.last_name")
            ),
            "packed_by_username",
          ],
          [
            sequelize.fn(
              "CONCAT",
              sequelize.col("`job_items->prepared_staff`.first_name"),
              " ",
              sequelize.col("`job_items->prepared_staff`.last_name")
            ),
            "prepared_by_username",
          ],
        ],
        required: false,
        as: "job_items",
        include: [
          {
            model: staff,
            as: "disassembled_user",
            attributes: ["first_name", "last_name"],
          },
          {
            model: staff,
            as: "carrier_packed",
            attributes: ["first_name", "last_name"],
          },
          {
            model: staff,
            as: "prepared_staff",
            attributes: ["first_name", "last_name"],
          },
          {
            model: shipment_inventory_comments,
            attributes: ["id", "shipment_inventory_id", "comment"],
            as: "comments",
          },
          {
            model: shipment_inventory_exception_note,
            attributes: [
              ["shipment_inventory_exception_note_id", "note_id"],
              "notes",
            ],
            required: false,
            as: "exceptions",
            include: [
              {
                model: shipment_inventory_exception,
                required: false,
                attributes: [
                  "shipment_exception_id",
                  [
                    sequelize.literal(
                      "(select name FROM `shipment_exceptions` where shipment_exception_id = `job_items->exceptions->eid`.`shipment_exception_id`) "
                    ),
                    "exception_name",
                  ],
                ],
                as: "eid",
                include: [
                  {
                    model: shipment_exception,
                    attributes: [],
                    required: true,
                    as: "exception_list",
                  },
                ],
              },
              {
                model: shipment_inventory_location,
                required: false,
                attributes: [
                  "shipment_location_id",
                  [
                    sequelize.literal(
                      "(select name FROM `shipment_locations` where shipment_location_id = `job_items->exceptions->lid`.`shipment_location_id`) "
                    ),
                    "location_name",
                  ],
                ],
                as: "lid",
                include: [
                  {
                    model: shipment_location,
                    attributes: [],
                    required: true,
                    as: "location_list",
                  },
                ],
              },
            ],
          },

          {
            model: shipment_inventory_forced,
            as: "forced_inventory",
            attributes: [
              "shipment_inventory_id",
              "current_stage_id",
              "local_current_stage_id",
              "shipment_job_id",
              "reason",
              "created_at",
              "override_by_staff",
            ],
            include: [
              {
                model: staff,
                as: "staff",
                attributes: [
                  [
                    sequelize.fn(
                      "CONCAT",
                      sequelize.col(
                        "`job_items->forced_inventory->staff`.first_name"
                      ),
                      " ",
                      sequelize.col(
                        "`job_items->forced_inventory->staff`.last_name"
                      )
                    ),
                    "override_by_staff_name",
                  ],
                ],
              },
              {
                model: shipment_type_stage,
                as: "shipment_type_stage",
                attributes: ["name"],
              },
            ],
          },
          {
            model: shipment_inventory_job_scanned,
            as: "scanned_inventory",
            attributes: [
              "current_stage_id",
              "local_current_stage_id",
              "shipment_inventory_id",
              "shipment_job_id",
              "created_at",
              "scanned_by_staff",
            ],
            include: [
              {
                model: staff,
                as: "staff",
                attributes: [
                  [
                    sequelize.fn(
                      "CONCAT",
                      sequelize.col(
                        "`job_items->scanned_inventory->staff`.first_name"
                      ),
                      " ",
                      sequelize.col(
                        "`job_items->scanned_inventory->staff`.last_name"
                      )
                    ),
                    "scanned_by_staff_name",
                  ],
                ],
              },
              {
                model: shipment_type_stage,
                attributes: ["name"],
                as: "shipment_type_stage",
              },
            ],
          },
          {
            model: qr_code,
            attributes: [
              ...COMMON_QR_ATTRIBUTES,
              [
                sequelize.literal(`CONCAT('${Const_AWS_BASE}', qr_image)`),
                "qr_image",
              ],
              [
                sequelize.literal(
                  "(select LPAD(label_number,8,0) FROM `qr_codes` where `job_items->item_qr`.qr_code_id =qr_codes.qr_code_id)"
                ),
                "label_number",
              ],
            ],
            required: false,
            as: "item_qr",
          },
          {
            model: shipment_inventory_photo,
            attributes: [
              ["shipment_inventory_photo_id", "photo_id"],
              "description",
              "local_stage_id",
              "stage_id",
              "is_thumbnail",
              "media",
              [
                sequelize.literal(
                  `(CASE WHEN media IS NOT NULL AND local_stage_id is NULL AND stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', job_items.shipment_job_id, '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', job_items.shipment_inventory_id, '/original/', media) END)`
                ),
                "item_photo",
              ],
            ],
            required: false,
            as: "item_photos",
            include: [
              {
                model: shipment_type_stage,
                attributes: ["name", "order_of_stages"],
                as: "shipment_type_stage",
              },
            ],
          },
          {
            model: shipment_room,
            as: "room",
            attributes: ["name"],
          },
          {
            model: unit_list,
            attributes: [
              "unit_id",
              "name",
              "storage_unit_id",
              "unitCode",
              "currentLocation",
            ],
            required: false,
            as: "unit_list",
          },
        ],
      },
    ],
    raw: false,
    order: [["shipment_job_id", "DESC"]],
    subQuery: false,
  });
};

exports.getJobSummary = async (request) =>
  shipment_job.findOne({
    attributes: [
      ...COMMON_JOB_ATTRIBUTES,

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_disassembled = "1" and deletedAt IS NULL )'
        ),
        "total_disassembled_items",
      ],

      [
        sequelize.literal(
          "(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_items",
      ],

      [
        sequelize.literal(
          "(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_volume",
      ],
      [
        sequelize.literal(
          "(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
        ),
        "firearms_total_quantity",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
        ),
        "total_cartons",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
        ),
        "total_cartons_cp",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
        ),
        "total_cartons_pbo",
      ],

      [
        sequelize.literal(
          '(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
        ),
        "total_high_value",
      ],

      [
        sequelize.literal(
          "(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_pads_used",
      ],

      [
        sequelize.literal(
          '(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_items",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_electronics = "1" and deletedAt IS NULL)'
        ),
        "total_electronics_items",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
        ),
        "total_highValue_items",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_is_pro_gear_items",
      ],

      [
        sequelize.literal(
          "(select CONCAT(first_name,' ', last_name)  FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "customer_name",
      ],
      [
        sequelize.literal(
          "(select company_name FROM `companies` where company_id = shipment_job.company_id) "
        ),
        "company_name",
      ],
      [
        sequelize.literal(
          "(select account_id FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "account_id",
      ],
      [
        sequelize.literal("(IF(shipment_name IS NULL, '', shipment_name))"),
        "shipment_name",
      ],
      [
        sequelize.literal(
          "(select name FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_status",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_item = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_item",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_exceptions",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN show_no_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "show_no_exceptions",
      ],

      [
        sequelize.literal(
          "(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_stage",
      ],
      [
        sequelize.literal(
          "(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
        ),
        "total_stages",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN order_of_stages = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_first_stage",
      ],

      [
        sequelize.literal(
          "(select CASE WHEN scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN allow_default_manual_label = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "allow_default_manual_label",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN add_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "add_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN enable_partial_complete_stage = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "enable_partial_complete_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_into_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_into_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "assign_storage_units_to_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "unassign_storage_units_from_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_out_of_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_out_of_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN supervisor_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_supervisor_signature_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN customer_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_customer_signature_require",
      ],
      [
        sequelize.literal(
          "(select name  FROM `shipment_type_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id ) "
        ),
        "shipment_type_name",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_make_user_mandatory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id) "
        ),
        "is_make_user_mandatory",
      ],
      [
        sequelize.literal(
          "(select why_supervisor_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_supervisor_signature_require_note",
      ],

      [
        sequelize.literal(
          "(select why_customer_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_customer_signature_require_note",
      ],
      [
        sequelize.literal(
          `(if(
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL) = 
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "1")
					,"yes","no"))`
        ),
        "isAllItemScanned",
      ],
      [
        sequelize.literal(
          `(if(
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "0") = 
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NOT NULL AND isScannedFlag = "0")
					,"yes","no"))`
        ),
        "isAllItemsAssignToUnits",
      ],
      [
        sequelize.literal(
          `(if(
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "0") = 
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL AND isScannedFlag = "0")
					,"yes","no"))`
        ),
        "isAllItemsUnassignToUnits",
      ],
      [
        sequelize.literal(
          `(if(
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "0") = 
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND is_additional_scan = "1" AND isScannedFlag = "0")
					,"yes","no"))`
        ),
        "isAllAdditionalScanStageItemsScanned",
      ],
      [
        sequelize.literal(
          `(if(
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL) = 
					  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND is_remove_scan = "1")
					,"yes","no"))`
        ),
        "isAllRemoveScanStageItemsScanned",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`
        ),
        "is_job_complete",
      ],
    ],
    where: {
      shipment_job_id: request.job_id,
    },
    include: [
      {
        model: company,
        attributes: ["company_id", "company_name"],
        required: true,
        as: "job_company",
        include: [
          {
            model: staff,
            where: { roles: "ADMIN", is_deleted: 0, status: "active" },
            attributes: [
              "staff_id",
              ["roles", "role"],
              "first_name",
              "last_name",
            ],
            required: true,
            as: "staffs",
          },
        ],
      },
      {
        model: customer,
        attributes: [],
        required: true,
        as: "customer_job",
      },
      {
        model: shipment_type_stage_for_shipment,
        attributes: [],
        required: true,
        as: "local_shipment_job_status",
      },
      {
        model: shipment_job_assign_worker_list,
        required: false,
        attributes: [
          "staff_id",
          "role",
          [
            sequelize.literal(
              "(select first_name FROM `staffs` where staff_id = assign_worker.staff_id) "
            ),
            "first_name",
          ],
          [
            sequelize.literal(
              "(select last_name FROM `staffs` where staff_id = assign_worker.staff_id) "
            ),
            "last_name",
          ],
        ],
        as: "assign_worker",
        include: [
          {
            model: staff,
            attributes: [],
            required: true,
            as: "assign_worker_detail",
          },
        ],
      },
      {
        model: shipment_inventory,
        where: {
          shipment_inventory_id: null,
        },
        required: false,
        as: "job_items",
      },
    ],
    raw: false,
    order: [["shipment_job_id", "DESC"]],
    subQuery: false,
  });

exports.addSignature = async (request, file, staff_id) => {
  let signatureObj = {
    shipment_job_id: request.job_id && request.job_id,
    ...file,
    local_stage: request.stage && request.stage,
    staff_id: staff_id,
    supervisor_signature_require_note_by_user:
      request.supervisor_signature_require_note_by_user &&
      request.supervisor_signature_require_note_by_user,
    customer_signature_require_note_by_user:
      request.customer_signature_require_note_by_user &&
      request.customer_signature_require_note_by_user,
    supervisor_name: request.supervisor_name,
    customer_name: request.customer_name,
  };
  let signatureDetails = await shipment_job_signature.create(signatureObj);
  return signatureDetails;
};

exports.addSignatureWithoutImage = async (request, staff_id) => {
  let signatureObj = {
    shipment_job_id: request.job_id && request.job_id,
    local_stage: request.stage && request.stage,
    staff_id: staff_id,
    supervisor_signature_require_note_by_user:
      request.supervisor_signature_require_note_by_user &&
      request.supervisor_signature_require_note_by_user,
    customer_signature_require_note_by_user:
      request.customer_signature_require_note_by_user &&
      request.customer_signature_require_note_by_user,
    supervisor_name: request.supervisor_name,
    customer_name: request.customer_name,
  };
  let signatureDetails = await shipment_job_signature.create(signatureObj);
  return signatureDetails;
};

exports.getItemImagesList = async (item_id) => {
  return await shipment_inventory_photo.findAll({
    attributes: ["media"],
    where: {
      shipment_inventory_id: item_id,
    },
    raw: true,
  });
};

exports.getJobId = async (item_id) => {
  return await shipment_inventory.findOne({
    attributes: [
      "shipment_job_id",
      "storage_unit_id",
      "unit_id",
      "inventory_stage_id",
    ],
    where: {
      shipment_inventory_id: item_id,
    },
    raw: true,
  });
};

exports.allUnitItemsWithJob = async (jobId) => {
  return await shipment_inventory.findAndCountAll({
    where: {
      shipment_job_id: jobId.shipment_job_id,
      storage_unit_id: jobId.storage_unit_id,
      deletedAt: null,
    },
  });
};

exports.removeItemImage = async (image) => {
  return await shipment_inventory_photo.destroy({
    where: { media: image },
  });
};

exports.removeItem = async (itemId, stage_id) => {
  try {
    return await sequelize.transaction(async (transaction) => {
      await shipment_type_stage_for_shipment.decrement(
        { total_add_items_inventory_stage: 1 },
        { where: { local_shipment_stage_id: stage_id }, transaction }
      );

      return shipment_inventory.destroy({
        where: { shipment_inventory_id: itemId },
        transaction,
      });
    });
  } catch (error) {
    throw new Error("Failed to remove item");
  }
};

exports.removeItemScanHistoryFind = async (itemId) => {
  return await shipment_inventory_job_scanned.findOne({
    where: { shipment_inventory_id: itemId },
  });
};

exports.removeItemScanHistoryDelete = async (itemId) => {
  return await shipment_inventory_job_scanned.destroy({
    where: { shipment_inventory_id: itemId },
  });
};

exports.removeItemForcedHistoryFind = async (itemId) => {
  return await shipment_inventory_forced.findOne({
    where: { shipment_inventory_id: itemId },
  });
};

exports.removeItemForcedHistoryDelete = async (itemId) => {
  return await shipment_inventory_forced.destroy({
    where: { shipment_inventory_id: itemId },
  });
};

exports.removeItemHistory = async (itemId) =>
  await shipment_inventory_unit_history.destroy({
    where: { shipment_inventory_id: itemId },
  });

exports.editItem = async (
  request,
  mediaFileList,
  isThumbnailImageYes,
  thumbnailImageName
) => {
  let updateData = {
    shipment_job_id: request.job_id,
    storage_unit_id: request.storage_unit_id,
    unit_id: Number(request.unit_id),
    room_id: request.room_id,
    qr_id: request.qr_id && request.qr_id,
    item_name: request.item_name,
    description: request.desc,
    is_carton: request.is_carton == "true" || request.is_carton == true ? 1 : 0,

    packed_by_owner:
      request.packed_by &&
        (request.is_carton == "true" || request.is_carton == true) &&
        request.packed_by.includes("Owner")
        ? 1
        : 0,
    carrier_packed_user_id:
      Number(request.packed_user_id) &&
        (request.is_carton == "true" || request.is_carton == true) &&
        request.packed_user_id !== ""
        ? Number(request.packed_user_id)
        : null,

    carrier_packed_user_name_manually:
      (request.is_carton == "true" || request.is_carton == true) &&
        (request.packed_user_id == null ||
          request.packed_user_id == "" ||
          request.packed_user_id == undefined) &&
        request.packed_by !== "Packed by Owner"
        ? request.carrier_packed_user_name_manually == null ||
          request.carrier_packed_user_name_manually == "" ||
          request.carrier_packed_user_name_manually == undefined
          ? null
          : request.carrier_packed_user_name_manually
        : null,

    is_disassembled:
      request.is_disassembled == "true" || request.is_disassembled == true
        ? 1
        : 0,
    disassembled_by_owner:
      request.disassembled_by &&
        (request.is_disassembled == "true" || request.is_disassembled == true) &&
        request.disassembled_by.includes("Customer")
        ? 1
        : 0,
    disassembled_user_id:
      Number(request.disassembled_user_id) &&
        (request.is_disassembled == "true" || request.is_disassembled == true) &&
        request.disassembled_user_id !== ""
        ? Number(request.disassembled_user_id)
        : null,

    disassembled_user_name_manually:
      (request.is_disassembled == "true" || request.is_disassembled == true) &&
        (request.disassembled_user_id == null ||
          request.disassembled_user_id == "" ||
          request.disassembled_user_id == undefined) &&
        request.disassembled_by !== "Customer"
        ? request.disassembled_user_name_manually == null ||
          request.disassembled_user_name_manually == "" ||
          request.disassembled_user_name_manually == undefined
          ? null
          : request.disassembled_user_name_manually
        : null,

    is_electronics:
      request.is_electronics == "true" || request.is_electronics == true
        ? 1
        : 0,
    serial_number:
      request.serial_number &&
        (request.is_electronics == "true" || request.is_electronics == true)
        ? request.serial_number
        : "",
    is_high_value:
      request.is_high_value == "true" || request.is_high_value == true ? 1 : 0,
    seal_number:
      request.seal_number &&
        (request.is_high_value == "true" || request.is_high_value == true)
        ? request.seal_number
        : "",
    declared_value:
      request.declared_value &&
        (request.is_high_value == "true" || request.is_high_value == true) &&
        request.declared_value !== ""
        ? request.declared_value
        : 0,
    is_pro_gear:
      request.is_pro_gear == "true" || request.is_pro_gear == true ? 1 : 0,
    pro_gear_weight:
      request.pro_gear_weight &&
        (request.is_pro_gear == "true" || request.is_pro_gear == true) &&
        request.pro_gear_weight !== ""
        ? request.pro_gear_weight
        : 0,
    volume:
      request.volume !== "" && request.volume !== null ? request.volume : 0,
    weight:
      request.weight !== "" && request.weight !== null ? request.weight : 0,
    pads_used:
      request.pads_used !== "" && request.pads_used !== null
        ? request.pads_used
        : 0,
    prepared_by:
      request.prepared_by &&
        request.prepared_by !== "" &&
        request.prepared_by > 0
        ? request.prepared_by
        : null,

    prepared_user_name_manually: request.prepared_user_name_manually,

    notes: request.notes,
    progear_name:
      request.progear_name &&
        (request.is_pro_gear == "true" || request.is_pro_gear == true)
        ? request.progear_name
        : "",
    is_firearm:
      request.is_firearm == "true" || request.is_firearm == true ? 1 : 0,
    firmarm_serial_number:
      request.firmarm_serial_number &&
        (request.is_firearm == "true" || request.is_firearm == true)
        ? request.firmarm_serial_number
        : "",

    isManualLabel:
      request.isManualLabel == "true" || request.isManualLabel == true ? 1 : 0,

    label_no:
      request.label_no &&
        (request.isManualLabel == "true" || request.isManualLabel == true)
        ? request.label_no
        : 0,

    lot_no:
      request.lot_no &&
        (request.isManualLabel == "true" || request.isManualLabel == true)
        ? request.lot_no
        : "",

    color:
      request.color &&
        (request.isManualLabel == "true" || request.isManualLabel == true)
        ? request.color
        : "",
    is_add_dimension:
      request.is_add_dimension == "true" || request.is_add_dimension == true
        ? 1
        : 0,

    length:
      request.length &&
        (request.is_add_dimension == "true" || request.is_add_dimension == true)
        ? request.length
        : 0,

    width:
      request.width &&
        (request.is_add_dimension == "true" || request.is_add_dimension == true)
        ? request.width
        : 0,

    height:
      request.height &&
        (request.is_add_dimension == "true" || request.is_add_dimension == true)
        ? request.height
        : 0,
  };

  // decrease total count in job
  await this.updateJobDetail(request);

  const itemData = await shipment_inventory.update(updateData, {
    where: { shipment_inventory_id: request.inventory_id },
  });

  const checkIsCmsEdit =
    request &&
      request.itemEditByCms &&
      (request.itemEditByCms == true || request.itemEditByCms == "true")
      ? true
      : false;
  if (checkIsCmsEdit) {
    console.log("Check:itemEditByCmsTrue");
  } else {
    // remove old notes, exception, location
    await shipment_inventory_exception_note.destroy({
      where: { shipment_inventory_id: request.inventory_id },
    });

    await shipment_inventory_exception.destroy({
      where: { shipment_inventory_id: request.inventory_id },
    });

    await shipment_inventory_location.destroy({
      where: { shipment_inventory_id: request.inventory_id },
    });
  }

  // convert string to array
  let exceptions_array = new Function("return " + request.exceptions)();
  exceptions_array ? exceptions_array : (exceptions_array = []);
  if (request.inventory_id > 0 && exceptions_array.length > 0) {
    for (const element of exceptions_array) {
      let insertNoteData = {
        shipment_inventory_id: request.inventory_id,
        notes: element.notes,
      };
      const noteData = await shipment_inventory_exception_note.create(
        insertNoteData
      );
      if (
        noteData.shipment_inventory_exception_note_id > 0 &&
        element.eid !== ""
      ) {
        let exception_array = element.eid.split(",");

        const multiData = [];

        exception_array.forEach((element) => {
          let Options = {};
          Options["shipment_inventory_id"] = request.inventory_id;
          Options["exception_note_id"] =
            noteData.shipment_inventory_exception_note_id;
          Options["shipment_exception_id"] = element;
          multiData.push(Options);
        });
        const multiDataCreate = await shipment_inventory_exception.bulkCreate(
          multiData,
          // { transaction },
          {
            returning: true,
          }
        );
      }

      if (
        noteData.shipment_inventory_exception_note_id > 0 &&
        element.lid !== ""
      ) {
        let location_array = element.lid.split(",");

        const multiData = [];

        location_array.forEach((element) => {
          let Options = {};
          Options["shipment_inventory_id"] = request.inventory_id;
          Options["exception_note_id"] =
            noteData.shipment_inventory_exception_note_id;
          Options["shipment_location_id"] = element;
          multiData.push(Options);
        });
        const multiDataCreate = await shipment_inventory_location.bulkCreate(
          multiData,
          // { transaction },
          {
            returning: true,
          }
        );
      }
    }
  }

  if (
    request.item_thumbnail_photo !== undefined &&
    request.item_thumbnail_photo !== "" &&
    request.item_thumbnail_photo !== null
  ) {
    await this.removeItemOldThumbnail(request.inventory_id);
    if (!isThumbnailImageYes) {
      await this.updateItemThumbnail(request.item_thumbnail_photo);
    }
  }

  if (request.inventory_id > 0 && mediaFileList.length !== 0) {
    const multiData = [];

    mediaFileList.forEach((element) => {
      let Options = {};
      Options["shipment_inventory_id"] = request.inventory_id;
      Options["media"] = element.media;
      Options["is_thumbnail"] = isThumbnailImageYes
        ? element.media == thumbnailImageName
          ? 1
          : 0
        : 0;
      multiData.push(Options);
    });
    const multiDataCreate = await shipment_inventory_photo.bulkCreate(
      multiData,
      // { transaction },
      {
        returning: true,
      }
    );
    return multiDataCreate;
  }
  return itemData;
};

exports.removeItemFromInventoryScanned = async (inventoryId, stageId) => {
  let updateData = {
    remove_from_inventory_stage_id: stageId,
  };
  const itemData = await shipment_inventory.update(updateData, {
    where: { shipment_inventory_id: inventoryId },
  });
  return itemData;
};

exports.editItemUnit = async (request) => {
  let updateData = {
    shipment_job_id: request.job_id,
    storage_unit_id: request.storage_unit_id,
    unit_id: request.unit_id,
    assign_to_storage_stage_id: request.stageId,
  };
  const itemData = await shipment_inventory.update(updateData, {
    where: { shipment_inventory_id: request.inventory_id },
  });

  return itemData;
};

exports.editBulkItemUnit = async (request, item) => {
  let updateData = {
    shipment_job_id: request.job_id,
    storage_unit_id: request.storage_unit_id,
    unit_id: request.unit_id,
    assign_to_storage_stage_id: request.stageId,
    is_item_scanned_remove_from_storage: 0,
  };
  const itemData = await shipment_inventory.update(updateData, {
    where: { shipment_inventory_id: item.shipment_inventory_id },
  });
  return itemData;
};

exports.updateSingleTotalAddItemsToStorage = async (request) => {
  const updateResult = await shipment_type_stage_for_shipment.increment(
    {
      total_add_items_storage_stage: 1,
    },
    {
      where: { local_shipment_stage_id: request.stageId },
    }
  );
};

exports.updateSingleTotalRemoveItemsToStorage = async (request) => {
  const updateResult = await shipment_type_stage_for_shipment.increment(
    {
      total_remove_items_storage_stage: 1,
    },
    {
      where: { local_shipment_stage_id: request.current_job_stage },
    }
  );
};

exports.updateSingleTotalRemoveItemsToInventory = async (request) => {
  const updateResult = await shipment_type_stage_for_shipment.increment(
    {
      total_remove_items_inventory_stage: 1,
    },
    {
      where: { local_shipment_stage_id: request.current_job_stage },
    }
  );
};

exports.updateBulkItems = async (request, itemData, isAddItems) => {
  const itemArray = itemData.map(
    (item) => item.dataValues.shipment_inventory_id
  );
  const updateField = isAddItems ? "is_additional_scan" : "is_remove_scan";
  const incrementField = isAddItems
    ? "total_add_items_to_inventory_scan"
    : "total_remove_items_to_inventory_scan";

  await shipment_inventory.update(
    {
      [updateField]: 1,
    },
    {
      where: { shipment_inventory_id: { [Op.in]: itemArray } },
    }
  );

  await shipment_type_stage_for_shipment.increment(
    {
      [incrementField]: itemArray.length,
    },
    {
      where: { local_shipment_stage_id: request.current_job_stage },
    }
  );
};

exports.addInventoryCommentModel = async (data) =>
  await shipment_inventory_comments.create({
    shipment_inventory_id: data.inventoryId,
    comment: data.comment,
  });

exports.getInventoryCommentDetailModel = async (inventoryId) => {
  return await shipment_inventory_comments.findAll({
    attributes: ["comment", "shipment_inventory_id"],
    where: {
      shipment_inventory_id: inventoryId,
    },
  });
};

exports.getInventoryDetailModel = async (inventoryId) =>
  shipment_inventory.findOne({
    attributes: [
      ...COMMON_INVENTORY_ATTRIBUTES,
      [
        sequelize.literal(
          `(IF((select job_id from shipment_inventory_job_scanned where shipment_inventory_job_scanned.local_current_stage_id = shipment_job.local_job_status and shipment_inventory_job_scanned.shipment_inventory_id = shipment_inventory.shipment_inventory_id and shipment_inventory_job_scanned.shipment_job_id = shipment_inventory.shipment_job_id) is not null, 'yes', 'no'))`
        ),
        "isScanned",
      ],
      [
        sequelize.literal(
          `(IF((select shipment_inventory_id from shipment_inventory_forced where shipment_inventory_forced.local_current_stage_id = shipment_job.local_job_status and shipment_inventory_forced.shipment_inventory_id = shipment_inventory.shipment_inventory_id) is not null, 'yes', 'no'))`
        ),
        "isOverride",
      ],
      [sequelize.literal(`(IF(is_carton = '1', true, false))`), "is_carton"],
      [
        sequelize.literal(
          `(IF(packed_by_owner = '1', 'Packed by Owner', 'Carrier Packed'))`
        ),
        "packed_by",
      ],
      [
        sequelize.literal(`(IF(is_disassembled = '1', true, false))`),
        "is_disassembled",
      ],
      [
        sequelize.literal(
          `(IF(disassembled_by_owner = '1', 'By Customer', 'By Company'))`
        ),
        "disassembled_by",
      ],
      [
        sequelize.literal(`(IF(is_electronics = '1', true, false))`),
        "is_electronics",
      ],
      [
        sequelize.literal(`(IF(is_high_value = '1', true, false))`),
        "is_high_value",
      ],
      [
        sequelize.literal(`(IF(is_pro_gear = '1', true, false))`),
        "is_pro_gear",
      ],
      [
        sequelize.literal(`(IF(isManualLabel = '1', true, false))`),
        "isManualLabel",
      ],
      [sequelize.literal(`(IF(is_firearm = '1', true, false))`), "is_firearm"],
    ],
    where: {
      shipment_inventory_id: inventoryId,
    },
    include: [
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
        ],
        required: false,
        as: "unit_list",
      },
      {
        model: shipment_inventory_comments,
        attributes: ["id", "shipment_inventory_id", "comment"],
        as: "comments",
      },
      {
        model: shipment_inventory_exception_note,
        attributes: [
          ["shipment_inventory_exception_note_id", "note_id"],
          "notes",
        ],
        required: false,
        as: "exceptions",
        include: [
          {
            model: shipment_inventory_exception,
            required: false,
            attributes: [
              "shipment_exception_id",
              [
                sequelize.literal(
                  "(select name FROM `shipment_exceptions` where shipment_exception_id = `exceptions->eid`.`shipment_exception_id`) "
                ),
                "exception_name",
              ],
            ],
            as: "eid",
            include: [
              {
                model: shipment_exception,
                attributes: [],
                required: true,
                as: "exception_list",
              },
            ],
          },
          {
            model: shipment_inventory_location,
            required: false,
            attributes: [
              "shipment_location_id",
              [
                sequelize.literal(
                  "(select name FROM `shipment_locations` where shipment_location_id = `exceptions->lid`.`shipment_location_id`) "
                ),
                "location_name",
              ],
            ],
            as: "lid",
            include: [
              {
                model: shipment_location,
                attributes: [],
                required: true,
                as: "location_list",
              },
            ],
          },
        ],
      },

      {
        model: shipment_inventory_forced,
        attributes: [
          "shipment_inventory_id",
          "current_stage_id",
          "local_current_stage_id",
          "shipment_job_id",
          "updated_at",
          "reason",
          "override_by_staff",
        ],
        as: "forced_inventory",
        include: [
          {
            model: staff,
            as: "staff",
            attributes: ["first_name", "last_name"],
          },
          {
            model: shipment_type_stage_for_shipment,
            attributes: ["name"],
            as: "shipment_type_stage_for_shipment",
          },
        ],
      },
      {
        model: shipment_inventory_job_scanned,
        attributes: [
          "current_stage_id",
          "local_current_stage_id",
          "shipment_inventory_id",
          "shipment_job_id",
          "created_at",
          "scanned_by_staff",
        ],
        as: "scanned_inventory",
        include: [
          {
            model: staff,
            as: "staff",
            attributes: ["first_name", "last_name"],
          },
          {
            model: shipment_type_stage_for_shipment,
            attributes: ["name"],
            as: "shipment_type_stage_for_shipment",
          },
        ],
      },
      {
        model: qr_code,
        attributes: [
          ...COMMON_QR_ATTRIBUTES,
          [
            sequelize.literal(`CONCAT('${Const_AWS_BASE}', qr_image)`),
            "qr_image",
          ],
          [
            sequelize.literal(
              "(select LPAD(label_number,8,0) FROM `qr_codes` where item_qr.qr_code_id =qr_codes.qr_code_id)"
            ),
            "label_number",
          ],
        ],
        required: false,
        as: "item_qr",
      },
      {
        model: shipment_room,
        as: "room",
        attributes: ["name"],
      },
      {
        model: staff,
        as: "prepared_staff",
        attributes: ["first_name", "last_name"],
      },
      {
        model: staff,
        as: "carrier_packed",
        attributes: ["first_name", "last_name"],
      },
      {
        model: staff,
        as: "disassembled_user",
        attributes: ["first_name", "last_name"],
      },
      {
        model: shipment_inventory_photo,
        attributes: [
          ["shipment_inventory_photo_id", "photo_id"],
          "description",
          "stage_id",
          "local_stage_id",
          "is_thumbnail",
          "media",
          [
            sequelize.literal(
              `(CASE WHEN media IS NOT NULL AND local_stage_id is NULL AND stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', shipment_inventory.shipment_job_id, '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', media) END)`
            ),
            "item_photo",
          ],
        ],
        required: false,
        as: "item_photos",
        include: [
          {
            model: shipment_type_stage_for_shipment,
            attributes: ["name", "order_of_stages"],
            as: "shipment_type_stage_for_shipment",
          },
        ],
      },

      {
        model: shipment_job,
        as: "shipment_job",
        attributes: [
          "shipment_job_id",
          "storage_shipment_job_id",
          "company_id",
          [
            sequelize.literal(
              "(select CASE WHEN scan_into_storage = '1' AND storage_shipment_job_id IS NOT NULL AND `shipment_job`.`warehouseId` IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "scan_into_storage",
          ],
          [
            sequelize.literal(
              "(select CASE WHEN assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND `shipment_job`.`warehouseId` IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "assign_storage_units_to_items",
          ],

          [
            sequelize.literal(
              "(select CASE WHEN scan_out_of_storage = '1' AND storage_shipment_job_id IS NOT NULL AND `shipment_job`.`warehouseId` IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "scan_out_of_storage",
          ],

          [
            sequelize.literal(
              "(select CASE WHEN unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND `shipment_job`.`warehouseId` IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
            ),
            "unassign_storage_units_from_items",
          ],
        ],
      },
      {
        model: tag_item,
        as: "item_tag",
        required: false,
        attributes: [
          COMMON_ITEM_TAG_ATTRIBUTES[0],
          [sequelize.literal("`item_tag->m2m_item_tag`.tag_id"), "tag_id"],
          [sequelize.literal("`item_tag->m2m_item_tag`.name"), "name"],
          [sequelize.literal("`item_tag->m2m_item_tag`.color"), "color"],
          [
            sequelize.literal("`item_tag->m2m_item_tag`.company_id"),
            "company_id",
          ],
        ],
        include: {
          // required: false,
          model: tag,
          as: "m2m_item_tag",
          attributes: [],
        },
      },
    ],
    raw: false,
    order: [["shipment_job_id", "DESC"]],
    subQuery: false,
  });

exports.isValidInventoryModel = async (shipment_inventory_id) => {
  const isType = await shipment_inventory.findOne({
    where: { shipment_inventory_id: shipment_inventory_id },
    attributes: ["shipment_inventory_id"],
  });

  if (isType !== null) {
    return true;
  } else {
    return false;
  }
};

exports.isScannedInventoryModel = async (
  job_id,
  shipment_inventory_id,
  stage_id
) => {
  const isScanned = await shipment_inventory_job_scanned.findOne({
    where: {
      shipment_inventory_id: shipment_inventory_id,
      shipment_job_id: job_id,
    },
    attributes: ["shipment_inventory_id"],
  });
  if (isScanned === null) {
    return true;
  } else {
    return false;
  }
};

exports.isInventoryOnJobModel = async (job_id, shipment_inventory_id) => {
  const isScanned = await shipment_inventory.findOne({
    where: {
      shipment_inventory_id: shipment_inventory_id,
      shipment_job_id: job_id,
    },
    attributes: ["shipment_inventory_id"],
  });
  if (isScanned !== null) {
    return true;
  } else {
    return false;
  }
};

exports.isPreOverriddenInventoryModel = async (
  job_id,
  shipment_inventory_id,
  stage_id
) => {
  const isOverridden = await shipment_inventory_forced.findOne({
    where: {
      shipment_inventory_id: shipment_inventory_id,
      shipment_job_id: job_id,
    },
    attributes: ["shipment_inventory_id"],
  });

  if (isOverridden === null) {
    return true;
  } else {
    return false;
  }
};

const getInventoryListByJobId = async (job_id) => {
  const data = await shipment_inventory.findAndCountAll({
    attributes: ["shipment_job_id"],
    where: {
      shipment_job_id: job_id,
      deletedAt: null,
    },
  });
  return data.count;
};

// const getInventoryListByJobId = async (job_id) => {
// 	const data = await shipment_inventory.findAndCountAll({
// 		attributes: ["shipment_job_id"],
// 		include: {
// 			model: shipment_inventory_forced,
// 			as: "forced_inventory",
// 			attributes: ["shipment_job_id"],
// 			where: { shipment_job_id: job_id },
// 			required: false,
// 		},
// 		where: {
// 			shipment_job_id: job_id,
// 			"$forced_inventory.forced_status_id$": { [Op.eq]: null },
// 		},
// 	});
// 	return data.count
// }

exports.isAllInventoryJobItemScannedModel = async (job_id, stage_id) => {
  let countData = await shipment_inventory_job_scanned.findAndCountAll({
    where: { shipment_job_id: job_id },
  });

  let countData2 = await shipment_inventory_forced.findAndCountAll({
    where: { shipment_job_id: job_id },
  });
  if (countData) {
    let globalCount = await getInventoryListByJobId(job_id);
    let totalCount = countData.count + countData2.count;
    let data = globalCount - totalCount;

    if (globalCount) {
      return {
        isAllItemScanned: totalCount === globalCount ? "yes" : "no",
        leftToScan: data,
      };
    } else {
      return {
        isAllItemScanned: totalCount === globalCount ? "yes" : "no",
        leftToScan: data,
      };
    }
  }
};

exports.isAllBulkAdditionalItemsScanModel = async (
  job_id,
  isAddItemsToInventoryScan
) => {
  const scanType =
    isAddItemsToInventoryScan == "true" || isAddItemsToInventoryScan == true
      ? "is_additional_scan"
      : "is_remove_scan";

  let countData = await shipment_inventory.findAndCountAll({
    where: { shipment_job_id: job_id, [scanType]: 1 },
  });

  if (countData) {
    let globalCount = await getInventoryListByJobId(job_id);
    let totalCount = countData.count;
    let data = globalCount - totalCount;
    return {
      isAllItemScanned: totalCount === globalCount ? "yes" : "no",
      leftToScan: data,
    };
  }
};

exports.isAlreadyScanCheckModel = async (
  inventory_id,
  stage_id,
  shipment_id
) => {
  const data = await shipment_inventory_job_scanned.findOne({
    where: {
      local_current_stage_id: stage_id,
      shipment_inventory_id: inventory_id,
      shipment_job_id: shipment_id,
    },
  });
  if (data !== null) {
    return false;
  } else {
    return true;
  }
};

exports.scanInventoryJobModel = async (
  jobId,
  inventory_id,
  stage_id,
  staff_id
) => {
  return await shipment_inventory_job_scanned.create({
    current_stage_id: null,
    local_current_stage_id: stage_id,
    shipment_job_id: jobId,
    shipment_inventory_id: inventory_id,
    scanned_by_staff: staff_id,
  });
};

exports.overrideItemModel = async (
  jobId,
  inventory_id,
  stage_id,
  reason,
  staff_id
) =>
  await shipment_inventory_forced.create({
    local_current_stage_id: stage_id,
    current_stage_id: null,
    reason: reason,
    shipment_job_id: jobId,
    shipment_inventory_id: inventory_id,
    override_by_staff: staff_id,
  });

exports.addShipmentInventoryMedia = async (request, newFileList) => {
  let shipmentPhoto = await shipment_inventory_photo.findOne({
    where: { stage_id: request.current_job_stage },
    attributes: ["stage_id"],
  });
  const multiData = [];
  newFileList.length
    ? newFileList.forEach((element) => {
      let Options = {};
      Options["shipment_inventory_id"] = request.inventory_id;
      Options["media"] = element.media;
      Options["local_stage_id"] = request.current_job_stage;
      Options["stage_id"] = null;
      Options["description"] = request.desc || null;

      multiData.push(Options);
    })
    : multiData.push({
      shipment_inventory_id: request.inventory_id,
      media: null,
      local_stage_id: request.current_job_stage || null,
      stage_id: null,
      description: request.desc || null,
    });
  const multiDataCreate = await shipment_inventory_photo.bulkCreate(multiData, {
    returning: true,
  });
  return multiDataCreate;
};

exports.isValidShipmentPhotoController = async (stage_id) => {
  const isScanned = await shipment_inventory_photo.findOne({
    where: { stage_id: stage_id },
    attributes: ["stage_id"],
  });

  if (isScanned !== null) {
    return true;
  } else {
    return false;
  }
};

exports.viewInventoryPhoto = async (customer_id) => {
  return await shipment_inventory_photo.findOne({
    attributes: [
      [
        sequelize.literal(`(CASE WHEN media IS NULL THEN '' ELSE media END)`),
        "media",
      ],
      [
        sequelize.literal(
          `(CASE WHEN media IS NULL THEN '' WHEN media NOT LIKE 'http%' THEN CONCAT('${Const_AWS_BASE_Shipment_inventory}', 'original/', media) ELSE media END)`
        ),
        "customer_profile",
      ],
    ],
    where: { shipment_inventory_id: customer_id },
  });
};

exports.allItemsListByUnitIdModel = async (fieldsAndValues, unitArray) => {
  return shipment_inventory.findAll({
    where: {
      storage_unit_id: unitArray,
      [Op.or]: [
        { item_name: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
        { volume: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
        { weight: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
      ],
      deletedAt: null,
    },
    attributes: [
      ...COMMON_INVENTORY_ATTRIBUTES,
      [
        sequelize.literal(
          "(IF((select job_id from shipment_inventory_job_scanned where shipment_inventory_job_scanned.local_current_stage_id = (select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id) and shipment_inventory_job_scanned.shipment_inventory_id = shipment_inventory.shipment_inventory_id and shipment_inventory_job_scanned.shipment_job_id = shipment_inventory.shipment_job_id) is not null, 'yes', 'no'))"
        ),
        "isScanned",
      ],
      [
        sequelize.literal(
          "(select random_number FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "qr_generate_code",
      ],
      [
        sequelize.literal(
          "(select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id)"
        ),
        "status",
      ],
      [
        sequelize.literal(
          "(select type FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "type",
      ],

      [
        sequelize.literal(
          "(select LPAD(label_number,8,0) FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "label_number",
      ],
      [
        sequelize.literal(
          "coalesce((select name FROM `shipment_rooms` where shipment_room_id = room_id), '')"
        ),
        "room_name",
      ],

      [
        sequelize.literal(
          "(IF((select shipment_inventory_id from shipment_inventory_forced where shipment_inventory_forced.local_current_stage_id = (select local_job_status FROM `shipment_jobs` where shipment_job_id = shipment_inventory.shipment_job_id) and shipment_inventory_forced.shipment_inventory_id = shipment_inventory.shipment_inventory_id) is not null, 'yes', 'no'))"
        ),
        "isOverride",
      ],
      [
        sequelize.literal(
          `(IF(packed_by_owner = '1', 'Packed by Owner', 'Carrier Packed'))`
        ),
        "packed_by",
      ],
      [
        sequelize.literal(
          `(IF(disassembled_by_owner = '1', 'By Customer', 'By Company'))`
        ),
        "disassembled_by",
      ],
      [
        sequelize.literal(`(CASE WHEN is_electronics = '1' THEN 1 ELSE 0 END)`),
        "is_electronics",
      ],
      [
        sequelize.literal(`(CASE WHEN is_firearm = '1' THEN 1 ELSE 0 END)`),
        "is_firearm",
      ],

      [
        sequelize.literal(`(CASE WHEN is_high_value = '1' THEN 1 ELSE 0 END)`),
        "is_high_value",
      ],

      [
        sequelize.literal(`(CASE WHEN is_pro_gear = '1' THEN 1 ELSE 0 END)`),
        "is_pro_gear",
      ],

      [
        sequelize.literal(`(CASE WHEN isManualLabel = '1' THEN 1 ELSE 0 END)`),
        "isManualLabel",
      ],

      [
        sequelize.literal(`(CASE WHEN is_carton = '1' THEN 1 ELSE 0 END)`),
        "is_carton",
      ],

      [
        sequelize.literal(
          `(CASE WHEN is_disassembled = '1' THEN 1 ELSE 0 END)`
        ),
        "is_disassembled",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.prepared_by)"
        ),
        "prepared_by_username",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.disassembled_user_id)"
        ),
        "disassembled_by_username",
      ],

      [
        sequelize.literal(
          "(select first_name FROM `staffs` where  staff_id = shipment_inventory.carrier_packed_user_id)"
        ),
        "packed_by_username",
      ],
    ],

    include: [
      {
        model: shipment_inventory_unit_history,
        attributes: ["shipment_inventory_id"],
        as: "inventory_history",
      },
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
        ],
        as: "unit_list",
      },
      {
        model: shipment_inventory_comments,
        attributes: ["id", "shipment_inventory_id", "comment"],
        as: "comments",
      },
      {
        model: shipment_inventory_exception_note,
        attributes: [
          ["shipment_inventory_exception_note_id", "note_id"],
          "notes",
        ],
        required: false,
        as: "exceptions",
        include: [
          {
            model: shipment_inventory_exception,
            required: false,
            attributes: [
              "shipment_exception_id",
              [
                sequelize.literal(
                  "(select name FROM `shipment_exceptions` where shipment_exception_id = `exceptions->eid`.`shipment_exception_id`) "
                ),
                "exception_name",
              ],
            ],
            as: "eid",
            include: [
              {
                model: shipment_exception,
                attributes: [],
                required: true,
                as: "exception_list",
              },
            ],
          },
          {
            model: shipment_inventory_location,
            required: false,
            attributes: [
              "shipment_location_id",
              [
                sequelize.literal(
                  "(select name FROM `shipment_locations` where shipment_location_id = `exceptions->lid`.`shipment_location_id`) "
                ),
                "location_name",
              ],
            ],
            as: "lid",
            include: [
              {
                model: shipment_location,
                attributes: [],
                required: true,
                as: "location_list",
              },
            ],
          },
        ],
      },

      {
        model: shipment_inventory_forced,
        attributes: [
          "shipment_inventory_id",
          "current_stage_id",
          "local_current_stage_id",
          "shipment_job_id",
          "updated_at",
          "reason",
          "override_by_staff",
        ],
        as: "forced_inventory",
        include: [
          {
            model: staff,
            as: "staff",
            attributes: ["first_name", "last_name"],
          },
          {
            model: shipment_type_stage_for_shipment,
            attributes: ["name"],
            as: "shipment_type_stage_for_shipment",
          },
        ],
      },
      {
        model: shipment_inventory_job_scanned,
        attributes: [
          "current_stage_id",
          "local_current_stage_id",
          "shipment_inventory_id",
          "shipment_job_id",
          "created_at",
          "scanned_by_staff",
        ],
        as: "scanned_inventory",
        include: [
          {
            model: staff,
            as: "staff",
            attributes: ["first_name", "last_name"],
          },
          {
            model: shipment_type_stage_for_shipment,
            attributes: ["name"],
            as: "shipment_type_stage_for_shipment",
          },
        ],
      },
      {
        model: qr_code,
        attributes: [
          ...COMMON_QR_ATTRIBUTES,
          [
            sequelize.literal(`CONCAT('${Const_AWS_BASE}', qr_image)`),
            "qr_image",
          ],
          [
            sequelize.literal(
              "(select LPAD(label_number,8,0) FROM `qr_codes` where item_qr.qr_code_id =qr_codes.qr_code_id)"
            ),
            "label_number",
          ],
        ],
        required: false,
        as: "item_qr",
      },
      {
        model: shipment_room,
        as: "room",
        attributes: ["name"],
      },
      {
        model: staff,
        as: "prepared_staff",
        attributes: ["first_name", "last_name"],
      },
      {
        model: staff,
        as: "carrier_packed",
        attributes: ["first_name", "last_name"],
      },
      {
        model: staff,
        as: "disassembled_user",
        attributes: ["first_name", "last_name"],
      },
      {
        model: shipment_inventory_photo,
        attributes: [
          ["shipment_inventory_photo_id", "photo_id"],
          "description",
          "stage_id",
          "local_stage_id",

          "media",
          [
            sequelize.literal(
              `(CASE WHEN media IS NOT NULL AND local_stage_id is NULL AND stage_id is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item}', shipment_inventory.shipment_job_id, '/original/', media) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', media) END)`
            ),
            "item_photo",
          ],
        ],
        required: false,
        as: "item_photos",
        include: [
          {
            model: shipment_type_stage_for_shipment,
            attributes: ["name"],
            as: "shipment_type_stage_for_shipment",
          },
        ],
      },
      {
        model: shipment_inventory_thumbnail_photo,
        attributes: [
          ["shipment_inventory_photo_id", "photo_id"],
          "description",
          "stage_id2",
          "local_stage_id2",
          "mediaUrl",
          [
            sequelize.literal(
              `(CASE WHEN mediaUrl IS NOT NULL AND local_stage_id2 is NULL THEN CONCAT('${Const_AWS_BASE_Job_Item_thumbnail}', shipment_inventory.shipment_job_id, '/original/', mediaUrl) ELSE CONCAT('${Const_AWS_BASE_Shipment_inventory}', shipment_inventory.shipment_inventory_id, '/original/', mediaUrl) END)`
            ),
            "item_photo",
          ],
        ],
        required: false,
        as: "item_thumbnail_photo",
        include: [
          {
            model: shipment_type_stage_for_shipment,
            attributes: ["name"],
            as: "shipment_type_stage_for_shipment",
          },
        ],
      },

      {
        model: shipment_job,
        as: "shipment_job",
        attributes: [
          "shipment_job_id",
          "company_id",
          "job_status",
          "local_job_status",
        ],
      },
    ],
    limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 25,
    offset:
      fieldsAndValues.page_no > 1
        ? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
        : 0,
    order: [
      [
        fieldsAndValues.orderingField
          ? fieldsAndValues.orderingField
          : "created_at",
        fieldsAndValues.orderingWay ? fieldsAndValues.orderingWay : "DESC",
      ],
    ],
  });
};

exports.fetchAllItemListByWarehouse = async (fieldsAndValues) => {
  return shipment_inventory.findAll({
    where: {
      [Op.or]: [
        { item_name: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
        {
          shipment_inventory_id: {
            [Op.like]: "%" + fieldsAndValues.search + "%",
          },
        },
      ],
      deletedAt: null,
      storage_unit_id: { [Op.not]: null },
    },
    attributes: [
      ...COMMON_INVENTORY_ATTRIBUTES,
      [
        sequelize.literal(
          "(select random_number FROM `qr_codes` where qr_code_id = shipment_inventory.qr_id)"
        ),
        "qr_generate_code",
      ],
    ],

    include: [
      {
        model: unit_list,
        attributes: [
          "unit_id",
          "name",
          "storage_unit_id",
          "unitCode",
          "currentLocation",
          ["status", "unitStatus"],
        ],
        as: "unit_list",
      },

      {
        model: shipment_job,
        as: "shipment_job",
        where: {
          warehouseId: fieldsAndValues.warehouseId,
          storage_shipment_job_id: { [Op.not]: null },
        },
        attributes: [
          "shipment_job_id",
          "company_id",
          "job_status",
          "local_job_status",
          "warehouseId",
          "customer_id",
          "storage_shipment_job_id",
        ],
        include: [
          {
            model: customer,
            as: "customer_job",
          },
        ],
      },
    ],
    limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 25,
    offset:
      fieldsAndValues.page_no > 1
        ? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
        : 0,
    order: [
      [
        fieldsAndValues.orderingField
          ? fieldsAndValues.orderingField
          : "created_at",
        fieldsAndValues.orderingWay ? fieldsAndValues.orderingWay : "DESC",
      ],
    ],
  });
};

exports.allItemsCountByUnitIdController = async (request) => {
  return unit_list.findAndCountAll({
    where: {
      storage_unit_id: { [Op.in]: request.unitArray },
    },
    attributes: [
      "storage_unit_id",
      "name",
      [
        sequelize.literal(
          "(select count(storage_unit_id) FROM `shipment_inventories` where storage_unit_id = unit_list.storage_unit_id and deletedAt IS NULL)"
        ),
        "itemCount",
      ],
    ],
  });
};

exports.fetchAllUnitCountValidation = async (request) => {
  return shipment_inventory.findAndCountAll({
    where: {
      storage_unit_id: request.unitId,
      shipment_job_id: request.shipmentId,
    },
  });
};

exports.shuffleAndReshuffleUnitDetails = async (unitId) => {
  return unit_list.findOne({
    attributes: ["unit_id", "storage_unit_id"],
    where: {
      storage_unit_id: unitId,
    },
  });
};

exports.createBulkUnitList = async (unitList) => {
  return unit_list.bulkCreate(unitList);
};

exports.shuffleAndReshuffleItem = async (itemId, shipmentId, unitDetails) => {
  const updateData = await shipment_inventory.update(
    {
      storage_unit_id: unitDetails.storage_unit_id,
      unit_id: unitDetails.unit_id,
      shipment_job_id: shipmentId,
    },
    {
      where: {
        shipment_inventory_id: itemId,
      },
    }
  );
  return updateData;
};

exports.removeItemOldThumbnail = async (itemId) => {
  const updateData = await shipment_inventory_photo.update(
    {
      is_thumbnail: 0,
    },
    {
      where: {
        shipment_inventory_id: itemId,
      },
    }
  );
  return updateData;
};

exports.companyListBySuperAdmin = async (body) => {
  const whereCondition = {
    where: {
      status: "Active",
      roles: "COMPANYADMIN",
      is_deleted: "0",
      [Op.or]: [
        { company_name: { [Op.like]: "%" + body.search + "%" } },
        { company_identity: { [Op.like]: "%" + body.search + "%" } },
      ],
    },
    attributes: [
      "company_name",
      "group_id",
      "email",
      "company_id",
      "company_identity",
      "roles",
    ],
    order: [["company_name", "ASC"]],
  };
  if (body.page_size) {
    whereCondition.limit = parseInt(body.page_size);
    whereCondition.offset =
      body.page_no > 1 ? (parseInt(body.page_no) - 1) * body.page_size : 0;
  }
  return await company.findAndCountAll(whereCondition);
};

exports.companyListByGroup = async (body) => {
  const whereCondition = {
    where: {
      group_id: body.group_id,
      status: "Active",
      roles: "COMPANYADMIN",
      [Op.or]: [
        { company_name: { [Op.like]: "%" + body.search + "%" } },
        { company_identity: { [Op.like]: "%" + body.search + "%" } },
      ],
    },
    attributes: [
      "company_name",
      "group_id",
      "email",
      "company_id",
      "company_identity",
      "roles",
    ],
    order: [["company_name", "ASC"]],
  };
  if (body.page_size) {
    whereCondition.limit = parseInt(body.page_size);
    whereCondition.offset =
      body.page_no > 1 ? (parseInt(body.page_no) - 1) * body.page_size : 0;
  }
  return await company.findAndCountAll(whereCondition);
};

exports.isGroupExists = async (body) => {
  return await group.findOne({
    where: {
      group_id: body.group_id,
    },
  });
};

exports.updateItemThumbnail = async (photoId) => {
  const updateData = await shipment_inventory_photo.update(
    {
      is_thumbnail: 1,
    },
    {
      where: {
        shipment_inventory_photo_id: photoId,
      },
    }
  );
  return updateData;
};

exports.shuffleAndReshuffleItemHistoryUpdate = async (
  itemId,
  shipmentId,
  unitDetails
) => {
  const updateData = await shipment_inventory_unit_history.update(
    {
      storage_unit_id: unitDetails.storage_unit_id,
      unit_id: unitDetails.unit_id,
      shipment_job_id: shipmentId,
    },
    {
      where: {
        shipment_inventory_id: itemId,
      },
    }
  );
  return updateData;
};

exports.emptyUnitUpdate = async (emptyUnitIds) => {
  const updateData = await unit_list.update(
    {
      shipment_job_id: null,
      status: "empty",
    },
    {
      where: {
        storage_unit_id: { [Op.in]: emptyUnitIds },
      },
    }
  );
  return updateData;
};

exports.emptyUnitUpdateById = async (emptyUnitId) => {
  const updateData = await unit_list.update(
    {
      shipment_job_id: null,
      status: "empty",
    },
    {
      where: {
        storage_unit_id: emptyUnitId,
      },
    }
  );
  return updateData;
};

exports.newUnitUpdate = async (unitId, shipmentId) => {
  const updateData = await unit_list.update(
    {
      shipment_job_id: shipmentId,
      status: "occupied",
    },
    {
      where: {
        storage_unit_id: unitId,
      },
    }
  );
  return updateData;
};

exports.getAllShipmentInventories = async (
  storage_unit_id,
  shipment_job_id
) => {
  return shipment_inventory.findAll({
    where: {
      storage_unit_id,
      shipment_job_id,
    },
  });
};

exports.getAllShipmentsWithShipmentJobId = async (shipment_job_id) => {
  return shipment_inventory.findAll({
    where: {
      shipment_job_id,
      // isScannedFlag: 0,
      storage_unit_id: { [Op.ne]: null },
      deletedAt: null,
    },
  });
};

exports.updateShipmentInventoryWithShipmentInventoryId = async (
  shipment_inventory_id,
  stageId
) => {
  const [updateData] = await Promise.all([
    shipment_inventory.update(
      {
        storage_unit_id: null,
        unit_id: null,
        remove_from_storage_stage_id: stageId,
        is_item_assign_to_unit_completed: 0
      },
      {
        where: {
          shipment_inventory_id,
        },
      }
    ),
  ]);
  return updateData;
};

exports.removeStageItemStorageHistory = async (
  InventoryId,
  current_job_stage
) => {
  const updateData = await shipment_inventory_unit_history.update(
    {
      remove_stage_id: current_job_stage,
    },
    {
      where: {
        shipment_inventory_id: InventoryId,
      },
    }
  );
  return updateData;
};

exports.checkIsOldInventoryPhotoId = async (photoId) => {
  const data = await shipment_inventory_photo.findOne({
    where: {
      shipment_inventory_photo_id: photoId,
    },
  });
  if (data) {
    return true;
  } else {
    return false;
  }
};

exports.getShipmentRequiredStageDetails = async (shipment_job_id) => {
  const shipmentDetails = await shipment_job.findAll({
    include: [{ model: shipment_type_stage_for_shipment }],
    where: {
      shipment_job_id,
    },
  });

  return shipmentDetails;
};

exports.getJobSummaryMovegistics = async (jobId) =>
  shipment_job.findOne({
    attributes: [
      ...COMMON_JOB_ATTRIBUTES,

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_disassembled = "1" and deletedAt IS NULL )'
        ),
        "total_disassembled_items",
      ],

      [
        sequelize.literal(
          "(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_items",
      ],

      [
        sequelize.literal(
          "(select sum(volume) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_volume",
      ],
      [
        sequelize.literal(
          "(select sum(weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_firearm = "1" and deletedAt IS NULL)'
        ),
        "firearms_total_quantity",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and deletedAt IS NULL)'
        ),
        "total_cartons",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "0" and deletedAt IS NULL)'
        ),
        "total_cartons_cp",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_carton = "1" and packed_by_owner = "1" and deletedAt IS NULL)'
        ),
        "total_cartons_pbo",
      ],

      [
        sequelize.literal(
          '(select sum(declared_value) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
        ),
        "total_high_value",
      ],

      [
        sequelize.literal(
          "(select sum(pads_used) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL)"
        ),
        "total_pads_used",
      ],

      [
        sequelize.literal(
          '(select sum(pro_gear_weight) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_weight",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_pro_gear_items",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_electronics = "1" and deletedAt IS NULL)'
        ),
        "total_electronics_items",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_high_value = "1" and deletedAt IS NULL)'
        ),
        "total_highValue_items",
      ],

      [
        sequelize.literal(
          '(select count(shipment_inventory_id) FROM `shipment_inventories` where shipment_job_id = shipment_job.shipment_job_id and is_pro_gear = "1" and deletedAt IS NULL)'
        ),
        "total_is_pro_gear_items",
      ],

      [
        sequelize.literal(
          "(select CONCAT(first_name,' ', last_name)  FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "customer_name",
      ],
      [
        sequelize.literal(
          "(select account_id FROM `customers` where customer_id = shipment_job.customer_id) "
        ),
        "account_id",
      ],
      [
        sequelize.literal("(IF(shipment_name IS NULL, '', shipment_name))"),
        "shipment_name",
      ],
      [
        sequelize.literal(
          "(select name FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_status",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_item = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_item",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN is_add_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_add_exceptions",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN show_no_exceptions = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "show_no_exceptions",
      ],

      [
        sequelize.literal(
          "(select order_of_stages FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "current_job_stage",
      ],
      [
        sequelize.literal(
          "(select count(local_shipment_stage_id) FROM `shipment_type_stage_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id and status = 'active') "
        ),
        "total_stages",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN order_of_stages = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_first_stage",
      ],

      [
        sequelize.literal(
          "(select CASE WHEN scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_scan_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_scan_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN allow_default_manual_label = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "allow_default_manual_label",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN add_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "add_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN remove_items_to_inventory = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "remove_items_to_inventory",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN enable_partial_complete_stage = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "enable_partial_complete_stage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_into_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_into_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN assign_storage_units_to_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "assign_storage_units_to_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN unassign_storage_units_from_items = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "unassign_storage_units_from_items",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN scan_out_of_storage = '1' AND storage_shipment_job_id IS NOT NULL AND warehouseId IS NOT NULL THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "scan_out_of_storage",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN supervisor_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_supervisor_signature_require",
      ],
      [
        sequelize.literal(
          "(select CASE WHEN customer_signature_require = '1' THEN 'yes' ELSE 'no' END FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "is_customer_signature_require",
      ],
      [
        sequelize.literal(
          "(select name  FROM `shipment_type_for_shipments` where local_shipment_type_id = shipment_job.local_shipment_type_id ) "
        ),
        "shipment_type_name",
      ],
      [
        sequelize.literal(
          "(select why_supervisor_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_supervisor_signature_require_note",
      ],

      [
        sequelize.literal(
          "(select why_customer_signature_require_note FROM `shipment_type_stage_for_shipments` where local_shipment_stage_id = shipment_job.local_job_status) "
        ),
        "why_customer_signature_require_note",
      ],
      [
        sequelize.literal(
          `(if(
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL) = 
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "1")
						,"yes","no"))`
        ),
        "isAllItemScanned",
      ],
      [
        sequelize.literal(
          `(if(
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "0") = 
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NOT NULL AND isScannedFlag = "0")
						,"yes","no"))`
        ),
        "isAllItemsAssignToUnits",
      ],
      [
        sequelize.literal(
          `(if(
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "0") = 
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND storage_unit_id IS NULL AND isScannedFlag = "0")
						,"yes","no"))`
        ),
        "isAllItemsUnassignToUnits",
      ],
      [
        sequelize.literal(
          `(if(
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND isScannedFlag = "0") = 
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND is_additional_scan = "1" AND isScannedFlag = "0")
						,"yes","no"))`
        ),
        "isAllAdditionalScanStageItemsScanned",
      ],
      [
        sequelize.literal(
          `(if(
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL) = 
						  (select count(shipment_inventory_id) FROM shipment_inventories where shipment_job_id = shipment_job.shipment_job_id and deletedAt IS NULL AND is_remove_scan = "1")
						,"yes","no"))`
        ),
        "isAllRemoveScanStageItemsScanned",
      ],
      [
        sequelize.literal(
          `(CASE WHEN is_job_complete_flag = '1' THEN 'yes' ELSE 'no' END)`
        ),
        "is_job_complete",
      ],
    ],
    where: {
      shipment_job_id: jobId,
    },
    include: [
      {
        model: company,
        attributes: ["company_id"],
        required: true,
        as: "job_company",
        include: [
          {
            model: staff,
            where: { roles: "ADMIN", is_deleted: 0, status: "active" },
            attributes: [
              "staff_id",
              ["roles", "role"],
              "first_name",
              "last_name",
            ],
            required: true,
            as: "staffs",
          },
        ],
      },
      {
        model: customer,
        attributes: [],
        required: true,
        as: "customer_job",
      },
      {
        model: shipment_type_stage_for_shipment,
        as: "local_shipment_job_status",
        attributes: [...BASIC_JOB_STAGES_ATTRIBUTES2],
      },
      {
        model: shipment_job_assign_worker_list,
        required: false,
        attributes: [
          "staff_id",
          "role",
          [
            sequelize.literal(
              "(select first_name FROM `staffs` where staff_id = assign_worker.staff_id) "
            ),
            "first_name",
          ],
          [
            sequelize.literal(
              "(select last_name FROM `staffs` where staff_id = assign_worker.staff_id) "
            ),
            "last_name",
          ],
        ],
        as: "assign_worker",
        include: [
          {
            model: staff,
            attributes: [],
            required: true,
            as: "assign_worker_detail",
          },
        ],
      },
      {
        model: shipment_inventory,
        required: false,
        as: "job_items",
      },
    ],
    raw: false,
    order: [["shipment_job_id", "DESC"]],
    subQuery: false,
  });

const is_job_complete = `IF(IF((select shipment_stage_id
              from shipment_type_stages
                       inner join shipment_types st
                                  on shipment_type_stages.shipment_type_id = st.shipment_type_id and
                                     shipment_type_stages.status = 'active'
              where st.shipment_type_id = shipment_job.shipment_type_id
              order by order_of_stages desc
              limit 1) = job_status,
             (
                     IF((select sts.scan_require
                         from shipment_type_stages sts
                         where shipment_stage_id = job_status) = 1,
                        ((SELECT count(shipment_inventory_id)
                          FROM \`shipment_inventory_forced\` AS \`forced_inventory\`
                          WHERE (\`forced_inventory\`.\`current_stage_id\` = job_status and
                                 \`forced_inventory\`.\`shipment_job_id\` = shipment_job.shipment_job_id)) +
                         (SELECT count(shipment_inventory_id)
                          FROM \`shipment_inventory_job_scanned\`
                          WHERE \`shipment_inventory_job_scanned\`.\`current_stage_id\` = job_status
                            and \`shipment_inventory_job_scanned\`.\`shipment_job_id\` =
                                shipment_job.shipment_job_id)) =
                        (select count(shipment_inventory_id)
                         from shipment_inventories
                         where shipment_job_id = shipment_job.shipment_job_id
                           and deletedAt is null), true)
                     and
                     (select customer_signature
                      from shipment_job_signatures as sjs
                               left join shipment_type_stages sts on sts.shipment_stage_id = sjs.stage
                      where shipment_job_id = shipment_job.shipment_job_id
                        and stage = job_status
                        and sts.customer_signature_require = 1
                        and customer_signature != ''
                        and customer_signature is not null) is not null
                     and
                     (select supervisor_signature
                      from shipment_job_signatures as sjs
                               left join shipment_type_stages sts on sts.shipment_stage_id = sjs.stage
                      where shipment_job_id = shipment_job.shipment_job_id
                        and stage = job_status
                        and sts.supervisor_signature_require = 1
                        and supervisor_signature != ''
                        and supervisor_signature is not null) is not null
                 ), false),
          'yes', 'no')`;
