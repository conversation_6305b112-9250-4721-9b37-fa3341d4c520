
const {
    company_token,
    company,
    sequelize
} = require("../../database/schemas");
const { Op, literal } = require("sequelize");
const StaffModel = require("../../models/Admin/staffModel");

exports.companyStatusModel = async (body) => {
    return company.findOne({
        where: {
            company_id: body.company_id
        },
        attributes: [
            "company_id",
            "storage_company_id",
            "company_name",
            "company_identity",
            "status",
            "email",
            [
                sequelize.literal(
                    "(select company_key FROM `company_tokens` where company_id = company.company_id) "
                ),
                "movegistics_key",
            ],
            [
                sequelize.literal(
                    "(CASE WHEN (SELECT isEnable FROM `company_tokens` WHERE company_id = company.company_id) = 1 THEN 'active' ELSE 'inactive' END)"
                ),
                "movegistics_key_status",
            ],
            [
                sequelize.literal(
                    "(select integration_key FROM `company_integration_keys` where company_id = company.company_id) "
                ),
                "mover_storage_integration_key",
            ],
            [
                sequelize.literal(
                    "(select status FROM `company_integration_keys` where company_id = company.company_id) "
                ),
                "mover_storage_integration_key_status",
            ],
        ]
    })
}

exports.getCompanyIdOfCompanyKey = async (request) => {
    return company_token.findOne({
        where: {
            company_key: request.company_key
        }
    })
}

exports.createCompanyApiKey = async (key, details) => {
    let data = {
        company_key: key,
        company_identity: details.company_identity,
        company_id: details.company_id,
    }
    return company_token.create(data)
}

exports.checkApiKeyExits = async (request) => {
    return company_token.findOne({
        where: {
            company_id: request.company_id
        }
    })
}

exports.listCompanyKey = async (request) => {
    return company_token.findAndCountAll({
        where: {
            company_id: request.company_id,
        },
        attributes: ["company_key_id", "company_id", "company_key", "company_identity", "isEnable"]
    });
}

exports.deleteCompanyKey = async (request) => {
    return company_token.destroy({
        where:
            { company_id: request.company_id }
    }
    );
};

exports.changeKeyStatusModel = async (request) => {
    return company_token.update(
        {
            isEnable: literal('CASE WHEN isEnable = "0" THEN "1" ELSE "0" END'),
        },
        { where: { company_id: request.company_id } }
    );
};



exports.fetchCompanyKeyDetails = async (getUserDetails) => {
    if (getUserDetails.company_id !== null) {
        return company_token.findOne({
            where: {
                company_id: getUserDetails.company_id
            },
        })
    }
    else if (getUserDetails.staff_id !== null) {
        let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);
        return company_token.findOne({
            where: {
                company_id: getStaffDetails.company_id
            }
        })
    }
    return null
}
