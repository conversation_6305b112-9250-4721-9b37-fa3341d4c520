'use strict';

module.exports = {
    up: async (queryInterface, Sequelize) => {
        try {
            await queryInterface.addColumn(
                'groups',
                'make_account_id_mandatory',
                {
                    type: Sequelize.BOOLEAN,
                    allowNull: true,
                    defaultValue: 0,
                },
                {
                    logging: true,
                }
            );
            return Promise.resolve();
        } catch (e) {
            return Promise.reject(e);
        }
    },

    down: async (queryInterface, Sequelize) => {
        try {
            await queryInterface.removeColumn('groups', 'make_account_id_mandatory');
            return Promise.resolve();
        } catch (e) {
            return Promise.reject(e);
        }
    },
};
