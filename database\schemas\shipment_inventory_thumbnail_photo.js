"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_inventory_thumbnail_photo = sequelize.define(
		"shipment_inventory_thumbnail_photo",
		{
			shipment_inventory_photo_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			shipment_inventory_id: DataTypes.INTEGER,
			stage_id2: DataTypes.INTEGER,
			local_stage_id2: DataTypes.INTEGER,

			mediaUrl: DataTypes.TEXT,
			description: DataTypes.TEXT,
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	shipment_inventory_thumbnail_photo.associate = function (models) {
		// associations can be defined here
		shipment_inventory_thumbnail_photo.belongsTo(models.shipment_inventory, {
			foreignKey: "shipment_inventory_id",
		});
		shipment_inventory_thumbnail_photo.belongsTo(models.shipment_type_stage, {
			foreignKey: "stage_id2",
		});

		shipment_inventory_thumbnail_photo.belongsTo(models.shipment_type_stage_for_shipment, {
			foreignKey: "local_stage_id2",
		});

	};
	return shipment_inventory_thumbnail_photo;
};
