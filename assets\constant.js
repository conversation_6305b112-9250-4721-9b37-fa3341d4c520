// status codes //
global.SUCCESS_CODE = 200;
global.CREATED_CODE = 201;
global.NO_CONTENT_CODE = 201;
global.NOT_FOUND_CODE = 404;
global.BAD_REQUEST_CODE = 400;
global.UNAUTHORIZED_CODE = 401;
global.CONFLICT_CODE = 409;
global.EXPECTATION_FAILED_CODE = 417;
global.NOT_VALID_DATA_CODE = 422;
global.SERVER_ERROR_CODE = 500;

// error success messages //
global.INVALID_X_API_KEY = "plz send valid x_api_key.";
global.UNAUTHORIZED_ACCESS = "Unauthorized Access.";
global.KEY_GENERATED = "Key generated successfully.";
global.KEY_GENERATE_FAIL = "Error in key generate, please try again!";
global.LOGIN_SUCCESS = "Logged in successfully.";
global.LOGIN_EMAIL_FAIL = "Email is not registered with us.";
global.LOGIN_COMPANY_FAIL = "Email is not registered with given company.";
global.LOGIN_PASSWORD_FAIL = "Please enter valid email/password.";
global.LOGIN_FAIL = "Please enter valid email/password.";
global.USER_REGISTERED = "Your account is created successfully.";
global.USER_REGISTER_FAIL = "Some error while register. Please try again!";
global.USER_INACTIVE = "Your account is inactive. Please contact administrator.";
global.COMPANY_INACTIVE = "Your company is inactive. Please contact administrator.";
global.EMAIL_EXIST = "Email is already registered. Please try with another email!";
global.LOGOUT_SUCCESS = "Logged out successfully";
global.LOGOUT_FAIL = "Error when logged out. Please try again!";
global.EDIT_PROFILE_SUCCESS = "Profile updated successfully.";
global.EDIT_PROFILE_FAIL = "Error while updating the profile. Please try again!";
global.PAGE_SUCCESS = "Page retrieved successfully!";
global.PAGE_FAIL = "Error in page retrieved, please try again!";
global.OLD_PASSWORD_NOT_MATCH = "Old password is incorrect.";
global.CHANGE_PASSWORD_SUCCESS = "Password has been changed successfully";
global.CHANGE_PASSWORD_FAIL = "Error while changing the password. Please try again!";
global.USER_RETRIVED_SUCCESS = "User retrieved successfully!.";
global.USER_NOT_FOUND = "User does not exists!.";
global.USER_PROFILE_FAIL = "Error in user profile. Please try again!";
global.JOB_RETRIEVED_SUCCESS = "Job retrieved successfully!.";
global.EXCEPTION_RETRIEVED_SUCCESS = "Exception retrieved successfully!.";

global.JOB_NOT_FOUND = "Job does not exists!.";
global.EXCEPTION_NOT_FOUND = "Exception does not exists!.";

global.COMPANY_NOT_FOUND = "Company does not exists!.";
global.ADD_ITEM_SUCCESS = "Item added successfully!";
global.ADD_ITEM_FAIL = "Error in add item. Please try again!";
global.EDIT_ITEM_SUCCESS = "Item edited successfully!";
global.ASSIGN_UNIT_ITEM_SUCCESS = "Unit assigned to the item Successfully.!";
global.EDIT_ITEM_FAIL = "Error in edit item. Please try again!";
global.REMOVE_ITEM_SUCCESS = "Item removed successfully!";
global.REMOVE_ITEM_FAIL = "Error in remove item. Please try again!";
global.CONTACT_EMAIL_SENT = "Your request has been submitted successfully.";
global.FEEDBACK_EMAIL_SENT = "Your feedback has been sent successfully.";
global.ADD_SHIPMENT_INVENTORY_MEDIA_SUCCESS = "Shipment inventory media added successfully!";
global.STATUS_CHANGE_FAIL = "Error in status change, please try again!";
global.STATUS_CHANGE_SUCCESS = "Status change successfully!";
global.EMAIL_NOT_REGISTERED = "Email is not registered with us.";
global.EMAIL_SENT = "Email has been sent successfully!";
global.MAIL_NOT_FOUND = "No email Id for this shipment's customer! please add one and try again";
global.FORGOT_EMAIL_SENT = "We have sent you password reset link in your email.";
global.GET_SUMMARY_SUCCESS = "Job Summary retrieved successfully!.";
global.GET_SUMMARY_FAIL = "Error in retrieve job summary, please try again.";
global.GET_SUMMARY_EMPTY = "This job summary do not exist.";
global.ADD_SIGNATURE_SUCCESS = "Signature added successfully!";
global.ADD_SIGNATURE_FAIL = "Error in add signature. Please try again!";
global.MAIL_SENT_TO_CUSTOMER = "Mail sent successfully.";

// pagination constant

global.JOB_LIST_PER_PAGE = 5;

// common attribute list //
global.ADMIN_ATTRIBUTES = [
	"admin_id",
	"first_name",
	"last_name",
	"gender",
	"email",
	"password",
	"status",
	"role",
	"photo",
];
global.COMMON_COMPANY_ATTRIBUTES = [
	"company_id",
	"company_name",
	"address1",
	"address2",
	"city",
	"group_id",
	"state",
	"zipCode",
	"country",
	"phone",
	"country_code",
	"company_identity",
	"email",
	"notes",
	"push_notification",
	"is_verified",
	"status",
	"created_at",
];

global.COMMON_COMPANY_ATTRIBUTES2 = [
	"company_id",
	"company_name",
	"photo",
	"address1",
	"address2",
	"group_id",
	"city",
	"state",
	"zipCode",
	"country",
	"phone",
	"country_code",
	"company_identity",
	"email",
	"password",
	"notes",
	"notificationStatus",
	"push_notification",
	"verification_code",
	"is_verified",
	"status",
	"roles",
	"group_id",
	"is_deleted",
	"created_at",
	"updated_at",
];

global.COMMON_STAFF_ATTRIBUTES = [
	"staff_id",
	"warehouse_id",
	"storage_staff_id",
	"company_id",
	"roles",
	"first_name",
	"last_name",
	"email",
	"phone",
	"country_code",
	"photo",
	"notes",
	"push_notification",
	"is_verified",
	"status",
	"created_at",
	"is_admin_can_edit_shipment_type"
];
global.COMMON_JOB_ATTRIBUTES = [
	["shipment_job_id", "job_id"],
	"job_number",
	"storage_shipment_job_id",
	"shipment_type_id",
	"warehouseId",
	"local_shipment_type_id",
	"company_id",
	"customer_id",
	"contact_reference",
	"account_reference",
	"opportunity_reference",
	"move_coordinator",
	"wo_reference",
	"source",
	"external_reference",
	"external_reference_2",
	"external_reference_3",
	"estimated_weight",
	"estimated_volume",
	"notes",
	"email",
	"email2",
	"customer_id",
	"pickup_address",
	"pickup_address2",
	"pickup_city",
	"pickup_state",
	"pickup_zipcode",
	"pickup_country",
	"delivery_address",
	"delivery_address2",
	"delivery_city",
	"delivery_state",
	"delivery_zipcode",
	"delivery_country",
	"pickup_date",
	"delivery_date",
	"shipment_summary",
	"warehouse",
	"job_status",
	"local_job_status",
	"status",
	"damaged_items",
	"created_at",
	"updated_at",
	"deletedAt",
	"is_job_complete_flag",
];
global.CUSTOMER_JOB_ATTRIBUTES_FOR_STORAGE = [
	["shipment_job_id", "job_id"],
	"job_number",
	"shipment_type_id",
	"local_shipment_type_id",
	"email",
	"email2",
	"customer_id",
	"pickup_address",
	"delivery_address",
	"external_reference",
	"pickup_date",
	"delivery_date",
	"notes",
	"shipment_summary",
	"job_status",
	"local_job_status",
	"total_items",
	"total_cartons",
	"total_cartons_cp",
	"total_cartons_pbo",
	"total_volume",
	"total_weight",
	"total_high_value",
	"total_pads_used",
	"total_pro_gear_items",
	"total_pro_gear_weight",
	"damaged_items",
	"status",
	"created_at",
	"updated_at",
	"deletedAt",
	"company_id",
	"external_reference_3",
	"external_reference_2",
	"warehouse",
	"pickup_city",
	"pickup_state",
	"pickup_zipcode",
	"pickup_country",
	"delivery_city",
	"delivery_state",
	"delivery_zipcode",
	"delivery_country",
	"pickup_address2",
	"delivery_address2",
	"contact_reference",
	"account_reference",
	"opportunity_reference",
	"move_coordinator",
	"source",
	"wo_reference",
	"is_job_complete_flag",
	"estimated_weight",
	"estimated_volume",
	"firearms_total_quantity"


];

global.CUSTOMER_JOB_ATTRIBUTES = [
	["shipment_job_id", "job_id"],
	"job_number",
	"warehouse",
	"email",
	"customer_id",
	"pickup_address",
	"pickup_address2",
	"pickup_city",
	"pickup_state",
	"pickup_zipcode",
	"pickup_country",
	"delivery_address",
	"delivery_address2",
	"delivery_city",
	"delivery_state",
	"delivery_zipcode",
	"delivery_country",
	"pickup_date",
	"delivery_date",
	"shipment_summary",
	"job_status",
	"local_job_status",
	"total_items",
	"total_cartons",
	"total_cartons_cp",
	"total_cartons_pbo",
	"total_volume",
	"total_weight",
	"total_high_value",
	"total_pads_used",
	"total_pro_gear_items",
	"total_pro_gear_weight",
	"damaged_items",
	"created_at",
];

global.COMMON_INVENTORY_ATTRIBUTES = [
	["shipment_inventory_id", "inventory_id"],
	["shipment_job_id", "job_id"],
	"storage_unit_id",
	"room_id",
	"unit_id",
	"qr_id",
	"packed_by_owner",
	"isScannedFlag",
	"is_item_scanned_remove_from_storage",
	"item_name",
	["description", "desc"],
	["carrier_packed_user_id", "packed_user_id"],
	"carrier_packed_user_name_manually",
	"disassembled_user_name_manually",
	"prepared_user_name_manually",
	"disassembled_user_id",
	"serial_number",
	"declared_value",
	"pro_gear_weight",
	"progear_name",
	"firmarm_serial_number",
	"label_no",
	"lot_no",
	"color",
	"volume",
	"seal_number",
	"weight",
	"length",
	"width",
	"height",
	"pads_used",
	"prepared_by",
	"notes",
	"created_at",
];

global.COMMON_PAGE_ATTRIBUTES = ["page_id", "page_title", "page_content", "status"];

global.COMMON_QR_ATTRIBUTES = ["random_number", "job_id", "qr_code_id"];
global.COMMON_LABEL_ATTRIBUTES = ["random_number", "company_id", "qr_code_id"];


global.COMMON_CUSTOMER_ATTRIBUTES = [
	"customer_id",
	"storage_customer_id",
	"company_id",
	// "customer_name",
	"first_name",
	"last_name",
	"address1",
	"address2",
	"city",
	"state",
	"zipCode",
	"country",
	"country_code",
	"phone",
	"phone2",
	"phone3",
	"email",
	"email2",
	"account_id",
	"account_name",
	"sales_rep",
	"total_shipment",
	"push_notification",
	"is_verified",
	"status",
	"created_at",
	"updated_at",
	"password",
	"is_deleted",
	"is_invited",
	"notes",
];
// Admin Module Messages //
global.ADMIN_INACTIVE = "Your account is in active, please contact administrator.";
global.ADMIN_EXIST = "User already exists with this email.";
global.ADMIN_NOT_EXIST = "User does not exists with this email.";
global.ADMIN_SIGNUP_SUCCESS = "Admin user registered successfully.";
global.PROFILE_SUCCESS = "Profile updated successfully.";
global.PASSWORD_NOT_MATCH = "Password not matched, please try again!.";
global.PASSWORD_CHANGE_SUCCESS = "Password changed successfully!.";

// Company management module
global.COMPANY_EMAIL_EXIST = "Email already exists, please try with another!.";
global.COMPANY_IDENTITY_EXIST = "Company identity already exists, please try with another!.";
global.COMPANY_ADD_SUCCESS = "Company created successfully!.";
global.COMPANY_ADD_FAIL = "Error in company created, please try again!.";
global.COMPANY_RETRIEVED_SUCCESS = "Company retrieved successfully!.";
global.COMPANY_NOT_FOUND = "Could not find the company. Please try again!.";
global.COMPANY_UPDATE_SUCCESS = "Company updated successfully!.";
global.COMPANY_UPDATE_FAIL = " Error in company update, please try again!.";
global.COMPANY_DELETE_SUCCESS = "Company deleted successfully!.";
global.COMPANY_DELETE_FAIL = "Error while delete company, please try again!";
global.VALID_COMPANY_ID = "Please Enter valid companyId!";
global.VALID_COMPANY_KEY = "Unauthorized access error, Please enter valid company key!";

//Company Key
global.COMPANY_KEY_ADD_SUCCESS = "Api key created successfully!.";
global.COMPANY_KEY_ADD_ERROR = "Error while adding api key!.";
global.COMPANY_KEY_EXIST = "Api key already exists!.";
global.COMPANY_KEY_RETRIEVED_SUCCESS = "Api key retrieved successfully!.";
global.COMPANY_KEY_LIST_EMPTY = "No api key found!.";
global.COMPANY_KEY_DELETE_SUCCESS = "Api key deleted successfully!.";
global.COMPANY_KEY_DELETE_FAIL = "Error while deleting api key!.";
global.COMPANY_KEY_STATUS_CHANGE = "Api key status change successfully!.";
global.COMPANY_KEY_STATUS_FAIL = "Error while changing api key status!.";
global.COMPANY_INTEGRATION_KEY_STATUS_CHANGE = "Integration key status change successfully!.";
global.COMPANY_INTEGRATION_KEY_STATUS_FAIL = "Error while changing integration key status!.";

// Customer management module
global.CUSTOMER_EMAIL_EXIST = "Email already exists, please try with another!.";
global.CUSTOMER_ADD_SUCCESS = "Customer created successfully!.";
global.CUSTOMER_UPDATED_SUCCESS = "Customer updated successfully!.";
global.CUSTOMER_NOT_FOUND = "Customer not found!.";
global.CUSTOMER_SET_PASSWORD = "Please set password!.";
global.CUSTOMER_SIGN_IN = "You can proceed to sign in!.";
global.CUSTOMER_LIST_EMPTY = "No customer found!.";
global.CUSTOMER_UPDATE_FAIL = " Error in customer update, please try again!.";
global.CUSTOMER_RETRIEVED_SUCCESS = "Customers retrieved successfully!.";
global.CUSTOMER_DELETE_SUCCESS = "Customer deleted successfully!.";
global.CUSTOMER_DELETE_ASSIGN_JOB =
	"Cannot delete this Customer as the Customer has Shipments associated!";
global.CUSTOMER_STATUS_UPDATE_ASSIGN_JOB =
	"Cannot Inactive this Customer as the Customer has Shipments associated!";
global.CUSTOMER_DELETE_FAIL = "Error while delete customer, please try again!";

global.UNIT_RETRIEVED_SUCCESS = "Units retrieved successfully!.";
global.USER_RETRIEVED_SUCCESS = "Users retrieved successfully!.";



global.ROOM_RETRIEVED_SUCCESS = "Rooms retrieved successfully!.";
global.ROOM_ADDED_SUCCESS = "Room added successfully!.";
global.ROOM_ADDED_FAILURE = "Error while adding room!.";
global.ROOM_EDIT_SUCCESS = "Room edited successfully!.";
global.ROOM_DELETE_SUCCESS = "Room deleted successfully.!";
global.ROOM_EDIT_FAILURE = "Error while editing room!.";
global.ROOM_DETAILS = "Room details!.";
global.QRDETAILS_DETAILS = "Qr Code details!.";

global.ROOM_NOT_FOUND = "Rooms not found!.";
global.QRCODE_NOT_FOUND = "QrCode not found!.";
global.RANDOM_NUMBER_DETAILS_NOT_FOUND = "Please scan a valid Qr code!.";
global.RANDOM_NUMBER_NOT_ASSOCIATED_WITH_SHIPMENT = "This Qr code is not associated with any inventory!.";

global.TAG_RETRIEVED_SUCCESS = "Tag retrieved successfully!.";
global.TAG_ADDED_SUCCESS = "Tag added successfully!.";
global.TAG_ADDED_FAILURE = "Error while adding tag!.";
global.TAG_EDIT_SUCCESS = "Tag edited successfully!.";
global.TAG_DELETE_SUCCESS = "Tag deleted successfully!.";
global.TAG_EDIT_FAILURE = "Error while editing tag!.";
global.TAG_DETAILS = "Tag details!.";
global.TAG_NOT_FOUND = "Tag not found!.";

global.ITEM_SUG_RETRIEVED_SUCCESS = "Item retrieved successfully!.";
global.ITEM_SUG_ADDED_SUCCESS = "Item added successfully!.";
global.ITEM_SUG_ADDED_FAILURE = "Error while adding item!.";
global.ITEM_SUG_EDIT_SUCCESS = "Item edited successfully!.";
global.ITEM_SUG_EDIT_FAILURE = "Error while editing item!.";
global.ITEM_SUG_DETAILS = "Item details!.";
global.ITEM_SUG_NOT_FOUND = "Item not found!.";
global.ITEM_SUG_DELETE_SUCCESS = "Item deleted successfully!.";

global.GROUP_SUG_RETRIEVED_SUCCESS = "Group retrieved successfully!.";
global.GROUP_SUG_ADDED_SUCCESS = "Group added successfully!.";
global.GROUP_SUG_ADDED_FAILURE = "Error while adding group!.";
global.GROUP_SUG_EDIT_SUCCESS = "Group edited successfully!.";
global.GROUP_SUG_EDIT_FAILURE = "Error while editing group!.";
global.GROUP_SUG_DETAILS = "Group details!.";
global.GROUP_SUG_NOT_FOUND = "Group not found!.";
global.GROUP_SUG_DELETE_SUCCESS = "Group deleted successfully!.";
global.GROUP_SUG_USER_DELETE_SUCCESS = "Group user deleted successfully!.";
global.GROUP_DELETE_ASSIGN_COMPANY =
	"Cannot delete this Group as the Group has Company associated!";


// Shipment type management module
global.SHIPMENT_TYPE_ADD_SUCCESS = "Shipment type created successfully!";
global.SHIPMENT_TYPE_RETRIEVED_SUCCESS = "Shipment type retrieved successfully!.";
global.SHIPMENT_TYPE_NOT_FOUND = "Shipment type not found!.";
global.SHIPMENT_TYPE_ADD_FAIL = "Error in shipment type created, please try again!.";
global.SHIPMENT_TYPE_UPDATED_FAIL = "Error in shipment type updated, please try again!.";
global.SHIPMENT_TYPE_UPDATED_SUCCESS = "Shipment type updated successfully!.";
global.SHIPMENT_TYPE_DELETED_SUCCESS = "Shipment type deleted successfully!.";
global.SHIPMENT_TYPE_DELETED_FAIL = " Error in shipment type deleted, please try again!.";
global.SHIPMENT_STAGE_RETRIEVED_SUCCESS = "Shipment stage retrieved successfully!.";
global.SHIPMENT_STAGE_NOT_FOUND = "Shipment stage not found!.";
global.SHIPMENT_STAGE_UPDATE_SUCCESS = "Shipment stage updated successfully!.";
global.SHIPMENT_STAGE_UPDATE_FAIL = "Error in shipment stage updated, please try again!.";

// Staff management module
global.STAFF_EMAIL_EXIST = "Email already exists, please try with another!.";
global.STAFF_ADD_SUCCESS = "Staff created successfully!.";
global.STAFF_ADD_FAIL = "Error in staff created, please try again!.";
global.STAFF_RETRIEVED_SUCCESS = "Staff retrieved successfully!.";
global.STAFF_NOT_FOUND = "Could not find the staff. Please try again!.";
global.STAFF_UPDATE_SUCCESS = "Staff updated successfully!.";
global.STAFF_UPDATE_FAIL = " Error in staff update, please try again!.";
global.STAFF_DELETE_SUCCESS = "Staff deleted successfully!.";
global.STAFF_DELETE_FAIL = "Error while delete staff, please try again!";
global.STAFF_PASSWORD_UPDATED = "password change successfully";

// aws constants //
global.Const_AWS_BUCKET = process.env.AWS_BUCKET;
global.Const_AWS_User_Profile = "mover-inventory/staffProfile/";
global.Const_AWS_Job_Item = "mover-inventory/jobItem/";
global.Const_AWS_Job_Item_thumbnail = "mover-inventory/jobItemThumbnail/";
global.Const_AWS_Shipment_inventory = "mover-inventory/shipmentInventory/";
global.Const_AWS_Shipment_Document = "mover-inventory/shipmentDocument/";
global.Const_AWS_Job_Signature = "mover-inventory/jobSignature/";
global.Const_AWS_Company_Profile = "mover-inventory/companyProfile/";
global.Const_AWS_Staff_Profile = "mover-inventory/staffProfile/";
global.Const_AWS_Customer_Profile = "mover-inventory/customerProfile/";
global.Const_AWS_Warehouse_Man_Profile = "mover-inventory/warehouseManProfile/";
global.Const_AWS_BASE = process.env.AWS_BASEURL;
global.Const_AWS_BASE_User_Profile = Const_AWS_BASE + Const_AWS_User_Profile;
global.Const_AWS_BASE_Company_Profile = Const_AWS_BASE + Const_AWS_Company_Profile;
global.Const_AWS_BASE_Staff_Profile = Const_AWS_BASE + Const_AWS_Staff_Profile;
global.Const_AWS_BASE_Customer_Profile = Const_AWS_BASE + Const_AWS_Customer_Profile;
global.Const_AWS_BASE_shipment_Document = Const_AWS_BASE + Const_AWS_Shipment_Document;
global.Const_AWS_BASE_Warehouse_Man_Profile = Const_AWS_BASE + Const_AWS_Warehouse_Man_Profile;
global.Const_AWS_BASE_Job_Item = Const_AWS_BASE + Const_AWS_Job_Item;
global.Const_AWS_BASE_Job_Item_thumbnail = Const_AWS_BASE + Const_AWS_Job_Item_thumbnail;
global.Const_AWS_BASE_Shipment_inventory = Const_AWS_BASE + Const_AWS_Shipment_inventory;
global.Const_AWS_BASE_Job_Signature = Const_AWS_BASE + Const_AWS_Job_Signature;

// Shipment Inventory
global.SHIPMENT_INVENTORY_NOT_FOUND = "Could not find this item in inventory. Please try again!.";
global.SHIPMENT_INVENTORY_EMPTY = "No item are there in this inventory.";
global.SHIPMENT_INVENTORY_ALREADY_SCANNED = "Item is already scanned.";
global.SHIPMENT_INVENTORY_NOT_ASSOC_JOB =
	"This inventory item is not associated with specified job.";
global.SHIPMENT_INVENTORY_ASSIGN_TO_UNITS = "Items assign to the units. we can not delete shipment, please try again!."
global.SHIPMENT_INVENTORY_OVERRRIDDEN = "This inventory item is already overridden.";
global.OVERRIDE_ITEM_SUCCESS = "Inventory item overriding successful.";
global.OVERRIDE_ITEM_FAILED = "Error overriding this inventory item. please try again!";
global.SCAN_NOT_PERFORMED = "Error scanning item. Please try again!.";
global.SCAN_COMPLETED = "Item scanned successfully!";
global.SHIPMENT_INVENTORY_ALREADY_LABELNUMBER = "Label number already exists.";
global.SHIPMENT_INVENTORY_NOT_ASSOC_SHIPMENT = "This QR code is assigned to another shipment/company.";
global.SHIPMENT_INVENTORY_NOT_ASSOC_QR = "This QR code is not assigned to item.";


// Shipment module
global.SHIPMENT_ADD_SUCCESS = "Job Shipment created successfully!";
global.SHIPMENT_NOT_FOUND = "This Job Shipment is not available.";
global.SHIPMENT_RETRIEVED_SUCCESS = "Job Shipment fetched successfully";
global.SHIPMENT_DELETED_SUCCESS = "Job Shipment deleted successfully";
global.SHIPMENT_UPDATED_SUCCESS = "Job Shipment updated successfully";
global.SHIPMENT_PRINT_FAILED = "Job Shipment printing failed";
global.SHIPMENT_NOT_UPDATED_SUCCESS = "Shipment Job not updated.";
global.SHIPMENT_EMPTY = "Job Shipment detail not found";
global.MOVE_CO_NOT_FOUND = "Could not find the move coordinator. Please try again!.";
global.SALES_NOT_FOUND = "Could not find the sales representative. Please try again!.";
global.NOT_DELETE = "Cannot delete this User as there are Shipments assigned to this User!";
global.SHIPMENT_LIST_EMPTY = "Job Shipment list Empty";
global.INACTIVE_CUSTOMER_UPDATE_FOR_SHIPMENT = "The selected customer is inactive. Please select another customer or reactivate the selected customer to update the shipment."
global.INACTIVE_CUSTOMER_LOGIN_FOR_CUSTOMER_PORTAL = "The selected customer is inactive. Please inform the location to get this fixed."

global.COMMON_JOB_ATTRIBUTES_STORAGE = [
	"shipment_job_id",
	"storage_shipment_job_id",
	"warehouseId",
	"job_number",
	"email",
	"pickup_address",
	"delivery_address",
	"pickup_date",
	"delivery_date",
	"shipment_name",
	"pickup_city",
	"pickup_state",
	"pickup_zipcode",
	"pickup_country",
	"delivery_city",
	"delivery_state",
	"delivery_zipcode",
	"delivery_country",
	"pickup_address2",
	"delivery_address2",
]

global.COMMON_QR_ATTRIBUTES_STORAGE = [
	"qr_code_id",
	"job_id",
	"random_number",
	"qr_image",
	"status",
	"type",
	"label_number"
];


global.COMMON_JOB_SHIPMENT_ATTRIBUTES = [
	"shipment_job_id",
	"job_number",
	"storage_shipment_job_id",
	"warehouseId",
	"company_id",
	"email",
	"shipment_name",
	"pickup_date",
	"delivery_date",
	"pickup_address",
	"pickup_address2",
	"pickup_city",
	"pickup_state",
	"pickup_zipcode",
	"pickup_country",
	"delivery_address",
	"delivery_address2",
	"delivery_city",
	"delivery_state",
	"delivery_zipcode",
	"delivery_country",
	"warehouse",
	"notes",
	"contact_reference",
	"account_reference",
	"opportunity_reference",
	"move_coordinator",
	"wo_reference",
	"source",
	"external_reference",
	"external_reference_2",
	"external_reference_3",
	"created_at",
	"is_job_complete_flag"
];
global.COMMON_JOB_FORCED_SHIPMENT_ATTRIBUTES = [
	"forced_status_id",
	"current_stage_id",
	"altered_stage_id",
	"reason",
];
global.COMMON_JOB_FORCED_SHIPMENT_ATTRIBUTES2 = [
	"forced_status_id",
	"current_stage_id",
	"local_current_stage_id",
	"altered_stage_id",
	"local_altered_stage_id",
	"reason",
];
global.COMMON_JOB_TAG_ATTRIBUTES = ["tag_shipment_id", "tag_id", "shipment_id"];
global.COMMON_CUSTOMER_TAG_ATTRIBUTES = ["tag_customer_id", "tag_id", "customer_id"];
global.COMMON_ITEM_TAG_ATTRIBUTES = ["tag_inventory_id", "tag_id", "shipment_inventory_id"];
global.COMMON_TAG_ATTRIBUTES = ["tag_id", "name", "color", "tag_for", "company_id", "staff_id", "admin_id"];
global.BASIC_JOB_STAGES_ATTRIBUTES = [
	"shipment_stage_id",
	"name",
	"status",
	"why_supervisor_signature_require_note",
	"why_customer_signature_require_note",
	"supervisor_signature_require_at_origin_to_all_pages",
	"supervisor_signature_require_at_destination_to_all_pages",
	"customer_signature_require_at_origin_to_all_pages",
	"customer_signature_require_at_destination_to_all_pages",
];

global.BASIC_JOB_STAGES_ATTRIBUTES2 = [
	"local_shipment_stage_id",
	"local_shipment_type_id",
	"ref_shipment_stage_id",
	"shipment_job_id",
	"name",
	"status",
	"scan_require",
	"remove_scan_require",
	"scan_into_storage",
	"allow_default_manual_label",
	"add_items_to_inventory",
	"assign_storage_units_to_items",
	"unassign_storage_units_from_items",
	"remove_items_to_inventory",
	"enable_partial_complete_stage",
	"total_add_items_inventory_stage",
	"total_add_items_storage_stage",
	"total_remove_items_storage_stage",
	"total_remove_items_inventory_stage",
	"scan_out_of_storage",
	"supervisor_signature_require",
	"customer_signature_require",
	"is_add_item",
	"is_add_exceptions",
	["created_at", "date"],
	"why_supervisor_signature_require_note",
	"why_customer_signature_require_note",
	"supervisor_signature_require_at_origin_to_all_pages",
	"supervisor_signature_require_at_destination_to_all_pages",
	"customer_signature_require_at_origin_to_all_pages",
	"customer_signature_require_at_destination_to_all_pages",
	"total_add_items_to_inventory_scan",
	"total_remove_items_to_inventory_scan"
];
global.COMMON_JOB_SIGNATURE_ATTRIBUTES = [
	"shipment_job_signature_id",
	"customer_signature",
	"supervisor_signature",
];
// Qr module
global.QR_SUCCESS = "QR code created successfully.";
global.QR_USED = "This QR code is already assigned to a shipment.";
global.BATCH_QR_ALREADY_USED = "Cannot delete this batch as there are batch lables assigned to shipments!";
global.GENERIC_REQUEST_SUCCESS = "Request Handled Successfully.";

global.BATCH_DELETE = "Batch deleted successfully.";

global.BATCH_REQUEST_SUCCESS = "Request Handled Successfully.";
global.GENERIC_REQUEST_FAILURE = "Error Handling Request.";
global.INCORRECT_LENGTH_PDF_HEADER = "length accepted for header is 24 characters.";
global.QR_DELETED = "QR code deleted successfully.";
global.QR_EMPTY = "No QR available on this Job.";
global.QR_NOT_AVAILABLE = "This QR is not available.";
global.SEQUENCE_NUMBER_NOT_AVAILABLE = "Sequence number already exists, please try with another!.";
global.QR_UPLOAD_PATH = "mover-inventory/qrImage";
global.LABEL_UPLOAD_PATH = "mover-inventory/genericLabelImage";


// Email Constant
global.Const_Email = process.env.EMAIL_ID;
global.Const_Email_id = process.env.EMAIL_ID;
global.Const_Email_Password = process.env.EMAIL_PASSWORD;
global.Const_Email_Host = "smtp.gmail.com";
global.Const_Email_Port = "465"; //'465'

global.Const_Admin_Email = process.env.EMAIL_ID;

global.Support_Email = process.env.EMAIL_ID;
global.cc_support_email = process.env.EMAIL_ID;

global.Const_Report_Email = process.env.EMAIL_ID;
global.Const_Report_Email_CC = process.env.EMAIL_ID;

global.RESET_PASSWORD_LINK = process.env.CMS_URL + "reset-password/";
global.RESET_CUSTOMER_PASSWORD_LINK = process.env.CMS_URL + "customer-portal/change_password";
// global.RESET_CUSTOMER_PASSWORD_LINK =
// 	"http://localhost:3000/mover-inventory-cms/customer-portal/change_password";

global.CUSTOMER_PORTAL_LINK = process.env.CMS_URL + "customer-portal/sign_in";
global.CMS_URL_LINK = process.env.CMS_URL


// JWT constant
global.JWTPRIVATEKEY = "MoverInventoryJwtPrivateKey";

// reset password
global.TOKEN_VERIFIED = "OTP verified successfully.";
global.TOKEN_VERIFICATION_ERROR = "OTP does not match, please try again!.";
global.PASSWORD_RESET_SUCCESS = "Password reset successfully.";
global.PASSWORD_RESET_ERROR = "Error in password reset. Please try again!.";

global.TEMPLATE_MODULE_TYPES = {
	EMAIL: {
		EMAIL_VERIFICATION: "EMAIL_VERIFICATION",
		CONTACT: "CONTACT",
		FORGOT_PASSWORD: "FORGOT_PASSWORD",
	},
	NOTIFICATION: {
		LOCAL_CHALLENGE: "LOCAL_CHALLENGE",
	},
};
global.TEMPLATE_TYPES = {
	EMAIL: "EMAIL",
	NOTIFICATION: "NOTIFICATION",
};

global.STAFF_TYPES = {
	WORKER: "WORKER",
	ADMIN: "ADMIN",
};

//JSREPORT INFO
global.JSREPORT_URL = process.env.JSREPORT_URL;
global.JSREPORT_ADMIN = process.env.JSREPORT_ADMIN;
global.JSREPORT_PASSWORD = process.env.JSREPORT_PASSWORD;
global.MOVER_STORAGE_API_URL = process.env.MOVER_STORAGE_API_URL
global.MOVEGISTICS_STORAGE_API_URL = process.env.MOVEGISTICS_STORAGE_API_URL
global.GOOGLE_API_KEY = process.env.GOOGLE_API_KEY
