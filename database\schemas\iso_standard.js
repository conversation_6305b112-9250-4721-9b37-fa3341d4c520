'use strict';
module.exports = (sequelize, DataTypes) => {
  const iso_Standard = sequelize.define('iso_Standard', {
    ISO_ID: DataTypes.INTEGER,
    ISO_FULL_ID: DataTypes.INTEGER,
    Standard_name: DataTypes.STRING,
    Standard_Full_Name: DataTypes.STRING,
    Annex: DataTypes.STRING,
    level: DataTypes.INTEGER,
    SearchKey: DataTypes.STRING,
    type: DataTypes.STRING,
    created_at: {
      type: DataTypes.DATE,
    },
    updated_at: {
      type: DataTypes.DATE,
    },
  }, {});
  iso_Standard.associate = function (models) {
    // associations can be defined here
  };
  return iso_Standard;
};