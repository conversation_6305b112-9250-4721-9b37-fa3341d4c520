const groupModel = require("../../models/Admin/groupModel");
const commonFunction = require("../../assets/common");
const commonModel = require("../../models/Admin/commonModel");

exports.isCheckGroupNameExistsController = async (request, response, next) => {
	try {
		const isCheckGroupNameExists = await groupModel.isCheckGroupNameExistsModel(request.body);
		if (!isCheckGroupNameExists) {
			next();
		} else
			commonFunction.generateResponse(
				response,
				NOT_FOUND_CODE,
				0,
				"Group already exists with this name.",
				{}
			);
	} catch (error) {
		console.log("exports.isValidGroupNameController -> error: ", error);
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.createGroupController = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		const groupDetails = await groupModel.createGroupModel(request.body, getUserDetails)
		if (groupDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				GROUP_SUG_ADDED_SUCCESS,
				groupDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				GROUP_SUG_ADDED_FAILURE,
				{}
			);
	}
	catch (reason) {
		console.log("exports.createGroupController -> error: ", reason);
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};


exports.getGroupListingController = async (request, response) => {
	try {
		request.query.search = request.query.search ? request.query.search : "";
		let getUserDetails = await commonModel.getUserDetails(request);
		const groupListing = await groupModel.getGroupListingModel(request.query, getUserDetails)
		if (groupListing)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				GROUP_SUG_RETRIEVED_SUCCESS,
				groupListing
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				GROUP_SUG_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};

exports.getGroupUserListingController = async (request, response) => {
	try {
		const { groupId } = request.params;
		request.query.search = request.query.search ? request.query.search : "";
		let getUserDetails = await commonModel.getUserDetails(request);
		const groupListing = await groupModel.getGroupUserListingModel(groupId, request.query, getUserDetails)
		if (groupListing)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				GROUP_SUG_RETRIEVED_SUCCESS,
				groupListing
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				GROUP_SUG_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};




exports.isValidGroupController = async (request, response, next) => {
	try {
		const groupId = request.params.groupId;

		const isValidGroup = await groupModel.checkGroupExistenceModel(groupId);
		if (isValidGroup) {
			next();
		} else
			commonFunction.generateResponse(
				response,
				NOT_FOUND_CODE,
				0,
				GROUP_SUG_NOT_FOUND,
				{}
			);
	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};


exports.isValidGroupUserController = async (request, response, next) => {
	try {
		const groupUserId = request.params.groupUserId;
		const isValidGroupUser = await groupModel.checkGroupUserExistenceModel(groupUserId);
		if (isValidGroupUser) {
			next();
		} else
			commonFunction.generateResponse(
				response,
				NOT_FOUND_CODE,
				0,
				"Group user not found.",
				{}
			);
	} catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.isGroupAssignController = async (request, response, next) => {
	try {
		const { groupId } = request.params;
		const data = await groupModel.isGroupAssignModel(groupId)
		if (data && data.count > 0) {
			commonFunction.generateResponse(
				response,
				EXPECTATION_FAILED_CODE,
				0,
				GROUP_DELETE_ASSIGN_COMPANY,
				{}
			);
		}
		else {
			next();
		}

	}
	catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
}


exports.removeGroupController = async (request, response) => {
	try {
		const { groupId } = request.params;
		const data = await groupModel.removeGroupModel(groupId)
		if (data) {
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				GROUP_SUG_DELETE_SUCCESS,
				{}
			);
		}
	}
	catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.removeGroupUserController = async (request, response) => {
	try {
		const { groupUserId } = request.params;
		const data = await groupModel.removeGroupUserModel(groupUserId)
		if (data) {
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				GROUP_SUG_USER_DELETE_SUCCESS,
				{}
			);
		}
	}
	catch (error) {
		commonFunction.generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};



exports.viewGroupController = async (request, response) => {
	try {
		const { groupId } = request.params;
		const groupDetails = await groupModel.getGroupModel(groupId)
		if (groupDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				GROUP_SUG_DETAILS,
				groupDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				GROUP_SUG_NOT_FOUND,
				{}
			);
	}
	catch (reason) {
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};



exports.editGroupController = async (request, response) => {
	try {
		const { groupId } = request.params;
		const groupDetails = await groupModel.editGroupModel(groupId, request.body)
		if (groupDetails)
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				1,
				GROUP_SUG_EDIT_SUCCESS,
				groupDetails
			);
		else
			commonFunction.generateResponse(
				response,
				SUCCESS_CODE,
				0,
				GROUP_SUG_EDIT_FAILURE,
				{}
			);
	}
	catch (reason) {
		commonFunction.generateResponse(
			response,
			SERVER_ERROR_CODE,
			0,
			reason,
			{}
		)
	}
};