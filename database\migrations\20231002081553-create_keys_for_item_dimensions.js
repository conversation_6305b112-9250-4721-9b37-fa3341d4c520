"use strict";

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'shipment_inventories',
        'is_add_dimension',
        {
          type: Sequelize.TINYINT,
          allowNull: true,
          defaultValue: 0,
        },
        {
          logging: true,
        }
      );
      await queryInterface.addColumn(
        "shipment_inventories",
        "length",
        {
          type: Sequelize.DECIMAL(11, 2),
          allowNull: true,
          defaultValue: 0
        }
      );
      await queryInterface.addColumn(
        "shipment_inventories",
        "width",
        {
          type: Sequelize.DECIMAL(11, 2),
          allowNull: true,
          defaultValue: 0
        }
      );
      await queryInterface.addColumn(
        "shipment_inventories",
        "height",
        {
          type: Sequelize.DECIMAL(11, 2),
          allowNull: true,
          defaultValue: 0
        }
      );
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn("shipment_inventories", "is_add_dimension");
      await queryInterface.removeColumn("shipment_inventories", "length");
      await queryInterface.removeColumn("shipment_inventories", "width");
      await queryInterface.removeColumn("shipment_inventories", "height");
      return Promise.resolve();
    } catch (e) {
      return Promise.reject(e);
    }
  },
};
