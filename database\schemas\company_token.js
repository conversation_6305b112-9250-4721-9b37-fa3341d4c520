"use strict";
module.exports = (sequelize, DataTypes) => {
  const company_token = sequelize.define(
    "company_token",
    {
      company_key_id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      company_id: DataTypes.INTEGER,
      company_key: DataTypes.STRING,
      company_identity: DataTypes.STRING,
      isEnable: { type: DataTypes.TINYINT, defaultValue: 1 },
      created_at: {
        type: DataTypes.DATE,
        // defaultValue: Date.now()
      },
      updated_at: {
        type: DataTypes.DATE,
        // defaultValue: Date.now()
      },
    },
    { createdAt: false, updatedAt: false }
  );
  company_token.associate = function (models) {
    // associations can be defined here
    company_token.belongsTo(models.company, {
			as: "job_company",
			foreignKey: "company_id",
		});

  };
  return company_token;
};
