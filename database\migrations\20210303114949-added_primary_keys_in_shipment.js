'use strict';

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.addColumn(
				'tag_customer',
				'tag_customer_id',
				{
					allowNull: false,
					autoIncrement: true,
					primaryKey: true,
					type: Sequelize.INTEGER,
				}
			);
			await queryInterface.addColumn(
				'tag_shipment',
				'tag_shipment_id',
				{
					allowNull: false,
					autoIncrement: true,
					primaryKey: true,
					type: Sequelize.INTEGER,
				}
			);
			return Promise.resolve()

		} catch (e) {
			return Promise.reject(e);
		}
	},
	down: async (queryInterface, Sequelize) => {
		try {
			await queryInterface.removeColumn(
				'tag_shipment',
				'tag_shipment_id',
			);
			await queryInterface.removeColumn(
				'tag_customer',
				'tag_customer_id',
			);
			return Promise.resolve()

		} catch (e) {
			return Promise.reject(e);
		}
	}
};
