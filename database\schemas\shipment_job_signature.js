"use strict";
module.exports = (sequelize, DataTypes) => {
	const shipment_job_signature = sequelize.define(
		"shipment_job_signature",
		{
			shipment_job_signature_id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			shipment_job_id: DataTypes.INTEGER,
			staff_id: DataTypes.INTEGER,
			customer_signature: {
				type: DataTypes.TEXT,
			},
			customer_name:DataTypes.STRING,
			supervisor_signature: {
				type: DataTypes.TEXT,
			},
			supervisor_name: DataTypes.STRING,
			stage: DataTypes.INTEGER,
			local_stage: DataTypes.INTEGER,
			supervisor_signature_require_note_by_user: DataTypes.STRING,
			customer_signature_require_note_by_user: DataTypes.STRING,
			created_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
			updated_at: {
				type: DataTypes.DATE,
				// defaultValue: Date.now()
			},
		},
		{ createdAt: false, updatedAt: false }
	);
	shipment_job_signature.associate = function (models) {
		// associations can be defined here
		shipment_job_signature.belongsTo(models.shipment_job, {
			foreignKey: "shipment_job_id",
		});

		shipment_job_signature.belongsTo(models.staff, {
			foreignKey: "staff_id",
		});

		shipment_job_signature.belongsTo(models.shipment_type_stage, {
			foreignKey: "stage",
		});

		shipment_job_signature.belongsTo(models.shipment_type_stage_for_shipment, {
			foreignKey: "local_stage",
		});

	};
	return shipment_job_signature;
};
