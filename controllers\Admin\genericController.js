const {
	generateResponse,
	randomString,
	deleteImageS3,
	UploadImageS3,
} = require("../../assets/common");
const axios = require("axios");
const AWS = require("aws-sdk");
const fs = require("fs");
const genericLabelModel = require("../../models/Admin/genericLabelModel");
const commonModel = require("../../models/Admin/commonModel");
const inventoryModel = require("../../models/Admin/inventoryModel");
const StaffModel = require("../../models/Admin/staffModel");
const companyModel = require("../../models/Admin/companyModel");


const QRCode = require("qrcode");
const { PDFDocument, StandardFonts } = require("pdf-lib");
const charsetOptions = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
const s3 = new AWS.S3();
const qrCodeModelInstance = require("../../models/Admin/qrCodeModel");



exports.printDymoLabelController = async (request, response, next) => {
	try {
		const qrCodeId = request.params.qrCodeId;
		const getLabelDetailsDymo = await genericLabelModel.getLabelDetailsDymo(qrCodeId);
		generateResponse(response, SUCCESS_CODE, 1, BATCH_REQUEST_SUCCESS, {});

	} catch (error) {
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
}

exports.checkCompanyExistence = async (request, response, next) => {
	try {
		const companyId = request.body.companyId ? request.body.companyId : request.params.companyId;
		if (await genericLabelModel.checkCompanyExistenceModel(companyId)) {
			next();
		} else {
			generateResponse(response, NOT_FOUND_CODE, 0, COMPANY_NOT_FOUND, {});
		}
	} catch (error) {
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};

exports.listLabelController = async (request, response) => {
	try {
		const { batchId } = request.params;
		let getUserDetails = await commonModel.getUserDetails(request);
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);
		const companyId = getUserDetails.company_id ? getUserDetails.company_id : getStaffDetails.company_id
		const labelList = await genericLabelModel.listLabelModel(companyId, batchId, request.query);
		generateResponse(response, SUCCESS_CODE, 1, GENERIC_REQUEST_SUCCESS, labelList);
	} catch (error) {
		console.log("exports.listLabelController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};

exports.checkAssignToItems = async (request, response, next) => {
	try {
		const { batchId } = request.params;
		let getUserDetails = await commonModel.getUserDetails(request);
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);
		const companyId = getUserDetails.company_id ? getUserDetails.company_id : getStaffDetails.company_id
		const labelList = await genericLabelModel.checkAssignToItemsQR(companyId, batchId);
		let isLableAssignToShipment = false;

		for (const newData of labelList) {
			if (newData.job_id !== null && newData.job_id !== undefined && newData.job_id !== "") {
				isLableAssignToShipment = true;
				break;
			}
		}
		if (isLableAssignToShipment) {
			generateResponse(response, SUCCESS_CODE, 0, BATCH_QR_ALREADY_USED, {});
		}
		else {
			next();
		}
	}
	catch (error) {
		console.log("exports.checkAssignToItems -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
}

exports.deleteBatchController = async (request, response) => {
	try {
		const { batchId } = request.params;
		const qrDelete = await genericLabelModel.deleteAllQrOfBatchModel(batchId);
		const data = await genericLabelModel.batchDeleteModel(batchId);

		generateResponse(response, SUCCESS_CODE, 1, BATCH_DELETE, {});
	} catch (error) {
		console.log("exports.deleteBatchController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};


exports.listBatchController = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);
		const companyId = getUserDetails.company_id ? getUserDetails.company_id : getStaffDetails.company_id
		const batchList = await genericLabelModel.listBatchModel(companyId, request.query);
		generateResponse(response, SUCCESS_CODE, 1, BATCH_REQUEST_SUCCESS, batchList);
	} catch (error) {
		console.log("exports.listBatchController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};

exports.findCompanyLastGenericLabelController = async (request, response) => {
	try {
		let getUserDetails = await commonModel.getUserDetails(request);
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);
		const companyId = getUserDetails.company_id ? getUserDetails.company_id : getStaffDetails.company_id
		const findCompanyLastGenericLabel = await genericLabelModel.findCompanyLastGenericLabel(companyId)
		generateResponse(response, SUCCESS_CODE, 1, BATCH_REQUEST_SUCCESS, findCompanyLastGenericLabel);
	} catch (error) {
		console.log("exports.findCompanyLastGenericLabelController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};



exports.deleteQrLabelController = async (request, response) => {
	try {
		const { qrCodeId } = request.params;
		//newChanges
		const modelResponse = await genericLabelModel.getQrLabelModel(qrCodeId);
		if (modelResponse) {
			try {
				const bucketResponse = await removeQrLabel(modelResponse);
				if (bucketResponse) {
					const removeModelResponse = await genericLabelModel.removeQrLabelModel(qrCodeId);
					if (removeModelResponse) {
						generateResponse(response, NO_CONTENT_CODE, 1, QR_DELETED, removeModelResponse)
					}
				}
				else generateResponse(response, GENERIC_REQUEST_FAILURE, 0, bucketResponse, {});
			}
			catch (err) {
				generateResponse(response, SERVER_ERROR_CODE, 0, err, {})
			}
		} else {
			generateResponse(response, NO_CONTENT_CODE, 0, QR_NOT_AVAILABLE, {});
		}
	}
	catch (err) {
		generateResponse(response, SERVER_ERROR_CODE, 0, err, {})
	}
}


// newChangesQRBulckUpload
exports.generateLabelController = async (request, response) => {
	try {
		const formatDate = () => {
			let d = new Date();
			let month = (d.getMonth() + 1).toString();
			let day = d.getDate().toString();
			let year = d.getFullYear();
			if (month.length < 2) {
				month = '0' + month;
			}
			if (day.length < 2) {
				day = '0' + day;
			}
			return [year, month, day].join('-');
		}

		let formatDates = formatDate();
		let getUserDetails = await commonModel.getUserDetails(request);
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);
		const companyId = getUserDetails.company_id ? getUserDetails.company_id : getStaffDetails.company_id
		const { quantity, fromNumber } = request.body;
		let qrCodeBulk = [];
		// let { label_number } = await genericLabelModel.findLastLabel(companyId);
		let checkLabelNumberAvailable = await genericLabelModel.checkLabelNumberAvailable(companyId, request.body);
		if (checkLabelNumberAvailable) {
			generateResponse(response, NOT_VALID_DATA_CODE, 0, SEQUENCE_NUMBER_NOT_AVAILABLE, {});
		}
		else {
			let { batch_number } = await genericLabelModel.findLastBatch(companyId, formatDates);
			let createBatch = await genericLabelModel.createBatch(companyId, formatDates, batch_number);

			let loopIndex,
				loopJ,
				chunkValue = 10,
				qrCodeValues = [];

			for (loopIndex = 0, loopJ = quantity; loopIndex < loopJ; loopIndex += chunkValue) {
				const remainingQuantity = loopJ - loopIndex;
				const subLoopValue = remainingQuantity < chunkValue ? remainingQuantity : chunkValue;


				const qrCodeBulk = []; // Move this declaration inside the loop for each chunk

				for (let index = 0; index < subLoopValue; index++) {
					const qrString = randomString(10, charsetOptions);
					const qr_number = fromNumber + loopIndex + index;
					const qrImageFileName = qrString + `_` + companyId + `.png`;
					const qrFilePath = `/tmp/qrCodes/${companyId}/${qrImageFileName}`;
					const serverUploadPath = `${QR_UPLOAD_PATH}/${companyId}/original/${qrImageFileName}`;
					const baseFolderPath = `/tmp/qrCodes/${companyId}`;

					if (!fs.existsSync(baseFolderPath)) fs.mkdirSync(`/tmp/qrCodes/${companyId}`, { recursive: true });
					await QRCode.toFile(`${qrFilePath}`, qrString).catch((err) => console.log(err));

					let bucketObject = {
						ACL: "public-read",
						Bucket: Const_AWS_BUCKET,
						Body: fs.createReadStream(qrFilePath),
						Key: `${serverUploadPath}`,
					};

					await UploadImageS3(bucketObject, s3);

					qrCodeBulk.push({
						random_number: qrString,
						qr_image: serverUploadPath,
						company_id: companyId,
						batch_id: createBatch.batch_id,
						label_number: qr_number,
						type: 'Generic',
						job_id: null,
					});
				}

				await genericLabelModel.generateQrModel(qrCodeBulk);
			}
			generateResponse(response, CREATED_CODE, 1, "QR/Label created!", {});

		}

	} catch (error) {
		console.log("exports.generateLabelController -> error: ", error);
		generateResponse(response, SERVER_ERROR_CODE, 0, error.message, {});
	}
};


const removeQrLabel = async (modelResponse) => {
	if (modelResponse.qr_image) {
		let delMedia = {
			Bucket: Const_AWS_BUCKET,
			Key: modelResponse.qr_image,
		};
		return deleteImageS3(delMedia, s3);
	} else {
		return false;
	}
};

const generateAndUploadLabel = async (qrCodeString, companyId) => {
	let qrImageFileName = qrCodeString + `_` + companyId + `.png`;
	let qrFilePath = `/tmp/qrCodes/${companyId}/${qrImageFileName}`;
	let serverUploadPath = `${QR_UPLOAD_PATH}/${companyId}/original/${qrImageFileName}`;

	let baseFolderPath = `/tmp/qrCodes/${companyId}`;
	if (!fs.existsSync(baseFolderPath)) fs.mkdirSync(`/tmp/qrCodes/${companyId}`, { recursive: true });

	await QRCode.toFile(`${qrFilePath}`, qrCodeString).catch((err) => console.log(err));

	// Moving photos to s3 bucket
	let bucketObject = {
		ACL: "public-read",
		Bucket: Const_AWS_BUCKET,
		Body: fs.createReadStream(qrFilePath),
		Key: `${serverUploadPath}`,
	};

	// uploading and removing files from temp folder
	let result = await UploadImageS3(bucketObject, s3);
	if (result) {
		return serverUploadPath;
	}
};



exports.printLabelController = async (request, response) => {
	let { jobId, batchId } = request.params;
	let {
		title_flag,
		qr_code_label_flag,
		sequenced_label_flag,
	} = request.body;
	let pdfDoc = await PDFDocument.create();
	let HelveticaBold = await pdfDoc.embedFont(StandardFonts.TimesRomanBold);
	let page = pdfDoc.addPage();
	let rowShiftBy = 10;
	let columnShiftBy = 0;
	let howBroad = 0;
	let qrPerRow = 2;
	let qrPerColumn = 6;
	let isHeightLimit = 0;

	const qrCodeStrings = await genericLabelModel.listGenericQrCodeLinksModel(jobId, batchId);
	const fetchCompanyNameForGenericLable = await companyModel.fetchCompanyNameForGenericLable(jobId);
	let place = fetchCompanyNameForGenericLable.company_name
	// const newArray = qrCodeStrings.flatMap(i => [i, i]);

	for (let [isWidthLimit, qrCodeContent] of qrCodeStrings.entries()) {
		let qrStatus = new Promise(async (resolve, reject) => {

			let qrImageFileName = qrCodeContent.random_number + `_` + jobId + `.png`;
			let qrFilePath = `/tmp/qrCodes/${jobId}/${qrImageFileName}`;

			if (fs.existsSync(qrFilePath)) {
				let responseQR = fs.readFileSync(qrFilePath);
				resolve(responseQR);
			} else {
				let image = await axios.default(Const_AWS_BASE + qrCodeContent.qr_image, {
					responseType: "arraybuffer",
				});
				resolve(image.data);
			}
		});
		//newChanges
		const qrContent = await qrStatus;
		if (qrContent) {
			const pngImage = await pdfDoc.embedPng(qrContent);
			const pngDims = pngImage.scale(0.68);
			const baseMargin = 10;

			if (isWidthLimit % qrPerRow === 0) {
				isHeightLimit += 1;
				rowShiftBy += 6.25 + page.getHeight() / qrPerColumn;
				columnShiftBy = 1 + page.getWidth() / qrPerRow + 0.15;
				howBroad = -0.03;
			} else {
				howBroad += 0.97;
			}
			if (isHeightLimit === qrPerColumn) {
				page = pdfDoc.addPage();
				rowShiftBy = page.getHeight() / qrPerColumn + 10;
				isHeightLimit = 1;
				howBroad = 0;
			}
			let xPosition = baseMargin + page.getWidth() / 20 + howBroad * columnShiftBy;
			let yPosition = page.getHeight() - rowShiftBy - 10;

			const jobDetails = qrCodeContent.shipment_job;
			const companyDetails = jobDetails.job_company ? jobDetails.job_company : false;
			const randomNumber = qrCodeContent.random_number;
			const labelNumber = qrCodeContent.label_number;

			const jobInfo = `Job: ${jobDetails.job_number}`;
			place = `${place ? place : "Final Mile, Paso"}`;
			const companyName = `${companyDetails ? companyDetails.company_name : "Mover Inventory"}`;
			const contactNumber = `${companyDetails.phone ? companyDetails.phone : "1234567890"}`;


			const fromAddress = `From: ${jobDetails.pickup_address &&
				jobDetails.pickup_address !== "undefined" &&
				jobDetails.pickup_address !== "null"
				? jobDetails.pickup_address
				: ""
				} ${jobDetails.pickup_address2 &&
					jobDetails.pickup_address2 !== "undefined" &&
					jobDetails.pickup_address2 !== "null"
					? jobDetails.pickup_address2
					: ""
				}`;
			const fromAddress1 = `${jobDetails.pickup_city &&
				jobDetails.pickup_city !== "undefined" &&
				jobDetails.pickup_city !== "null"
				? jobDetails.pickup_city
				: ""
				} ${jobDetails.pickup_state &&
					jobDetails.pickup_state !== "undefined" &&
					jobDetails.pickup_state !== "null"
					? jobDetails.pickup_state
					: ""
				}${jobDetails.pickup_zipcode &&
					jobDetails.pickup_zipcode !== "undefined" &&
					jobDetails.pickup_zipcode !== "null"
					? ", " + jobDetails.pickup_zipcode
					: ""
				}`;
			const fromAddress2 = `${jobDetails.pickup_country &&
				jobDetails.pickup_country !== "undefined" &&
				jobDetails.pickup_country !== "null"
				? jobDetails.pickup_country
				: ""
				}`;

			const toAddress = `To: ${jobDetails.delivery_address &&
				jobDetails.delivery_address !== "undefined" &&
				jobDetails.delivery_address !== "null"
				? jobDetails.delivery_address
				: ""
				} ${jobDetails.delivery_address2 &&
					jobDetails.delivery_address2 !== "undefined" &&
					jobDetails.delivery_address2 !== "null"
					? jobDetails.delivery_address2
					: ""
				}`;
			const toAddress1 = `${jobDetails.delivery_city &&
				jobDetails.delivery_city !== "undefined" &&
				jobDetails.delivery_city !== "null"
				? jobDetails.delivery_city
				: ""
				} ${jobDetails.delivery_state &&
					jobDetails.delivery_state !== "undefined" &&
					jobDetails.delivery_state !== "null"
					? jobDetails.delivery_state
					: ""
				}${jobDetails.delivery_zipcode &&
					jobDetails.delivery_zipcode !== "undefined" &&
					jobDetails.delivery_zipcode !== "null"
					? ", " + jobDetails.delivery_zipcode
					: ""
				}`;
			const toAddress2 = `${jobDetails.delivery_country &&
				jobDetails.delivery_country !== "undefined" &&
				jobDetails.delivery_country !== "null"
				? jobDetails.delivery_country
				: ""
				}`;

			const smallTextSize = 10;
			const bigTextSize = 12;
			const sideMomentQR = 80;
			let labelPosition = 65;

			const labelMeta = () => {
				const _ = {
					x: xPosition + sideMomentQR,
					y: yPosition + labelPosition,
					size: smallTextSize,
				};
				labelPosition -= 10.6;
				return _;
			};

			if (place.length <= 40) {
				if (title_flag)
					page.drawText(place, {
						x: xPosition + 10,
						y: yPosition + 81,
						size: bigTextSize - (place.length < 32 ? 0 : bigTextSize / place.length),
						font: HelveticaBold,
					});
			} else {
				const newWord = chunkSubstr(place, 40);

				function chunkSubstr(str, size) {
					const numChunks = Math.ceil(str.length / size);
					const chunks = new Array(numChunks);

					for (let i = 0, o = 0; i < numChunks; ++i, o += size) {
						chunks[i] = str.substr(o, size);
					}

					return chunks;
				}
				if (title_flag)
					page.drawText(newWord[1], {
						x: xPosition + 10,
						y: yPosition + 81,
						size: bigTextSize - (newWord[1].length < 32 ? 0 : bigTextSize / newWord[1].length),
						font: HelveticaBold,
					});
				if (title_flag)
					page.drawText(newWord[0], {
						x: xPosition + 10,
						y: yPosition + 93,
						size: bigTextSize - (newWord[0].length < 32 ? 0 : bigTextSize / newWord[0].length),
						font: HelveticaBold,
					});
			}


			page.drawImage(pngImage, {
				x: xPosition,
				y: yPosition,
				...pngDims,
			});


			if (qr_code_label_flag)
				page.drawText(randomNumber, {
					x: xPosition + 2,
					y: yPosition - 15,
					size: 12,
					font: HelveticaBold,
				});

			if (sequenced_label_flag)
				page.drawText(labelNumber, {
					x: xPosition + 20,
					y: yPosition,
					size: smallTextSize,
					font: HelveticaBold,
				});

		}
	}
	try {
		//newChanges
		const result = await pdfDoc.save()
		let baseFolderPath = `/tmp/qrPdf/${jobId}`;

		let qrPdfPath = `${baseFolderPath}/tempFile.pdf`;
		if (!fs.existsSync(baseFolderPath)) fs.mkdirSync(`${baseFolderPath}`, { recursive: true });

		if (result) {
			fs.writeFileSync(qrPdfPath, result);

			fs.readFile(qrPdfPath, (err, fileData) => {
				if (!err) {
					response.status(CREATED_CODE);
					response.contentType("application/pdf");
					response.send(fileData);
					console.log("pdfSaved");
					return;
				} else {
					generateResponse(response, SERVER_ERROR_CODE, 0, GENERIC_REQUEST_FAILURE, {});
				}
			});
		} else {
			generateResponse(response, NO_CONTENT_CODE, 0, GENERIC_REQUEST_FAILURE, {});
		}

	} catch (error) {
		console.log("exports.printLabelController -> error: ", error);

		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};

exports.printSingalLabelController = async (request, response) => {

	let { jobId } = request.params;
	let {
		title_flag,
		qr_code_label_flag,
		sequenced_label_flag,
		qrId
	} = request.body;
	let pdfDoc = await PDFDocument.create();
	let HelveticaBold = await pdfDoc.embedFont(StandardFonts.TimesRomanBold);
	let page = pdfDoc.addPage();
	let rowShiftBy = 10;
	let columnShiftBy = 0;
	let howBroad = 0;
	let qrPerRow = 2;
	let qrPerColumn = 6;
	let isHeightLimit = 0;

	const qrCodeStrings = await genericLabelModel.listSingalGenericQrCodeLinksModel(jobId, qrId);
	const fetchCompanyNameForGenericLable = await companyModel.fetchCompanyNameForGenericLable(jobId);
	let place = fetchCompanyNameForGenericLable.company_name

	// const newArray = qrCodeStrings.flatMap(i => [i, i]);

	for (let [isWidthLimit, qrCodeContent] of qrCodeStrings.entries()) {
		let qrStatus = new Promise(async (resolve, reject) => {

			let qrImageFileName = qrCodeContent.random_number + `_` + jobId + `.png`;
			let qrFilePath = `/tmp/qrCodes/${jobId}/${qrImageFileName}`;

			if (fs.existsSync(qrFilePath)) {
				let responseQR = fs.readFileSync(qrFilePath);
				resolve(responseQR);
			} else {
				let image = await axios.default(Const_AWS_BASE + qrCodeContent.qr_image, {
					responseType: "arraybuffer",
				});
				resolve(image.data);
			}
		});
		//newChanges
		const qrContent = await qrStatus;
		if (qrContent) {
			const pngImage = await pdfDoc.embedPng(qrContent);
			const pngDims = pngImage.scale(0.68);
			const baseMargin = 10;

			if (isWidthLimit % qrPerRow === 0) {
				isHeightLimit += 1;
				rowShiftBy += 6.25 + page.getHeight() / qrPerColumn;
				columnShiftBy = 1 + page.getWidth() / qrPerRow + 0.15;
				howBroad = -0.03;
			} else {
				howBroad += 0.97;
			}
			if (isHeightLimit === qrPerColumn) {
				page = pdfDoc.addPage();
				rowShiftBy = page.getHeight() / qrPerColumn + 10;
				isHeightLimit = 1;
				howBroad = 0;
			}
			let xPosition = baseMargin + page.getWidth() / 20 + howBroad * columnShiftBy;
			let yPosition = page.getHeight() - rowShiftBy - 10;

			const jobDetails = qrCodeContent.shipment_job;
			const companyDetails = jobDetails.job_company ? jobDetails.job_company : false;
			const randomNumber = qrCodeContent.random_number;
			const labelNumber = qrCodeContent.label_number;

			const jobInfo = `Job: ${jobDetails.job_number}`;
			place = `${place ? place : "Final Mile, Paso"}`;
			const companyName = `${companyDetails ? companyDetails.company_name : "Mover Inventory"}`;
			const contactNumber = `${companyDetails.phone ? companyDetails.phone : "1234567890"}`;


			const fromAddress = `From: ${jobDetails.pickup_address &&
				jobDetails.pickup_address !== "undefined" &&
				jobDetails.pickup_address !== "null"
				? jobDetails.pickup_address
				: ""
				} ${jobDetails.pickup_address2 &&
					jobDetails.pickup_address2 !== "undefined" &&
					jobDetails.pickup_address2 !== "null"
					? jobDetails.pickup_address2
					: ""
				}`;
			const fromAddress1 = `${jobDetails.pickup_city &&
				jobDetails.pickup_city !== "undefined" &&
				jobDetails.pickup_city !== "null"
				? jobDetails.pickup_city
				: ""
				} ${jobDetails.pickup_state &&
					jobDetails.pickup_state !== "undefined" &&
					jobDetails.pickup_state !== "null"
					? jobDetails.pickup_state
					: ""
				}${jobDetails.pickup_zipcode &&
					jobDetails.pickup_zipcode !== "undefined" &&
					jobDetails.pickup_zipcode !== "null"
					? ", " + jobDetails.pickup_zipcode
					: ""
				}`;
			const fromAddress2 = `${jobDetails.pickup_country &&
				jobDetails.pickup_country !== "undefined" &&
				jobDetails.pickup_country !== "null"
				? jobDetails.pickup_country
				: ""
				}`;

			const toAddress = `To: ${jobDetails.delivery_address &&
				jobDetails.delivery_address !== "undefined" &&
				jobDetails.delivery_address !== "null"
				? jobDetails.delivery_address
				: ""
				} ${jobDetails.delivery_address2 &&
					jobDetails.delivery_address2 !== "undefined" &&
					jobDetails.delivery_address2 !== "null"
					? jobDetails.delivery_address2
					: ""
				}`;
			const toAddress1 = `${jobDetails.delivery_city &&
				jobDetails.delivery_city !== "undefined" &&
				jobDetails.delivery_city !== "null"
				? jobDetails.delivery_city
				: ""
				} ${jobDetails.delivery_state &&
					jobDetails.delivery_state !== "undefined" &&
					jobDetails.delivery_state !== "null"
					? jobDetails.delivery_state
					: ""
				}${jobDetails.delivery_zipcode &&
					jobDetails.delivery_zipcode !== "undefined" &&
					jobDetails.delivery_zipcode !== "null"
					? ", " + jobDetails.delivery_zipcode
					: ""
				}`;
			const toAddress2 = `${jobDetails.delivery_country &&
				jobDetails.delivery_country !== "undefined" &&
				jobDetails.delivery_country !== "null"
				? jobDetails.delivery_country
				: ""
				}`;

			const smallTextSize = 10;
			const bigTextSize = 12;
			const sideMomentQR = 80;
			let labelPosition = 65;

			const labelMeta = () => {
				const _ = {
					x: xPosition + sideMomentQR,
					y: yPosition + labelPosition,
					size: smallTextSize,
				};
				labelPosition -= 10.6;
				return _;
			};

			if (place.length <= 40) {
				if (title_flag)
					page.drawText(place, {
						x: xPosition + 10,
						y: yPosition + 81,
						size: bigTextSize - (place.length < 32 ? 0 : bigTextSize / place.length),
						font: HelveticaBold,
					});
			} else {
				const newWord = chunkSubstr(place, 40);

				function chunkSubstr(str, size) {
					const numChunks = Math.ceil(str.length / size);
					const chunks = new Array(numChunks);

					for (let i = 0, o = 0; i < numChunks; ++i, o += size) {
						chunks[i] = str.substr(o, size);
					}

					return chunks;
				}
				if (title_flag)
					page.drawText(newWord[1], {
						x: xPosition + 10,
						y: yPosition + 81,
						size: bigTextSize - (newWord[1].length < 32 ? 0 : bigTextSize / newWord[1].length),
						font: HelveticaBold,
					});
				if (title_flag)
					page.drawText(newWord[0], {
						x: xPosition + 10,
						y: yPosition + 93,
						size: bigTextSize - (newWord[0].length < 32 ? 0 : bigTextSize / newWord[0].length),
						font: HelveticaBold,
					});
			}


			page.drawImage(pngImage, {
				x: xPosition,
				y: yPosition,
				...pngDims,
			});

			if (qr_code_label_flag)
				page.drawText(randomNumber, {
					x: xPosition + 2,
					y: yPosition - 15,
					size: 12,
					font: HelveticaBold,
				});

			if (sequenced_label_flag)
				page.drawText(labelNumber, {
					x: xPosition + 20,
					y: yPosition,
					size: smallTextSize,
					font: HelveticaBold,
				});
		}
	}
	try {
		//newChanges
		const result = await pdfDoc.save()
		let baseFolderPath = `/tmp/qrPdf/${jobId}`;

		let qrPdfPath = `${baseFolderPath}/tempFile.pdf`;
		if (!fs.existsSync(baseFolderPath)) fs.mkdirSync(`${baseFolderPath}`, { recursive: true });

		if (result) {
			fs.writeFileSync(qrPdfPath, result);

			fs.readFile(qrPdfPath, (err, fileData) => {
				if (!err) {
					response.status(CREATED_CODE);
					response.contentType("application/pdf");
					response.send(fileData);
					return;
				} else {
					generateResponse(response, SERVER_ERROR_CODE, 0, GENERIC_REQUEST_FAILURE, {});
				}
			});
		} else {
			generateResponse(response, NO_CONTENT_CODE, 0, GENERIC_REQUEST_FAILURE, {});
		}

	} catch (error) {
		console.log("exports.printSingalLabelController -> error: ", error);

		generateResponse(response, SERVER_ERROR_CODE, 0, error, {});
	}
};