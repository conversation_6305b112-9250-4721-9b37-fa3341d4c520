const {
    shipment_room: room,
} = require("../../database/schemas");
const { Op, literal } = require("sequelize");

exports.createRoomModel = async (roomName, companyId) => {
    return room.create({
        name: roomName,
        admin_id: null,
        company_id: companyId,
        staff_id: null
    });
}


exports.getRoomListingModel = async (fieldsAndValues, body) => {
    return room.findAndCountAll({
        limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
        offset:
            fieldsAndValues.page_no > 1
                ? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
                : 0,
        where: {
            company_id: body.company_id,
            [Op.or]: [
                { shipment_room_id: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
                { name: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
            ],
            status: fieldsAndValues.filter ? fieldsAndValues.filter : "Active",
        },
        attributes: ["shipment_room_id", "name", "status", "company_id"],
        order: [
            [
                fieldsAndValues.order_by_fields
                    ? fieldsAndValues.order_by_fields
                    : "name",
                fieldsAndValues.order_sequence ? fieldsAndValues.order_sequence : "ASC",
            ],
        ],
    });
}
